/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadResourceScheduling.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadResourceScheduling.cpp $
 *         $Author: yening $
 *         $Revision: 507 $
 *         $Date: 2025-05-27 19:14:15 $
*******************************************************************************************/
#include "ThreadResourceScheduling.h"
#include<QThread>
#include <QDateTime>
#include <QDebug>

#include <cuda_runtime_api.h>
#include <nvml.h>


ThreadResourceScheduling::ThreadResourceScheduling(QObject *parent) : QObject(parent)
{
	SetMemPoolSrcFileName((char*)"ThreadResourceScheduling", sizeof("ThreadResourceScheduling"));
	_DefineMemPool(stTaskInfo, 10);
	_DefineMemPool(stCalcutePoolInfo, 10);
	_DefineMemPool(stTarClutterLink, 10);
	_DefineMemPool(ClutterRoeData, 10);

	_DefineMemPool(stSarEchoPara, 100);
	_DefineMemPool(stClutterEchoPara, 20);
	_DefineMemPool(stJamEchoPara, 20);
	_DefineMemPool(stRadarAPC, 100);

	_DefineMemPool(stFrameLink, 30);
	_DefineMemPool(stDYTExternPara, 30);

	_DefineMemPool(stResourceScheduingPara, 100);

	m_StFrameLinkHead = (stFrameLink *)_MallocMemObj(stFrameLink); m_StFrameLinkHead->init();
	m_stTarClutterLink = (stTarClutterLink*)_MallocMemObj(stTarClutterLink); m_stTarClutterLink->init();

    pClassRcsRead = new ClassRcsRead;
    pClassClutterRcsRead = new ClassClutterRcsRead;

	m_RestTaskNode = 0;

	m_GpuCount = 0;
	cudaGetDeviceCount(&m_GpuCount);//获取GPU卡个数
    m_GpuCount = 3;
	pThreadEchoDataFormat	= new ThreadEchoDataFormat;
	pThreadEchoDataSave		= new ThreadEchoDataSave;
	m_GPU_NO_Start = GPU_Dev_Select;
	for (int i = 0; i < m_GpuCount; i++)
	{
		int GPU_NO = i + m_GPU_NO_Start;
		pThreadClassGPUCalcute[i] = new ThreadClassGPUCalcute(GPU_NO);
		connect(pThreadClassGPUCalcute[i], SIGNAL(sendEchoScheduling(void*)), this, SLOT(slotRecvGPUEcho2(void*)), Qt::QueuedConnection);
		if (i == 0)connect(this, SIGNAL(sendTarEchoDateGen1(int, void*, void*)), pThreadClassGPUCalcute[i], SLOT(slotTarEchoDateGen(int, void*, void*)), Qt::QueuedConnection);
		if (i == 1)connect(this, SIGNAL(sendTarEchoDateGen2(int, void*, void*)), pThreadClassGPUCalcute[i], SLOT(slotTarEchoDateGen(int, void*, void*)), Qt::QueuedConnection);
		if (i == 2)connect(this, SIGNAL(sendTarEchoDateGen3(int, void*, void*)), pThreadClassGPUCalcute[i], SLOT(slotTarEchoDateGen(int, void*, void*)), Qt::QueuedConnection);
		if (i == 3)connect(this, SIGNAL(sendTarEchoDateGen4(int, void*, void*)), pThreadClassGPUCalcute[i], SLOT(slotTarEchoDateGen(int, void*, void*)), Qt::QueuedConnection);
		if (i == 4)connect(this, SIGNAL(sendTarEchoDateGen5(int, void*, void*)), pThreadClassGPUCalcute[i], SLOT(slotTarEchoDateGen(int, void*, void*)), Qt::QueuedConnection);
		if (i == 5)connect(this, SIGNAL(sendTarEchoDateGen6(int, void*, void*)), pThreadClassGPUCalcute[i], SLOT(slotTarEchoDateGen(int, void*, void*)), Qt::QueuedConnection);
		if (i == 6)connect(this, SIGNAL(sendTarEchoDateGen7(int, void*, void*)), pThreadClassGPUCalcute[i], SLOT(slotTarEchoDateGen(int, void*, void*)), Qt::QueuedConnection);
		if (i == 7)connect(this, SIGNAL(sendTarEchoDateGen8(int, void*, void*)), pThreadClassGPUCalcute[i], SLOT(slotTarEchoDateGen(int, void*, void*)), Qt::QueuedConnection);
	}
	connect(this, SIGNAL(sendEchoDataFormat(void*)), pThreadEchoDataFormat, SLOT(slotEchoDataFormat(void*)), Qt::QueuedConnection);
	connect(this, SIGNAL(sendEchoDataPackFormat(void*)), pThreadEchoDataFormat, SLOT(slotEchoDataPackFormat(void*)), Qt::QueuedConnection);
	connect(pThreadEchoDataFormat, SIGNAL(sendEchoDataSave(void*)), pThreadEchoDataSave, SLOT(slotEchoDataSave(void*)), Qt::QueuedConnection);
	connect(this, SIGNAL(sendASKWaveDate()), pThreadEchoDataFormat, SLOT(slotASKWaveDate()), Qt::QueuedConnection);

	connect(pThreadEchoDataFormat, SIGNAL(sendEchoDataSave2(void*)), pThreadEchoDataSave, SLOT(slotEchoDataSave2(void*)), Qt::QueuedConnection);

	GetGPUDeviceProperties();//获取GPU设备硬件参数

    InitTarRcsInfo(nullptr);//加载散射系数文件
    InitClutterRcsInfo(nullptr);//加载杂波系数文件

	//定时器监视UDP连接
	timer = new QTimer(this);
	connect(timer, SIGNAL(timeout()), this, SLOT(handleTimeout()));
	//timer->start(1000);


    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadResourceScheduling::~ThreadResourceScheduling()
{

}

void ThreadResourceScheduling::slotASKWaveDate()
{
	emit sendASKWaveDate();
}
void ThreadResourceScheduling::handleTimeout()
{
	if (m_GpuCount == 0)
		return;

	stCalcutePoolInfo *pStCalcutePoolInfo = _MallocMemObj(stCalcutePoolInfo);
	cudaDeviceProp pCudaDeviceProp = { 0 };
//	for (int i = 0; i < m_GpuCount; i++)
//	{
//		cudaGetDeviceProperties(&pCudaDeviceProp, i);
//		nvmlMemory_t nvDevice;
//		nvmlDevice_t dev;
//		nvmlDeviceGetHandleByIndex(i, &dev);
//		nvmlReturn_t dDECLDIR = nvmlDeviceGetMemoryInfo(dev, &nvDevice);

//		pStCalcutePoolInfo->GPUDeviceInfo[i].cudaMemTotalGB = nvDevice.total;
//		pStCalcutePoolInfo->GPUDeviceInfo[i].cudaMemRestGB = nvDevice.free;
//		//pStCalcutePoolInfo->GPUDeviceInfo[i].ResourceGPU	= nvDevice.gpu;
//	}
	pStCalcutePoolInfo->GPU_Num = m_GpuCount;
	emit sendSocketMsgTCP(pStCalcutePoolInfo, (unsigned int)DecoteType_DeviceState);
	//_ReleaseMemObj(stCalcutePoolInfo, pStCalcutePoolInfo);


}
//获取GPU板卡信息
bool ThreadResourceScheduling::GetGPUDeviceProperties()
{
    
    if(m_GpuCount == 0)
        return false;
    
	stCalcutePoolInfo *pStCalcutePoolInfo = _MallocMemObj(stCalcutePoolInfo);
	cudaDeviceProp pCudaDeviceProp = { 0 };
    for(int i = 0; i < m_GpuCount;i++)
    {
        cudaGetDeviceProperties(&pCudaDeviceProp,i);

    }
	_ReleaseMemObj(stCalcutePoolInfo, pStCalcutePoolInfo);

	int *GPU_IDInfo = new int[MAX_GPU_DEVICE_NUM];
	memset(GPU_IDInfo, 0, sizeof(int)*MAX_GPU_DEVICE_NUM);
	
	//初始化GPU运算资源，由外部配置，支持重新配置
	for (int i = 0; i < m_GpuCount; i++)
	{
		stTaskInfo *pStTaskInfo = (stTaskInfo*)_MallocMemObj(stTaskInfo);
		pStTaskInfo->GPU_Num = 1;
		pStTaskInfo->GPU_ID = i + m_GPU_NO_Start;
		pStTaskInfo->GPU_Func[0] = (0x01 << 0) + (0x01 << 1) + (0x01 << 2);
		pThreadClassGPUCalcute[i]->InitGPUResource(GPU_IDInfo, pStTaskInfo);
	}

    return true;
}
//初始化目标散射系数信息
void ThreadResourceScheduling::InitTarRcsInfo(char *path)
{
	float minx = 5e31;
	float minz = 5e31;
	float maxx = -5e31;
	float maxz = -5e31;
	char FilePath[4][256];
	memset(FilePath, 0, sizeof(FilePath));
	memcpy(FilePath[0], "..\\..\\..\\rcs\\JFC1_3DSC", sizeof("..\\..\\..\\rcs\\JFC1_3DSC"));
	memcpy(FilePath[1], "..\\..\\..\\rcs\\CVN68\\HH_3DSC", sizeof("..\\..\\..\\rcs\\CVN68\\HH_3DSC"));
	memcpy(FilePath[2], "..\\..\\..\\rcs\\DDG104\\HH_3DSC", sizeof("..\\..\\..\\rcs\\DDG104\\HH_3DSC"));
	memcpy(FilePath[3], "..\\..\\..\\rcs\\C17\\HH_3DSC", sizeof("..\\..\\..\\rcs\\C17\\HH_3DSC"));
	stTarClutterLink *pstTarClutterLinkHead = (stTarClutterLink*)_MallocMemObj(stTarClutterLink); pstTarClutterLinkHead->init();
	for (int Tar = 0; Tar < 4; Tar++)
	{
		stTarPitLink *pStTarPitLinkHead = pClassRcsRead->ReadRCS_File2Link(FilePath[Tar]);
		//
		//stTarPitLink *pStTarPitLinkHead = pClassRcsRead->readModPhara(FilePath[Tar]);
		//continue;

		stTarPitLink *pStTarPitLinkTest = pStTarPitLinkHead;
		stTarClutterLink *pstTarClutterLinkNode = (stTarClutterLink*)_MallocMemObj(stTarClutterLink); pstTarClutterLinkNode->init();
		pstTarClutterLinkNode->iTarNo = Tar;
		ClutterRoeData *clutterRoeData = (ClutterRoeData*)_MallocMemObj(ClutterRoeData);
		pstTarClutterLinkNode->pClutterRoeData = clutterRoeData;
		while (pStTarPitLinkTest->next)
		{
			pStTarPitLinkTest = pStTarPitLinkTest->next;
			stTarAziLink *pstTarAziLinkTest = pStTarPitLinkTest->pStTarAziLink->next;
			if (pstTarAziLinkTest->pit == 90 && pstTarAziLinkTest->azi == 0)
			{
				for (int i = 0; i < pstTarAziLinkTest->pointNum; i++)
				{
					if (pstTarAziLinkTest->pMissileData->x[i] < minx)minx = pstTarAziLinkTest->pMissileData->x[i];
					if (pstTarAziLinkTest->pMissileData->x[i] > maxx)maxx = pstTarAziLinkTest->pMissileData->x[i];
					if (pstTarAziLinkTest->pMissileData->y[i] < minz)minz = pstTarAziLinkTest->pMissileData->y[i];
					if (pstTarAziLinkTest->pMissileData->y[i] > maxz)maxz = pstTarAziLinkTest->pMissileData->y[i];
				}
				break;
			}
		}
		pstTarClutterLinkNode->pClutterRoeData->MinX = minx;
		pstTarClutterLinkNode->pClutterRoeData->MaxX = maxx;
		pstTarClutterLinkNode->pClutterRoeData->MinZ = minz;
		pstTarClutterLinkNode->pClutterRoeData->MaxZ = maxz;
		AddLink(pstTarClutterLinkHead, pstTarClutterLinkNode);

		pStTarPitLinkHead->iTarNo = Tar;
		for (int i = 0; i < m_GpuCount; i++)//每个GPU卡初始化目标散射系数文件
		{
			pThreadClassGPUCalcute[i]->InitTarRcsPara(pStTarPitLinkHead);
		}
		stTarPitLink *pStTarPitLinkTemp = pStTarPitLinkHead;
		while (pStTarPitLinkTemp->next)
		{
			pStTarPitLinkTemp = pStTarPitLinkTemp->next;

			stTarAziLink *pStTarAziLinkHead = pStTarPitLinkTemp->pStTarAziLink;
			stTarAziLink *pStTarAziLinkTemp = pStTarAziLinkHead;
			while (pStTarAziLinkTemp->next)
			{
				pStTarAziLinkTemp = pStTarAziLinkTemp->next;
				if (pStTarAziLinkTemp->pMissileData){
					_ReleaseMemObj(MissileData, pStTarAziLinkTemp->pMissileData);
				}
			}
			DestroyLink(stTarAziLink, pStTarAziLinkHead, true);
		}
		DestroyLink(stTarPitLink, pStTarPitLinkHead, true);
	}

	AddLink(m_stTarClutterLink, pstTarClutterLinkHead);
	_ReleaseMemObj(stTarClutterLink, pstTarClutterLinkHead);
}
//初始化海杂波散射系数信息
void ThreadResourceScheduling::InitClutterRcsInfo(char *path)
{
    char FilePath[4][256];
    memset(FilePath, 0, sizeof(FilePath));
    //更换为面杂波数据位置
	memcpy(FilePath[0], "..\\..\\..\\HK\\hm_1", sizeof("..\\..\\..\\HK\\hm_1"));
    memcpy(FilePath[1], "..\\..\\..\\HK\\hm_3", sizeof("..\\..\\..\\HK\\hm_3"));
    memcpy(FilePath[2], "..\\..\\..\\HK\\hm_4", sizeof("..\\..\\..\\HK\\hm_4"));
    memcpy(FilePath[3], "..\\..\\..\\HK\\hm_5", sizeof("..\\..\\..\\HK\\hm_5"));
    //memcpy(FilePath[3], "..\\..\\..\\rcs\\C17\\HH_3DSC", sizeof("..\\..\\..\\rcs\\C17\\HH_3DSC"));
    for (int Tar = 0; Tar < 4; Tar++)
    {
        stClutterSeaStateLink* pStClutterSeaStateLink = pClassClutterRcsRead->readClutterRCSFile(FilePath[Tar]);

		for (int i = 0; i < m_GpuCount; i++)//每个GPU卡初始化海杂波散射系数文件
        {
            pThreadClassGPUCalcute[i]->InitClutterRcsPara(pStClutterSeaStateLink);
        }
		DestroyLink(stClutterPitLink, pStClutterSeaStateLink->pStClutterPitLink, true);
		_ReleaseMemObj(stClutterSeaStateLink, pStClutterSeaStateLink);
    }
	for (int i = 0; i < m_GpuCount; i++)//每个GPU卡初始化海杂波散射系数文件
	{
		pThreadClassGPUCalcute[i]->InitTarClutterPara(m_stTarClutterLink);
	}

	DestroyLink(stTarClutterLink, m_stTarClutterLink, true);
}
//波门宽度计算(适用场景为空对海场景、波束俯仰角小于-3°的情形)
template<class T, class T2, class T3>
double ThreadResourceScheduling::CalcuteRangeGaeInfo(double &Rmin, double &WaveGateWidth, T AziAngle, T PitAngle, T2 lobeWidth3dB, T3 *RadarPos, double Fs, double Tp, double PRT)
{

	float PI_div180 = PI / 180.0;

	//计算波束波足坐标P0
	float PitAngle0 = PitAngle;
	float BeamX0 = -RadarPos[1] * cos(AziAngle*PI_div180) / tan(PitAngle0*PI_div180) + RadarPos[0];
	float BeamY0 = 0;
	float BeamZ0 = -RadarPos[1] * sin(AziAngle*PI_div180) / tan(PitAngle0*PI_div180) + RadarPos[2];

	//先计算波足坐标P1
	float PitAngle1 = PitAngle + lobeWidth3dB*0.5f;
	float BeamX1 = -RadarPos[1] * cos(AziAngle*PI_div180) / tan(PitAngle1*PI_div180) + RadarPos[0];
	float BeamY1 = 0;
	float BeamZ1 = -RadarPos[1] * sin(AziAngle*PI_div180) / tan(PitAngle1*PI_div180) + RadarPos[2];

	//先计算波足坐标P2
	float PitAngle2 = PitAngle - lobeWidth3dB*0.5f;
	float BeamX2 = -RadarPos[1] * cos(AziAngle*PI_div180) / tan(PitAngle2*PI_div180) + RadarPos[0];
	float BeamY2 = 0;
	float BeamZ2 = -RadarPos[1] * sin(AziAngle*PI_div180) / tan(PitAngle2*PI_div180) + RadarPos[2];
	float PRT_2_Len = (PRT / 2)*C_ / 2;
	float PRT_2_Len2 = 4096.0/Fs*C_ / 2;//等于8192的情况

	double Range[3] = { 0 };
	//计算DYT到波足坐标P1和波足坐标P2的距离
	Range[0] = sqrt((RadarPos[0] - BeamX0)*(RadarPos[0] - BeamX0) + (RadarPos[1] - BeamY0)*(RadarPos[1] - BeamY0) + (RadarPos[2] - BeamZ0)*(RadarPos[2] - BeamZ0));
	Range[1] = sqrt((RadarPos[0] - BeamX1)*(RadarPos[0] - BeamX1) + (RadarPos[1] - BeamY1)*(RadarPos[1] - BeamY1) + (RadarPos[2] - BeamZ1)*(RadarPos[2] - BeamZ1));
	Range[2] = sqrt((RadarPos[0] - BeamX2)*(RadarPos[0] - BeamX2) + (RadarPos[1] - BeamY2)*(RadarPos[1] - BeamY2) + (RadarPos[2] - BeamZ2)*(RadarPos[2] - BeamZ2));

	float R1 = sqrt((BeamX0 - BeamX2)*(BeamX0 - BeamX2) + (BeamZ0 - BeamZ2)*(BeamZ0 - BeamZ2));

	if (PitAngle > -10)
	{
		if (PRT*Fs<8192)
		{
			if (R1>PRT_2_Len)
			{
				Rmin = Range[0] - PRT_2_Len;
			}
			else
				Rmin = Range[2] - 200;
		}
		else
		{
			if (R1 > PRT_2_Len2)
			{
				Rmin = Range[0] - PRT_2_Len2;
			}
			else
				Rmin = Range[2] - 200;
		}

		WaveGateWidth = PRT*Fs;
		if (WaveGateWidth > 8192){
			WaveGateWidth = 8192;
		}
		return 0;
	}

	double RangeMax = Range[0];
	double RangeMin = Range[1];
	for (int i = 0; i < 3; i++)
	{
		if (RangeMax < Range[i])RangeMax = Range[i];
		if (RangeMin > Range[i])RangeMin = Range[i];
	}

	WaveGateWidth = round(2 * abs(RangeMax - RangeMin) / C_*Fs + Tp*Fs);
	if (WaveGateWidth > PRT*Fs)
	{
		WaveGateWidth = PRT*Fs;
	}

	if (WaveGateWidth > 8192){
		WaveGateWidth = 8192;
	}

	if (PRT*Fs<8192)
	{
		if (R1>PRT_2_Len)
		{
			Rmin = Range[0] - PRT_2_Len;
		}
		else
			Rmin = Range[2] - 200;
	}
	else
	{
		if (R1 > PRT_2_Len2)
		{
			Rmin = Range[0] - PRT_2_Len2;
		}
		else
			Rmin = Range[2] - 200;
	}

	return 0;
}

//
void ThreadResourceScheduling::slotCalcuteEcho(void *p)
{

}
//
void ThreadResourceScheduling::slotSettingSarSence(void *p)
{
	for (int i = 0; i < m_GpuCount; i++)
	{
		pThreadClassGPUCalcute[i]->slotSettingSarSence(p);
	}
	_ReleaseMemObj(void, p);
}
void ThreadResourceScheduling::SettingTarPara(void *p,void *pc)
{
	for (int i = 0; i < m_GpuCount; i++)
	{
		pThreadClassGPUCalcute[i]->slotSettingAntPara(p,pc);

	}
}
//面目标杂波信息
void ThreadResourceScheduling::SettingSarClutter(void *p, void *pc)
{
	for (int i = 0; i < m_GpuCount; i++)
    {
        pThreadClassGPUCalcute[i]->slotSettingClutter(p,pc);

    }
}
//资源调度函数，负责将接收到的仿真任务分发给各个GPU卡进行计算，GPU计算完的中间数据再回传到资源调度模块，再进行合并（多GPU卡SAR回波、单脉冲回波、杂波、干扰等合并）等操作
void ThreadResourceScheduling::slotResourceScheduling(void *p)
{
	qint64 tn = QDateTime::currentMSecsSinceEpoch();

	static unsigned long long	FrameSerial			= 1;
	stDYTExternPara				*pStRedMasterPara	= (stDYTExternPara*)p;
	//将多个脉冲均匀分配给多个GPU卡
	int PulseNumPerGPU	= ceil((float)pStRedMasterPara->mDYTPara.DYTPulseNum / m_GpuCount);//每个GPU卡处理的脉冲数量
	int TotalPulse		= pStRedMasterPara->mDYTPara.DYTPulseNum;
	int restPulse		= pStRedMasterPara->mDYTPara.DYTPulseNum;

	double Rmin = 0;
	double WaveGateWidth = 0;
	double RadarPos[3] = { 0 };
	RadarPos[0] = pStRedMasterPara->mDYTPara.pDYT_MovePara[0].DYTRadarX;
	RadarPos[1] = pStRedMasterPara->mDYTPara.pDYT_MovePara[0].DYTRadarY;
	RadarPos[2] = pStRedMasterPara->mDYTPara.pDYT_MovePara[0].DYTRadarZ;
	CalcuteRangeGaeInfo(Rmin, WaveGateWidth, pStRedMasterPara->mDYTPara.Azimuth, pStRedMasterPara->mDYTPara.Pitch, pStRedMasterPara->mDYTPara.BeamWidth, 
						RadarPos, pStRedMasterPara->mDYTPara.DYTFs, pStRedMasterPara->mDYTPara.DYTPw, pStRedMasterPara->mDYTPara.DYTPRT);

	//pStRedMasterPara->mDYTPara.Rmin = Rmin;
	//pStRedMasterPara->mDYTPara.WaveGateWidth = WaveGateWidth;
	//Rmin = 84000;
	//WaveGateWidth = 8192;

	for (int i = 0; i < m_GpuCount; i++)
	{
		stDYTExternPara *pStDYTExternParaEcho = (stDYTExternPara*)_MallocMemObj(stDYTExternPara);
		memcpy(pStDYTExternParaEcho, pStRedMasterPara, sizeof(stDYTExternPara));
		pStDYTExternParaEcho->mDYTPara.DYTPulseNum = (restPulse - PulseNumPerGPU) >= 0 ? PulseNumPerGPU : restPulse;

		stResourceScheduingPara *pStResourceScheduingPara	= (stResourceScheduingPara*)_MallocMemObj(stResourceScheduingPara);
		pStResourceScheduingPara->FrameSerial				= FrameSerial;
		pStResourceScheduingPara->childFrameNum				= m_GpuCount;
		pStResourceScheduingPara->childFrameSerial			= i + 1;
		pStResourceScheduingPara->pulseStartIndex			= TotalPulse - restPulse;
		pStResourceScheduingPara->Rmin						= pStRedMasterPara->mDYTPara.DYTRmin;
		pStResourceScheduingPara->WaveGateWidth				= pStRedMasterPara->mDYTPara.WaveGateWidth;
		pStResourceScheduingPara->curMSecsSinceEpoch		= tn;

		restPulse -= pStDYTExternParaEcho->mDYTPara.DYTPulseNum;//当前剩余脉冲数
		if (i == 0)emit sendTarEchoDateGen1(i+m_GPU_NO_Start, pStDYTExternParaEcho, pStResourceScheduingPara);
		if (i == 1)emit sendTarEchoDateGen2(i+m_GPU_NO_Start, pStDYTExternParaEcho, pStResourceScheduingPara);
		if (i == 2)emit sendTarEchoDateGen3(i+m_GPU_NO_Start, pStDYTExternParaEcho, pStResourceScheduingPara);
		if (i == 3)emit sendTarEchoDateGen4(i+m_GPU_NO_Start, pStDYTExternParaEcho, pStResourceScheduingPara);
		if (i == 4)emit sendTarEchoDateGen5(i+m_GPU_NO_Start, pStDYTExternParaEcho, pStResourceScheduingPara);
		if (i == 5)emit sendTarEchoDateGen6(i+m_GPU_NO_Start, pStDYTExternParaEcho, pStResourceScheduingPara);
		if (i == 6)emit sendTarEchoDateGen7(i + m_GPU_NO_Start, pStDYTExternParaEcho, pStResourceScheduingPara);
		if (i == 7)emit sendTarEchoDateGen8(i + m_GPU_NO_Start, pStDYTExternParaEcho, pStResourceScheduingPara);
		cudaError_t Err = cudaGetLastError();
	}
	FrameSerial++;

	_ReleaseMemObj(stDYTExternPara, pStRedMasterPara);
	return;
}
//资源调度函数，负责将接收到的仿真任务分发给各个GPU卡进行计算，GPU计算完的中间数据再回传到资源调度模块，再进行合并（多GPU卡SAR回波、单脉冲回波、杂波、干扰等合并）等操作
void ThreadResourceScheduling::slotResourceScheduling2(void *p)
{
	stDYTExternPara *pStRedMasterPara = (stDYTExternPara*)p;

	//目标调制任务分发
	int TargetNum	= pStRedMasterPara->TargetNum;//目标个数
	if (TargetNum > 0)
	{
		stDYTExternPara *pStDYTExternParaEcho = (stDYTExternPara*)_MallocMemObj(stDYTExternPara);
		memcpy(pStDYTExternParaEcho, pStRedMasterPara, sizeof(stDYTExternPara));
        //emit sendTarEchoDateGen(pStDYTExternParaEcho);
	}
}

void ThreadResourceScheduling::slotRecvGPUEcho(void *p)
{
	emit sendEchoDataPackFormat(p);
}

//回波整合函数，①将多卡的回波数据进行排序 ②将不同类型的波形组合以及合成（回波、杂波、干扰）
void ThreadResourceScheduling::slotRecvGPUEcho2(void *p)
{
	static uint cntShedeu = 0;
	stEchoData *pStEchoData = (stEchoData*)p;

	if (pStEchoData->childFrameNum < 2)
	{
		stFrameLink *pFrameNode		= (stFrameLink*)_MallocMemObj(stFrameLink); pFrameNode->init();
		pFrameNode->p[0]			= pStEchoData;
		pFrameNode->iNodeCount		= 1;
		pFrameNode->childFrameNum	= 1;

		qint64 tn = QDateTime::currentMSecsSinceEpoch();
		qint64 tSub = tn - pStEchoData->curMSecsSinceEpoch;
		emit sendEchoDataFormat(pFrameNode);

		qDebug() << "------------------------------------------------------time pack over " << tSub << m_RestTaskNode << m_StFrameLinkHead->iNodeCount << cntShedeu; cntShedeu++;
		return;
	}

	if (m_StFrameLinkHead->next == nullptr)
	{
		stFrameLink *pFrameNode = (stFrameLink*)_MallocMemObj(stFrameLink); pFrameNode->init();
		AddLink(m_StFrameLinkHead, pFrameNode);
	}
	stFrameLink *pFrameTemp = m_StFrameLinkHead;
	while (pFrameTemp->next)//加入缓存链表
	{
		pFrameTemp = pFrameTemp->next;
		if (pFrameTemp->childFrameNum > 0)
		{
			if (pFrameTemp->FrameSerial == pStEchoData->FrameSerial)
			{
				pFrameTemp->p[pStEchoData->childFrameSerial - 1] = pStEchoData;
				pFrameTemp->iNodeCount++;
				break;
			}
		}
		else
		{
			pFrameTemp->FrameSerial		= pStEchoData->FrameSerial;
			pFrameTemp->childFrameNum	= pStEchoData->childFrameNum;
			pFrameTemp->p[pStEchoData->childFrameSerial - 1] = pStEchoData;
			pFrameTemp->iNodeCount++;
			break;
		}
		if (pFrameTemp->next == nullptr)
		{
			stFrameLink *pFrameNode = (stFrameLink*)_MallocMemObj(stFrameLink); pFrameNode->init();
			pFrameNode->FrameSerial = pStEchoData->FrameSerial;
			pFrameNode->childFrameNum = pStEchoData->childFrameNum;
			pFrameNode->p[pStEchoData->childFrameSerial - 1] = pStEchoData;
			pFrameNode->iNodeCount++;
			AddLink(m_StFrameLinkHead, pFrameNode);
			break;
		}
	}

	//遍历链表，满足条件的进行整合
	pFrameTemp = m_StFrameLinkHead;
	while (pFrameTemp->next)
	{
		pFrameTemp = pFrameTemp->next;
		if (pFrameTemp->iNodeCount == pFrameTemp->childFrameNum)//多卡数据已经凑齐
		{
			RemoveLinkNode(m_StFrameLinkHead, pFrameTemp);
			pFrameTemp->next = nullptr;
			pFrameTemp->prev = pFrameTemp;

			for (int i = 0; i < pFrameTemp->childFrameNum; i++)
			{
				if (pFrameTemp->p[i] == nullptr)
				{
					pFrameTemp->p[i] = pFrameTemp->p[i];
				}
			}

			qint64 tn = QDateTime::currentMSecsSinceEpoch();
			qint64 tSub = tn - pStEchoData->curMSecsSinceEpoch;
			emit sendEchoDataFormat(pFrameTemp);
			m_RestTaskNode--;
			qDebug() << "------------------------------------------------------time pack over " << tSub << m_RestTaskNode << m_StFrameLinkHead->iNodeCount<<cntShedeu; cntShedeu++;

            pFrameTemp = m_StFrameLinkHead;
		}
	}

}

