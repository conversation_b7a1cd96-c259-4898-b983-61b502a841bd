# =========================================================================================
# FileProperties:
#     FileName: EchoKernelLib.pro
#     SvnProperties: 
#         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/pro/EchoSarLib.pro $
#         $Author: yening $
#         $Revision: 111 $
#         $Date: 2025-01-15 10:37:52 $
# =========================================================================================
#-------------------------------------------------
#
# Project created by QtCreator 2024-04-01T10:50:55
#
#-------------------------------------------------

QT       += core

greaterThan(QT_MAJOR_VERSION, 4): QT -= widgets

TARGET = _HQ_Echo_Sar
TEMPLATE = lib

DESTDIR += ../dll

DEFINES += _HQ_ECHO_SAR_LIBRARY

INCLUDEPATH +=  ../inc \
                ../inc/Thread \
                ../inc/MemPool \
                ../inc/public \
                ../inc/cuda


#CONFIG +=plugin


SOURCES +=  \
            ../src/public/WhiteCalGloable.cpp \
            ../inc/public/HQ_Echo_Sar.cpp \
            ../src/Thread/ThreadClassSarEcho.cpp \
            ../inc/MemPool/MyMalloc.cpp \
            ../inc/MemPool/MyMallocCUDA.cpp \
            ../src/Thread/ThreadClassBase.cpp \
            ../src/Thread/ThreadClassSendWaveGen.cpp \
            ../src/Thread/ThreadClassAntennaGen.cpp

HEADERS  += \
            ../inc/cuda/KernelSar.h \
            ../inc/cuda/KernelCultter.h \
            ../inc/cuda/KernelPublic.h \
            ../inc/cuda/KernelJAM.h \
            ../inc/cuda/KernelAntenna.h \
            ../inc/cuda/KernelSpline.h \
            ../inc/MemPool/_stumempool.h \
            ../inc/public/WhiteCalGlobel.h \
            ../inc/public/HQ_Echo_Sar.h \
            ../inc/Thread/ThreadClassSarEcho.h \
            ../inc/MemPool/MyMalloc.h \
            ../inc/MemPool/MyMallocCUDA.h \
            ../inc/Thread/ThreadClassBase.h \
            ../inc/Thread/ThreadClassSendWaveGen.h \
            ../inc/Thread/ThreadClassAntennaGen.h

#Cuda sources
CUDA_SOURCES += \
                ../src/cuda/KernelSar.cu \
                ../src/cuda/KernelCultter.cu \
                ../src/cuda/KernelJAM.cu \
                ../src/cuda/KernelPublic.cu \
                ../src/cuda/KernelAntenna.cu \
                ../src/cuda/KernelSpline.cu

#windows下配置CUDA环境依赖
win32{
    INCLUDEPATH += C:\cuda\v10.0\include \
                    C:\cuda\v10.0\common\inc

    LIBS += -LC:/CUDA/v10.0/lib/x64 -lcudart -lcublas -lcufft -lcurand

    CUDA_SDK ="C:/cuda/v10.0"
    CUDA_DIR ="C:/cuda/v10.0"
    QMAKE_LIBDIR += $$CUDA_DIR/lib/x64
    SYSTEM_TYPE = 64

    CUDA_ARCH = sm_61
    NVCCFLAGS = --use_fast_math
    CUDA_INC = $$join("C:/cuda/v10.0/include",'" -I"','-I"','"')

    MSVCRT_LINK_FLAG_DEBUG = "/MDd"
    MSVCRT_LINK_FLAG_RELEASE = "/MD"

    CUDA_OBJECTS_DIR = ./

    CONFIG(debug, debug|release){
        cuda_d.input = CUDA_SOURCES
        cuda_d.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}testcuda.obj
        cuda_d.commands = $$CUDA_DIR/bin/nvcc.exe -D_DEBUG $$NVCC_OPTIONS $$CUDA_INC $$CUDA_LIBS --machine $$SYSTEM_TYPE\
                        -arch=$$CUDA_ARCH -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME} -Xcompiler $$MSVCRT_LINK_FLAG_DEBUG
        cuda_d.dependency_type = TYPE_C
        QMAKE_EXTRA_COMPILERS += cuda_d
    }
    else{
        cuda.input = CUDA_SOURCES
        cuda.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}testcuda.obj
        cuda.commands = $$CUDA_DIR/bin/nvcc.exe $$NVCC_OPTIONS $$CUDA_INC $$CUDA_LIBS --machine $$SYSTEM_TYPE\
                        -arch=$$CUDA_ARCH -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME} -Xcompiler $$MSVCRT_LINK_FLAG_RELEASE
        cuda.dependency_type = TYPE_C
        QMAKE_EXTRA_COMPILERS += cuda
    }
    INCLUDEPATH+=$$CUDA_DIR
    
    #内存池库添加
    win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../lib/ -l_stumempool
    else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../lib/ -l_stumempoold

    INCLUDEPATH += $$PWD/../
    DEPENDPATH += $$PWD/../
}
unix{
    #cuda
     LIBS += -L"/usr/local/lib" \
         -L"/usr/local/cuda/lib64" \
         -lcuda \
         -lcufft \
         -lcudart
    
     CUDA_SDK = "/usr/local/cuda"   # Path to cuda SDK install
     CUDA_DIR = "/usr/local/cuda"            # Path to cuda toolkit install
     SYSTEM_NAME = linux         # Depending on your system either 'Win32', 'x64', or 'Win64'
     SYSTEM_TYPE = 64            # '32' or '64', depending on your system
     CUDA_ARCH = sm_61          # Type of CUDA architecture, for example 'compute_10', 'compute_11', 'sm_10'
     NVCC_OPTIONS = --use_fast_math
    
    
     INCLUDEPATH += $$CUDA_DIR/include
     QMAKE_LIBDIR += $$CUDA_DIR/lib64/
    
     CUDA_OBJECTS_DIR = ./
    
     CUDA_LIBS = cuda cudart cufft
     CUDA_INC = $$join(INCLUDEPATH,'" -I"','-I"','"')
     NVCC_LIBS = $$join(CUDA_LIBS,' -l','-l', '')
    
     CONFIG(debug, debug|release) {
         # Debug mode
         cuda_d.input = CUDA_SOURCES
         cuda_d.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}_cuda.o
         cuda_d.commands = $$CUDA_DIR/bin/nvcc -D_DEBUG $$NVCC_OPTIONS $$CUDA_INC $$NVCC_LIBS --machine $$SYSTEM_TYPE -arch=$$CUDA_ARCH -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME}
         cuda_d.dependency_type = TYPE_C
         QMAKE_EXTRA_COMPILERS += cuda_d
     }
     else {
         # Release mode
         cuda.input = CUDA_SOURCES
         cuda.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}_cuda.o
         cuda.commands = $$CUDA_DIR/bin/nvcc $$NVCC_OPTIONS $$CUDA_INC $$NVCC_LIBS --machine $$SYSTEM_TYPE -arch=$$CUDA_ARCH -O3 -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME}
         cuda.dependency_type = TYPE_C
         QMAKE_EXTRA_COMPILERS += cuda
     }


}

