/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadEchoDataFormat.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadEchoDataFormat.cpp $
 *         $Author: yening $
 *         $Revision: 345 $
 *         $Date: 2025-03-26 18:41:32 $
*******************************************************************************************/
#include "ThreadEchoDataFormat.h"
#include<QThread>
#include <QFile>
#include <QDebug>
#include"globalExternalStruct.h"
#include"ThreadSocketTCPExtern.h"

ThreadEchoDataFormat::ThreadEchoDataFormat(QObject *parent) : QObject(parent)
{
    _DefineMemPool(stEchoDataPack,10);
    _DefineMemPool(stWaveShow,10);
	SetMemPoolSrcFileName((char*)"ThreadEchoDataFormat", sizeof("ThreadEchoDataFormat"));

	m_AmpMax = 1;
    m_bWaveDataShowFlag = false;

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadEchoDataFormat::~ThreadEchoDataFormat()
{

}
void ThreadEchoDataFormat::EchoWaveDataFormat(int dot,int &offset,Complexf *pComplexf,char *pBuffer)
{
    *(int*)(pBuffer + offset) = dot*sizeof(Complexs);offset += sizeof(int);
    Complexs *pComplexs = (Complexs*)(pBuffer + offset);
    if(dot > 0)
    {
		float ampCoef = 1.0 / m_AmpMax*2e4;
        for(int i = 0; i < dot;i++)
        {
			pComplexs[i].x = pComplexf[i].x*ampCoef + 0.5f;
			pComplexs[i].y = pComplexf[i].y*ampCoef + 0.5f;
        }
        offset += dot*sizeof(Complexs);
    }
}

void ThreadEchoDataFormat::platformPara(int &offset, PLATFORM *PlatForm, char *pBuffer)
{
	*(double*)(pBuffer + offset) = PlatForm->NorthPos; offset += sizeof(double);
	*(double*)(pBuffer + offset) = PlatForm->SkyPos; offset += sizeof(double);
	*(double*)(pBuffer + offset) = PlatForm->EastPos; offset += sizeof(double);

	*(double*)(pBuffer + offset) = PlatForm->SkyVel; offset += sizeof(double);
	*(double*)(pBuffer + offset) = PlatForm->SkyVel; offset += sizeof(double);
	*(double*)(pBuffer + offset) = PlatForm->EastVel; offset += sizeof(double);

	*(double*)(pBuffer + offset) = PlatForm->NorthAcc; offset += sizeof(double);
	*(double*)(pBuffer + offset) = PlatForm->SkyAcc; offset += sizeof(double);
	*(double*)(pBuffer + offset) = PlatForm->EastAcc; offset += sizeof(double);
}

float ThreadEchoDataFormat::AmpMax(int dot, float m_AmpMax,Complexf *pComplexf)
{
	float ampMax = -1e5;
	for (int i = 0; i < dot; i++)
	{
		float amp = abs(pComplexf[i].x);
		if (ampMax < amp){
			ampMax = amp;
		}

		amp = abs(pComplexf[i].y);
		if (ampMax < amp){
			ampMax = amp;
		}
	}

	if (abs(m_AmpMax - ampMax) / m_AmpMax > 0.2f)
	{
		m_AmpMax = ampMax;
	}

	return m_AmpMax;
}

void ThreadEchoDataFormat::EchoDataFormat(stEchoData *pStEchoData,stEchoDataPack *pStEchoDataPack)
{
	int		offset		= pStEchoDataPack->bytes;
	char	*pBuffer	= (char*)pStEchoDataPack->pBuffer;

	stEchoExternPara *pStEchoExternPara = (stEchoExternPara*)(pBuffer);

	for (int i = 0; i < pStEchoData->Na_Save; i++)
	{
		memcpy(pBuffer + offset, &pStEchoData->PlatForm[i], sizeof(double) * 9); offset += sizeof(double) * 9;
		memcpy(pBuffer + offset, &pStEchoData->TargetPos[0], sizeof(double) * 3); offset += sizeof(double) * 3;
		if (pStEchoExternPara->AziEleFlag == 0)
		{
			*(int*)(pBuffer + offset) = pStEchoData->SumEchoData1Dot; offset += sizeof(int);
			memcpy(pBuffer + offset, &pStEchoData->SumEchoDataPolar1[i*pStEchoData->SumEchoData1Dot], sizeof(Complexf)*pStEchoData->SumEchoData1Dot); offset += sizeof(Complexf)*pStEchoData->SumEchoData1Dot;
		}
		else
		{
			*(int*)(pBuffer + offset) = pStEchoData->SumEchoData1Dot; offset += sizeof(int);
			memcpy(pBuffer + offset, &pStEchoData->SumEchoDataPolar1[i*pStEchoData->SumEchoData1Dot], sizeof(Complexf)*pStEchoData->SumEchoData1Dot); offset += sizeof(Complexf)*pStEchoData->SumEchoData1Dot;
			*(int*)(pBuffer + offset) = pStEchoData->AziEchoData1Dot; offset += sizeof(int);
			memcpy(pBuffer + offset, &pStEchoData->AziEchoDataPolar1[i*pStEchoData->AziEchoData1Dot], sizeof(Complexf)*pStEchoData->AziEchoData1Dot); offset += sizeof(Complexf)*pStEchoData->AziEchoData1Dot;
			*(int*)(pBuffer + offset) = pStEchoData->EleEchoData1Dot; offset += sizeof(int);
			memcpy(pBuffer + offset, &pStEchoData->EleEchoDataPolar1[i*pStEchoData->EleEchoData1Dot], sizeof(Complexf)*pStEchoData->EleEchoData1Dot); offset += sizeof(Complexf)*pStEchoData->EleEchoData1Dot;
			*(int*)(pBuffer + offset) = pStEchoData->ExEchoData1Dot; offset += sizeof(int);
			memcpy(pBuffer + offset, &pStEchoData->ExEchoDataPolar1[i*pStEchoData->ExEchoData1Dot], sizeof(Complexf)*pStEchoData->ExEchoData1Dot);  offset += sizeof(Complexf)*pStEchoData->ExEchoData1Dot;
			*(int*)(pBuffer + offset) = pStEchoData->AuEchoData1Dot1; offset += sizeof(int);
			memcpy(pBuffer + offset, &pStEchoData->AuEchoDataPolar1[i*pStEchoData->AuEchoData1Dot1], sizeof(Complexf)*pStEchoData->AuEchoData1Dot1);  offset += sizeof(Complexf)*pStEchoData->AuEchoData1Dot1;
			*(int*)(pBuffer + offset) = pStEchoData->AuEchoData1Dot2; offset += sizeof(int);
			memcpy(pBuffer + offset, &pStEchoData->AuEchoDataPolar2[i*pStEchoData->AuEchoData1Dot2], sizeof(Complexf)*pStEchoData->AuEchoData1Dot2);  offset += sizeof(Complexf)*pStEchoData->AuEchoData1Dot2;
			*(int*)(pBuffer + offset) = pStEchoData->AuEchoData1Dot3; offset += sizeof(int);
			memcpy(pBuffer + offset, &pStEchoData->AuEchoDataPolar3[i*pStEchoData->AuEchoData1Dot3], sizeof(Complexf)*pStEchoData->AuEchoData1Dot3);  offset += sizeof(Complexf)*pStEchoData->AuEchoData1Dot3;
			*(int*)(pBuffer + offset) = pStEchoData->AuEchoData1Dot4; offset += sizeof(int);
			memcpy(pBuffer + offset, &pStEchoData->AuEchoDataPolar4[i*pStEchoData->AuEchoData1Dot4], sizeof(Complexf)*pStEchoData->AuEchoData1Dot4);  offset += sizeof(Complexf)*pStEchoData->AuEchoData1Dot4;
		}
	}
	pStEchoDataPack->bytes = offset;

}

void ThreadEchoDataFormat::slotEchoDataFormat(void *p)
{
    stFrameLink *pStFrameLinkNode = (stFrameLink*)p;

    int Frame = pStFrameLinkNode->childFrameNum;

    stEchoDataPack *pStEchoDataPack = _MallocMemObj(stEchoDataPack);
	pStEchoDataPack->bytes = 0;

	int		offset = pStEchoDataPack->bytes;
	char	*pBuffer = (char*)pStEchoDataPack->pBuffer;

	stEchoExternPara *pStEchoExternPara = (stEchoExternPara*)(pBuffer + offset); offset += sizeof(stEchoExternPara);

	stEchoData *pStEchoData			= pStFrameLinkNode->p[0];
	pStEchoDataPack->RADARNo		= pStEchoData->RADARNo;
	pStEchoExternPara->AziEleFlag	= pStEchoData->mDYTPara.AziEleFlag;
	pStEchoExternPara->Rmin			= pStEchoData->Rmin;
	pStEchoExternPara->EchoDot		= pStEchoData->Nr_Save;
	pStEchoExternPara->AziK			= pStEchoData->AziK;
	pStEchoExternPara->PitK			= pStEchoData->PitK;
	pStEchoExternPara->PulseNum		= 0;

	pStEchoDataPack->bytes = offset;
    for(int i = 0; i < Frame;i++)
    {
		pStEchoData = pStFrameLinkNode->p[i];
		pStEchoExternPara->PulseNum += pStEchoData->Na_Save;
        EchoDataFormat(pStFrameLinkNode->p[i],pStEchoDataPack);
    }

	emit sendSocketMsgTCP(pStEchoDataPack, (unsigned int)DecodeXD_EchoWave);

	emit sendEchoDataSave(p);

}
void ThreadEchoDataFormat::slotEchoDataPackFormat(void *p)
{
	stEchoData *pStEchoData = (stEchoData*)p;

	stEchoDataPack *pStEchoDataPack = _MallocMemObj(stEchoDataPack);
	pStEchoDataPack->bytes = 0;

	int offset = 0;
	char *pBuffer = (char*)pStEchoDataPack->pBuffer;

	stEchoExternPara *pStEchoExternPara = (stEchoExternPara*)(pBuffer + offset); offset += sizeof(stEchoExternPara);

	pStEchoDataPack->RADARNo		= pStEchoData->RADARNo;
	pStEchoExternPara->AziEleFlag	= pStEchoData->AziEleFlag;
	pStEchoExternPara->Rmin			= pStEchoData->Rmin;
	pStEchoExternPara->EchoDot		= pStEchoData->Nr_Save;
	pStEchoExternPara->AziK			= pStEchoData->AziK;
	pStEchoExternPara->PitK			= pStEchoData->PitK;
	pStEchoExternPara->PulseNum		= pStEchoData->Na_Save;

	for (int i = 0; i < pStEchoData->Na_Save; i++)
	{
		memcpy(pBuffer + offset, &pStEchoData->PlatForm[i], sizeof(double) * 3); offset += sizeof(double) * 3;
		memcpy(pBuffer + offset, &pStEchoData->TargetPos[0], sizeof(double) * 3); offset += sizeof(double) * 3;
		if (pStEchoData->AziEleFlag == 0)
		{
			memcpy(pBuffer + offset, &pStEchoData->SumEchoDataPolar1[i*pStEchoData->SumEchoData1Dot], sizeof(Complexf)*pStEchoData->SumEchoData1Dot); offset += sizeof(Complexf)*pStEchoData->SumEchoData1Dot;
		}
		else
		{
			memcpy(pBuffer + offset, &pStEchoData->SumEchoDataPolar1[i*pStEchoData->SumEchoData1Dot], sizeof(Complexf)*pStEchoData->SumEchoData1Dot); offset += sizeof(Complexf)*pStEchoData->SumEchoData1Dot;
			memcpy(pBuffer + offset, &pStEchoData->AziEchoDataPolar1[i*pStEchoData->AziEchoData1Dot], sizeof(Complexf)*pStEchoData->AziEchoData1Dot); offset += sizeof(Complexf)*pStEchoData->AziEchoData1Dot;
			memcpy(pBuffer + offset, &pStEchoData->EleEchoDataPolar1[i*pStEchoData->EleEchoData1Dot], sizeof(Complexf)*pStEchoData->EleEchoData1Dot); offset += sizeof(Complexf)*pStEchoData->EleEchoData1Dot;
		}
	}
	pStEchoDataPack->bytes = offset;

	emit sendSocketMsgTCP(pStEchoDataPack, (unsigned int)DecodeXD_EchoWave);
}

void ThreadEchoDataFormat::slotASKWaveDate()
{
    m_bWaveDataShowFlag = true;
}


