/*******************************************************************************************
 * FileProperties: 
 *     FileName: ClassCoorDinate.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Class/ClassCoorDinate.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef CLASSCOORDINATE_H
#define CLASSCOORDINATE_H

#include<QFile>

#include "_stumempool.h"
#include "MyMalloc.h"
#include"globalRadarStruct.h"

#ifndef PI
#define PI				3.141592653589793
#endif

class ClassCoorDinate : public _StuMemPool,public MyMalloc
{

public:
    explicit ClassCoorDinate();
    ~ClassCoorDinate();
    


private:
    //矩阵乘法
    void matrixmul(float *matrix1, int row1, int col1, float *matrix2, int row2, int col2, float *matrix);
	

public:
    /**************************************
     * in:	alpha,beta,gamma		本体姿态角：航向角、俯仰角和横滚角(rad)
     * out: pMatixG                 旋转矩阵
     * ************************************/
    void CoorMatrixG(float alpha, float beta, float gamma,float *pMatixG);
    /***************************************
    * 实现从场景北天东坐标系到目标本体坐标系的坐标转换
    * in:	Btd						北天东坐标系中待转换的坐标
    * in:	origin					本体坐标系原点在北天东坐标系中的坐标
    * in:	alpha,beta,gamma		本体姿态角：航向角、俯仰角和横滚角(rad)
    * in:	TarNum                  目标个数
    * out:	boat					转换为本体坐标系中的坐标
    ************************************/
    void CoorBtdToBoat(int TarNum, float *pMatixG,float *origin,float *BTD,float *Boat);
    /***************************************
    * 实现从目标本体坐标系到场景北天东坐标系的坐标转换
    * in:   Boat					本体坐标系中待转换的坐标
    * in:	origin					本体坐标系原点在北天东坐标系中的坐标
    * in:	Num						本体中散射点个数
    * out:	BTD						转换为北天东坐标系中的坐标
    ************************************/
    void CoorBoatToBtd(int Num, float *pMatixG,float *origin,float *Boat,float *BTD);
	void CoorBoatToBtd_CUDA(int Num, float *dev_MatixG, float *dev_origin, float *dev_Boat_X, float *dev_Boat_Y, float *dev_Boat_Z, float *dev_BTD_X, float *dev_BTD_Y, float *dev_BTD_Z);
	//计算目标旋转矩阵和DYT信号入射角
	//pTarVel:目标速度
	//pTarPoz:目标位置
	//pTarDYTRang:弹目矢量
	//Azi:入射方位角
	//Pit:入射俯仰角
	//pMatixG:输出旋转矩阵
	void ConvertAngleMatixG(float *pTarVel, float *pTarPoz, float *pTarDYTRang, float &Azi, float &Pit, float *pMatixG);
};

#endif // CLASSCOORDINATE_H
