/*******************************************************************************************
 * FileProperties: 
 *     FileName: HQ_Echo_Sar.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/public/HQ_Echo_Sar.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef HQ_ECHO_SAR_H
#define HQ_ECHO_SAR_H
#include"globalRadarStruct.h"

#ifdef linux
    #define Q_DECL_EXPORT __attribute__((visibility))
    #define Q_DECL_IMPORT __attribute__((visibility))
#else
    #define Q_DECL_EXPORT __declspec(dllexport)
    #define Q_DECL_IMPORT __declspec(dllimport)
#endif

#if defined(_HQ_ECHO_SAR_LIBRARY)
#  define _HQ_ECHO_SAR_LIBRARY Q_DECL_EXPORT
#else
#  define _HQ_ECHO_SAR_LIBRARY Q_DECL_IMPORT
#endif

#pragma  pack(push, 1)
typedef struct _stSarParaCfg
{
    SimStruct			SimData;          // 仿真参数
    PLATFORM			platPara;
    RADAR				radarPara;
    ANTPARAM			antPara;
}stSarParaCfg;

typedef struct _stSarEchoPara2
{
    RADAR       ParamR[8];
    ANTPARAM    AntParam[8];
    SimStruct   SimData;
    PLATFORM	PlatForm[128];
    int			EchoType;  //0Ϊ�����壬1Ϊ�����ز�
    int         TarNum;
    double      *RadarAPC;
    double		*RadarVel;
    double      *SenceTarget;
}stSarEchoPara2;

#pragma  pack(pop)

class _HQ_ECHO_SAR_LIBRARY HQ_Echo_Sar
{
public:
    HQ_Echo_Sar();
    
    ~HQ_Echo_Sar();

    static HQ_Echo_Sar* Instance()
    {
        static HQ_Echo_Sar Obj;
        return &Obj;
    }
    
public:
    void InitQtLib(int argc, char *argv[]);

	int GetDeviceCount();

    bool InitCUDA(int Dev_ID);
    
    int SettingSarSence(stSarParaCfg *pStParaRedCfg,char *pTar_Rcs_Pos);

	void Gen_PRI(RADAR *ParamR, double*PRI);

    void GenSenceEcho(stSarEchoPara2 *pStSarEchoPara, stEchoData *pStEchoData);
    
	double* GetSenceTarget();
    
public:
	int m_TargetNum;
    
};

#endif // HQ_ECHO_SAR_H
