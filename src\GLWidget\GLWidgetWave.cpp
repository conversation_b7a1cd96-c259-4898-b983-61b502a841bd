﻿/*******************************************************************************************
 * FileProperties: 
 *     FileName: GLWidgetWave.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/GLWidget/GLWidgetWave.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include <QtGui>
#include <QtOpenGL>

#include <math.h>
#include "GLWidgetWave.h"

#if _MSC_VER>=1600
#pragma execution_character_set("utf-8")
#endif

GLWidgetWave::GLWidgetWave()
{
    pDrawAllFreqPara = NULL;
    pDrawAllFreqPara = new ClassDrawAllFreq;
    memset(pDrawAllFreqPara,0,sizeof(ClassDrawAllFreq));

    len = 0;
    uBw = 0;
    uFs = 0;
    paint = (float*)malloc(sizeof(float)*1024*1024);
}

GLWidgetWave::~GLWidgetWave()
{
    
}
//绘制收到的数据
void GLWidgetWave::drawIQWave()
{
    //没有收到数据时返回
    if(paint==NULL || len == 0)return;
    qglColor(Qt::red);
    glLineWidth(1.3);
    glLoadIdentity();

    //I绘图
    GLfloat gIMax = (float)*(paint);
    //Q绘图
    GLfloat gQMax = (float)*(paint + 1);
    for(uint i = 0 ; i < len ; i++)
    {
        //I
        gIMax = max((GLfloat)((float)*(paint + 2 * i)), gIMax);
        //Q
        gQMax = max((GLfloat)((float)*(paint + 2 * i + 1)), gQMax);
    }

    float AmpMaxTemp = max(gIMax,gQMax);
    g_AmpMax = max(AmpMaxTemp,25.f);
    float rxStrtDraw,rxEndDraw;
    rxStrtDraw = rxStrt;
    rxEndDraw = rxEnd;

    float ShowRange = rxEndDraw - rxStrtDraw;

    UINT DorwTotalDot = ShowRange * len;
    if(DorwTotalDot < 16)DorwTotalDot = 16;

    float hRatio = 1.99f / (DorwTotalDot - 1); //横坐标
    float vRatio = 1.8f / (g_AmpMax*2.f);	//纵坐标   max(abs(gIMax),abs(gIMin))

    int StrtDrawIndex = rxStrtDraw * len;
    for(int Chan = 0; Chan < 2;Chan++)
    {
        int OFFSET = Chan;
        glBegin(GL_LINE_STRIP);
        GLfloat x,y;
        if(Chan == 0)glColor3f(182.f/255,92.f/255,56.f/255);
        if(Chan == 1)glColor3f(20.f/255,104.f/255,177.f/255);
        for(uint i = StrtDrawIndex ; i < StrtDrawIndex + DorwTotalDot; i++)
        {
            x = hRatio*(i - StrtDrawIndex)  - 0.995f ;
            y = ((float)*((paint + 2 * i + OFFSET)))* vRatio;
            glVertex2f(x,y);
        }
        glEnd();
    }
    //绘制鼠标标记矩形区域
    if(mouseLeftDown)
    {
        drawRect(strtPointDraw,endPointDraw,pSize);
    }

}

void GLWidgetWave::paintGL()
{
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    //绘制收到数据
    drawIQWave();
    //画网格
    drawGridLine(5,15,2,GL_LINE_STIPPLE);
    //坐标
    float RangeTime  = (float)len/m_Sample*1e3;
    float strtTime   = rxStrt*RangeTime;
    float stopTime   = (1.f-rxEnd)*RangeTime;
    font.setFamily("微软雅黑");font.setPointSize(10);
    drawGridCorrd(strtTime, stopTime, -g_AmpMax, g_AmpMax, 15, 5,font);
    //title
    font.setFamily("微软雅黑");font.setPointSize(13);
    glColor3f(0.85f,0.85f,0.85f);
    if(m_PlotMode == 2)
        SetWidgetTitle(-0.1f, 1.7/2,QString("中频信号时域波形(ms-Amp)"),font);
    else
        SetWidgetTitle(-0.1f, 1.7/2,QString("时域图(ms-Amp)"),font);
}

void GLWidgetWave::slotWidgetWave(void *p)
{
    stWaveShow *pStWaveShow = (stWaveShow*)p;
    //获取IQ数据个数
    len = pStWaveShow->uSigDot;
    if(pStWaveShow->uDataType == 8){
        float *pFloat = (float*)pStWaveShow->Buffer;
		for (int i = 0; i < len * 2; i++){
            paint[i] = pFloat[i];
        }
    }
    else if(pStWaveShow->uDataType == 4){
        short *pShort = (short*)pStWaveShow->Buffer;
		for (int i = 0; i < len * 2; i++){
            paint[i] = pShort[i];
        }
    }

    m_Sample = pStWaveShow->ullSample*1e-3;
    if (m_bState == true)
        updateGL();
}
