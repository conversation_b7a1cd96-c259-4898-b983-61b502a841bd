/*******************************************************************************************
 * FileProperties: 
 *     FileName: WidgetShow.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/WidgetShow.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "WidgetShow.h"
#include "ui_WidgetShow.h"
#include <QSplitter>

WidgetShow::WidgetShow(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::WidgetShow)
{
    ui->setupUi(this);

    pGLWidgetWaveSend = new GLWidgetWave;
    pGLWidgetWaveEcho = new GLWidgetWave;

    QVBoxLayout *pQVBoxLayout = new QVBoxLayout;
	QSplitter *pQSplitter = new QSplitter(Qt::Vertical);
	pQSplitter->addWidget(pGLWidgetWaveSend);
	pQSplitter->addWidget(pGLWidgetWaveEcho);
	pQVBoxLayout->addWidget(pQSplitter);
    pQVBoxLayout->setMargin(0);
    ui->frameMain->setLayout(pQVBoxLayout);
}

WidgetShow::~WidgetShow()
{
    delete ui;
}

void WidgetShow::slotWaveShow(void *p)
{
    stWaveShow *pStWaveShow = (stWaveShow*)p;
    if(pStWaveShow->bDecodeType == DecodeType_SendWave){
        pGLWidgetWaveSend->slotWidgetWave(p);
    }
    else if(pStWaveShow->bDecodeType == DecodeType_EchoWave){
        pGLWidgetWaveEcho->slotWidgetWave(p);
    }
}

void WidgetShow::on_pushButtonRefresh_clicked()
{
    emit sendASKWaveDate();
}
