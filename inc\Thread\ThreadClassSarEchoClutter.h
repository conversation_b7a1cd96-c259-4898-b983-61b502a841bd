/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassSarEchoClutter.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassSarEchoClutter.h $
 *         $Author: weiyingzhen $
 *         $Revision: 503 $
 *         $Date: 2025-05-27 10:41:15 $
*******************************************************************************************/
#ifndef THREADCLASSSARECHOCLUTTER_H
#define THREADCLASSSARECHOCLUTTER_H

#include <QObject>

#include<curand.h>
#include<cufft.h>

#include"MyMalloc.h"
#include"MyMallocCUDA.h"
#include"_stumempool.h"
#include"ClassClutterRcsRead.h"
#include"ClassCoorDinate.h"
#include"ClassRcsRead.h"
#include "WhiteCalGlobel.h"


class ThreadClassSarEchoClutter : public QObject, MyMalloc, MyMallocCUDA,_StuMemPool
{
    Q_OBJECT
public:
    explicit ThreadClassSarEchoClutter(int DevID = -1,int buffer_size = 0,char *d_Buffer = nullptr,QObject *parent = 0);
    ~ThreadClassSarEchoClutter();

	void GenSenceEcho(void *p, stEchoData *pStEchoData);
	//加载目标RCS系数
    void InitClutterRcsPara(void *p);
	//加载遮挡目标系数
	void InitTarClutterPara(void *p);
public:
    //初始化GPU设备工作参数
	bool InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic);
	//************杂波系数提取************//
	void ExtractClutterScatterCoef(Target *pTarget, PLATFORM *pPlatForm, int ClutterDot, int SeaStateClass, int SaveNr, float PitAngle, float AziAngle,
									double *Main3dBlobeWidth, double Fs);
signals:


public slots:


private:
    //根据海况和擦地角遍历数据文件，提取最终的面目标杂波散射数据
	stClutterPitLink* CalClutterRcs(int hk, float pit, stClutterSeaStateLink* pSt);
	ClutterRoeData* CalTarClutter(int iTarNo, stTarClutterLink* pSt);

	//根据擦地角计算散射系数
	float CalSigma(float cdj);
    
private:
    int m_DevID;
	char *d_BufferFather, *d_BufferInit;
	long long BufferInit_SizeTotal, BufferInitTarRcs_Size,BufferInit_Cur;
	float MaxX, MaxZ, MinX, MinZ;
	float *dev_Target_ALL1, *dev_Target_ALL2, *dev_Target_ALL3, *dev_Target_ALL4, *dev_Target_ALL5;
     
	float sigmaS; //幅度加权 海面散射系数*照射面积
    
	cudaEvent_t e_start, e_stop, e_start2, e_stop2;
	
    stClutterSeaStateLink *m_StClutterSeaStateLinkHead;
	stTarClutterLink      *m_stTarClutterLink;
	char				*m_d_BufferTarRCS, *m_pBufferTarRCS;
	unsigned long long	m_BytesOffsetTarRCS;

    ClassCoorDinate *pClassCoorDinate;
    int             SeaStateClass;
	float			m_Delat_X;
	float			m_Delat_Z;
	int				m_SacreNum;

	curandGenerator_t   gen_curand;
};

#endif // THREADCLASSSARECHO_H
