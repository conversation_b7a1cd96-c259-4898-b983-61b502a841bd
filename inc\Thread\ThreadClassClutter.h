/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassClutter.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassClutter.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef THREADCLASSCLUTTER_H
#define THREADCLASSCLUTTER_H

#include <QObject>

#include"MyMalloc.h"
#include"MyMallocCUDA.h"
#include "_stumempool.h"

#include "WhiteCalGlobel.h"

#include <cufft.h>
#include <curand.h>

class ThreadClassClutter : public QObject,MyMalloc,MyMallocCUDA,_StuMemPool
{
    Q_OBJECT
public:
    explicit ThreadClassClutter(int buffer_size = 0,char *d_Buffer = nullptr,int DevID = -1,QObject *parent = 0);
    ~ThreadClassClutter();
    
public:
    //初始化cuda参数
    bool InitCUDAPara();
    //初始化GPU设备工作参数
	bool InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer);
    //杂波生成顶层函数
	template <class T, class T2, class T3, class T4>
	void ClutterGen(SimStruct *SimData, RADAR *G_Radar, PLATFORM *RadarPos, ANTPARAM *AntParam, T *RadarAPC, T2 *PRI, int flag, T3 *BaseSignal,
					T4 *SumCluDataPolar1, T4 *AziCluDataPolar1, T4 *EleCluDataPolar1, T4 *ExCluDataPolar1);
    //*****统计模型杂波*******//
	template <class T,class T2>
	void StatisticalClutter(SimStruct *SimData, RADAR *G_Radar, PLATFORM *RadarPos, ANTPARAM *AntParam, T2 *BaseSignal, 
							T *SumCluDataPolar1, T *AziCluDataPolar1, T *EleCluDataPolar1, T *ExCluDataPolar1);
    //**********实时模型杂波*********//
	template <class T, class T2, class T3, class T4>
	void RealCultter(SimStruct *SimData, RADAR *G_Radar, PLATFORM *RadarPos, ANTPARAM *AntParam, T *RadarAPC, T2 *PRI, T3 *BaseSignal,
						T4 *SumCluDataPolar1, T4 *AziCluDataPolar1, T4 *EleCluDataPolar1, T4 *ExCluDataPolar1);
    //生成杂波主程序--实时
	template <class T, class T2, class T3, class T4, class T5, class T6>
	void GenCultter_GPU(SimStruct *SimData, RADAR *G_Radar, SCENE *G_Scene, CULPLAT *G_Plat, T *BaseSignal, T2 AngleBound[2],
						T2 AngSampSpace[2], T3 *G_Matrix, T4 *AntennaPattern, T5 *Cultter, T6 *RadarAPC, T6 *PRI);
    //生成杂波主程序--实时
	template <class T, class T2, class T3, class T4, class T5, class T6>
	void RangeAngle(SimStruct *SimData, RADAR *G_Radar, SCENE *G_Scene, CULPLAT *G_Plat, T *BaseSignal, T2 AngleBound[2], T2 AngSampSpace[2],
						int StartID, T3 APC[3], T3 V[3], T4 *Cultter, cufftHandle plan, T5 *G_Matrix, T6 *AntennaPattern);

	//K分布杂波生成
	template <class T, class T2, class T3, class T4, class T5, class T6, class T7>
	void KDisClutterGen(int Num, T *dev_Pf, T2 mu, T3 Shape, T4 Scale, T5 viu, T6 *dev_Cos_val, T6 *dev_Sin_val, double Range, double *dev_param, T7 *dev_KDisData, T7 *dev_KDisData_azi, T7 *dev_KDisData_pit);
	//威布尔杂波生成
	template <class T, class T2, class T3, class T4, class T5>
	void WeibullClutterGen(int Num, T *dev_Pf, T2 mu, T3 Shape, T4 Scale, double Range, double *param, T5 *WeibullData, T5 *WeibullData_azi, T5 *WeibullData_pit);
	//对数正态杂波生成
	template <class T, class T2, class T3, class T5>
	void LogNormClutterGen(int Num, T *dev_Pf, T2 mu, T3 sigma_m, T5 *LogNormData);
	//瑞利杂波生成
	template <class T, class T2, class T5>
	void RelayClutterGen(int Num, T *dev_Pf, T2 mu, double Range, double *param, T5 *RelayData, T5 *RelayData_azi, T5 *RelayData_pit);

	//杂波生成
	void GenClutter(void *p, stEchoData *pStEchoData);
signals:
	void sendCluDataFormat(void*p);
	void sendSettingClutterSence(void*p);

public slots:
    //杂波生成槽函数
    void slotClutterGen(void *p);
	void slotSettingClutterSence(void *p,void *pc);
    
private:
    int m_DevID;
	char *d_BufferFather, *d_BufferInit;
	long long BufferInit_SizeTotal, BufferInit_Cur;
    
	curandGenerator_t gen_curand;
    cufftHandle plan;			//cuFFT的句柄

	cufftHandle plan_1k;
	cufftHandle plan_2k;
	cufftHandle plan_4k;
	cufftHandle plan_8k;
	cufftHandle plan_16k;
	cufftHandle plan_32k;
	cufftHandle plan_64k;
	cufftHandle plan_128k;
	cufftHandle plan_256k;
	cufftHandle plan_512k;
    
	int CosValueLen;//三角函数表长
	float *dev_CosValue, *dev_SinValue;//三角函数查表显存
	float *m_CosValue, *m_SinValue;
	float *dev_SumAntennaFunction, *dev_AziSubAntennaFunction, *dev_PitSubAntennaFunction;
	float *pSumAntennaFunction, *pAziSubAntennaFunction, *pPitSubAntennaFunction;
	SimStruct *pSimData;


	cufftHandle CufftPlanCheck(int fftDot);


};

#endif // THREADCLASSCLUTTER_H
