﻿/*******************************************************************************************
 * FileProperties: 
 *     FileName: gGLWidgetInstanceBWFreq.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/GLWidget/GLWidgetInstanceBWFreq.h $
 *         $Author: yening $
 *         $Revision: 212 $
 *         $Date: 2025-02-19 09:34:25 $
*******************************************************************************************/
#ifndef GLWIDGETINSTANCEBWFREQ_H
#define GLWIDGETINSTANCEBWFREQ_H
#ifdef linux
#include<GL/gl.h>
#endif
#include <QGLWidget>
#include <QTimer>
#include"_GLWidgetBase.h"
#include"_stumempool.h"


class GLWidgetInstanceBWFreq :public _GLWidgetBase,_StuMemPool
{
    Q_OBJECT
public:
    explicit GLWidgetInstanceBWFreq();
    ~GLWidgetInstanceBWFreq();
    //绘制收到的数据
    void glDrawSpec();
protected:
    void paintGL();
public:

    
    uint uBw,uFs;
    float gMax,gMin;
public:

    
public slots:
    void onSendInstanceFreShow(void *p);
};


#endif // GLWIDGETINSTANCEBWFREQ_H

