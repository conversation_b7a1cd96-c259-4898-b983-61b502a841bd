﻿/*******************************************************************************************
 * FileProperties: 
 *     FileName: WhiteCalGlobel.h
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/WhiteCalGlobel.h $
 *         $Author: xiayuhui $
 *         $Revision: 369 $
 *         $Date: 2025-04-03 18:50:07 $
*******************************************************************************************/
#ifndef WHITECALGLOBLE
#define WHITECALGLOBLE

#define _CRT_SECURE_NO_WARNINGS

#include <stdio.h>
#include <stdlib.h>
//#include <Windows.h>
//#include <SDKDDKVer.h>
//#include <tchar.h>
#include <math.h>
#include <time.h>

#include"globalRadarStruct.h"
#include"globalExternalStruct.h"
#include"globalJamStruct.h"

using namespace std;



#define QUANTIFY_LEN	32768					//??????2^15
#define PI				3.141592653589793		//????pi
#define PI_2			6.283185307179586
#define C_				299792458.0
#define SPEEDLIGHT		299792458.0
#define BUFFER_LEN		65536
#define k0				1.3806488e-23			//????????????
#define MINEQUERR       1.0e-8					//????????С????
#define MAXTRACKNUM     10						//???????????????????????????????????????????? ??WS??

#define TREADSPERBLOCK 32
#define TARGET_NUM		8	//???????????
#define RANGEGATE_NUM   8	//??????????????

#define BIT8			8.0
#define BIT14           14.0
#define BIT16           16.0
#define TARGET_LEN		5

#define TILE_DIM 32       // 定义每个块的维度
#define BLOCK_ROWS 8      // 定义每块中的行数

enum DecodeWhiteType
{
    DecodeWhiteTypeUnknown = 0,

    DecodeDYT   = 0x1001,
    DecodeTarget   = 0x1002,
    DecodeCorRef  = 0x1003,
    DecodeChaff  = 0x1004,
    DecodeActiveJamming    = 0x1005,
    DecodeSeaState = 0x1006,
};
enum ElementType
{
    Missile = 0,//导弹
    Ship        //船
};
enum TriggerType	//??????????
{
	ConstantPRF = 0,
	StaggeredPRF,
	JitteringPRF,
	SlipPRF
};//???????????β????????????????

enum SignalType
{
	Pulse = 0,
	Chirp=4,
	FSK,
	NChirp,
	BPSK,
	QPSK,
	BPSK_FSK,
	FreqAgile,
	ContinueCos,
	Sawtooth,
	Triangle,
	SinCos,
	UnKnown
};//0??????????? 1????????? 2??????????? 3??λ??????? 4??????? 5????????????? 6????????????? 7????????????? 8?????????

typedef struct SCENE
{
	int Na;			//??λ??????(????????
	int Nr;		//??????????

	double Pix_N;	//?????????????
	double Pix_E;	//?????????????
	int SceneType;	//???????
}SCENE;


typedef struct CULPLAT
{
	double MaxRange;	//???????t???
	double Pos_North;	//?????????λ??,??
	double Pos_Sky;		//?????????λ??,??
	double Pos_East;	//?????????λ??,??
	double V_North;		//???????????????
	double V_Sky;		//???????????????
	double V_East;		//???????????????
	double A_North;		//?????????????????
	double A_Sky;		//?????????????????
	double A_East;		//?????????????????
	double A;			//???????
	double V;			//?????
}CULPLAT;

enum AntennaType
{
	Line = 0,
	RectAngle,
	CircleBound,
	Hexagon,
	Circle,
	ConcentircCircle,
	ArbitraryArray
};//????????,???????С?????????С??????????С???????С????????С?????????

enum SubAntType
{
	ArryElement = 0,
	SubArray
};//?????????

enum BalJamType
{
	Block = 0,
	Sweep,
	PulseJam,
	CombSpec
};	//?????????????????????????????????????????????

enum GauseType
{
	Gauss = 0,
	GaussFreq
};//???????????

enum SweepType
{
	TriangleSweep = 0,
	CosSweep,
	SawtoothSweep
};//????????????????????????

enum PulJamType
{
	SynPulse = 0,
	RanPulse
};//??????壬????????

typedef struct
{
	//int PowerType;	//0??????1?????????
	//double Power;	//?????
	int RcsType;	//????????,0????????1-4?????????1-3??
	double Sigma0;	//???RCS
	double SigmaRe;	//RCS???
	double SigmaIm;	//RCS?鲿
}RCS;	//RCS????

//???????????
typedef struct _stSarEchoPara
{
	int RADARNo;
	RADAR       ParamR[8];
    ANTPARAM    AntParam[8];
    SimStruct   SimData;
	PLATFORM	PlatForm[128];
	int         TarNum;
	Target		pCorRef[8];
	int			CorRefNum;
	Target		pTarget[8];
	int			EchoType;  //0??????壬1????????
	unsigned long long	FrameSerial;//大帧流水号
	int			childFrameSerial;//子帧编号 从1开始编号
	int			childFrameNum;//子帧总数
    int         SeaStateClass;//海况等级
	float		SeaMoveX;
	float		SeaMoveZ;
	double		BeamX;
	double		BeamY;
	double		BeamZ;
    double      *RadarAPC;
	double		*RadarVel;
    double      *SenceTarget;
}stSarEchoPara;
//??????????????
typedef struct _stJamEchoPara
{
	PLATFORM			Target[8];
	PLATFORM			PlatForm[128];		//???λ??	 ???????????
    JAM_PARAM			Jammer[8];		//?????????
	int					EchoType;
	JAM_SUPPRESS_PARAM	pJAM_SUPPRESS_PARAM[8];
	JAM_DECEPTION_PARAM pJAM_DECEPTION_PARAM[8];
	int					JamNum;
    RADAR				ParamR;
    ANTPARAM			AntParam;
    SimStruct			SimData;
	double				*RadarAPC;
	double				*RadarVel;
	double				*SenceTarget;
    unsigned long long	FrameSerial;//大帧流水号
    int					childFrameSerial;//子帧编号 从1开始编号
    int					childFrameNum;//子帧总数
    PLATFORM			RadarPos;			//???λ??   ???????8??
	Complexf			*pSignalSend;
}stJamEchoPara;

typedef struct _stChaffEchoPara
{
    PLATFORM    Target[8];
    PLATFORM    PlatForm[128];		//???λ??	 ???????????
    JAM_PARAM   Jammer[8];		//?????????
    int			EchoType;
    JAM_SUPPRESS_PARAM pJAM_SUPPRESS_PARAM[8];
    int         JamNum;
    RADAR       ParamR[8];
    ANTPARAM    AntParam[8];
    SimStruct   SimData;
    double      *RadarAPC;
    double		*RadarVel;
    double      *SenceTarget;
    unsigned long long	FrameSerial;//大帧流水号
    int			childFrameSerial;//子帧编号 从1开始编号
    int			childFrameNum;//子帧总数
    PLATFORM	RadarPos;			//???λ??   ???????8??
}stChaffEchoPara;
//???????????
typedef struct _stClutterEchoPara
{
    RADAR       ParamR[8];
    ANTPARAM    AntParam[8];
    SimStruct   SimData;
    PLATFORM    RadarPos[128];			//???λ??   ???????8??
    double      *RadarAPC;
    double      *SenceTarget;
    double      *PRI;
}stClutterEchoPara;

typedef struct _stAntennaPara
{
	RADAR		RadarParam;
	ANTPARAM	AntParam;
	SimStruct	SimData;
}stAntennaPara;

typedef struct _stWhitePara
{
    RADAR		RadarParam;
    SimStruct	SimData;
}stWhitePara;

typedef  struct _stTestPara
{
    int SimMode;
    int CPITime;
    int runState;
    int TypeSarEcho; //0=parasetting 1=wave
}stTestPara;

typedef struct _stResourceScheduingPara
{
    unsigned long long  FrameSerial;
    unsigned int        childFrameNum;
    unsigned int        childFrameSerial;
    unsigned int        pulseStartIndex;
	double				Rmin;//波们前沿
	double				WaveGateWidth;//波门宽度
	long long			curMSecsSinceEpoch;
}stResourceScheduingPara;

typedef struct _TarPos
{
    double PosX;//北天东坐标系下 x，y，z位置   对应笛卡尔坐标系 y，z，x
    double PosY;
    double PosZ;
}TarPos;
typedef struct _TarVel
{
    double VelX;//北天东坐标系下 x，y，z位置   对应笛卡尔坐标系 y，z，x
    double VelY;
    double VelZ;
}TarVel;
typedef struct _pGLDrawStruct
{
    ElementType pElement;//元素类型
    int         ElementNum;//元素编号
    TarPos      pTarPos;//目标位置
    TarVel      pTarVel;//目标速度
    double      BeamPitch;//波束俯仰角
    double      BeamAzimuth;//波束方位角
    double      BeamWidth;//波束宽度
}pGLDrawStruct;

typedef struct ShipTarget
{
    int		ModeNo;			//模型编号
    double	TargetX;		//目标位置x
    double	TargetY;		//目标位置y
    double	TargetZ;		//目标位置z
    double	TargetVX;		//目标速度x
    double	TargetVY;		//目标速度y
    double	TargetVZ;		//目标速度z
    double	TargetAccX;		//目标加速度x
    double	TargetAccY;		//目标加速度y
    double	TargetAccZ;		//目标加速度z
    int    CorRefNum;
    CorRef pCorRef[8];
    int    ChaffNum;
    Chaff pChaff[8];
}ShipTarget;//目标
typedef struct UpdateMode
{
    int DYTNum;
    DYTPara mDYTPara[8];
    int TarNum;
    Target pTarget[8];
}UpdateMode;//目标


//??????????????????
void AntennaSimRe(RADAR *RadarParam, ANTPARAM *AntParam, SimStruct *SimData);

//????APC????
void GenAPC(int Na, double Prf, PLATFORM *Target, double *APC);

//PRT????????
void GenPRI(RADAR *ParamR, double *PRI);

//????????????????
extern double ExpRand(double Lambda);
//???????3*3
extern void CoordMulTrans(double MatrixA[9], double MatrixB[9], double MatrixOut[9]);
//????????3*3
extern void InverseMatrix(double MatrixIn[9], double MatrixOut[9]);
//????????????????????????WS??
extern void getTimes(int l, int m, int n, double **MatrixIn1, double **MatrixIn2, double **ans);
//???????????? (n????? WS)
extern double getA(int n, double **Matrix);
//?????????? (n????? WS)
extern void getAStar(int n, double **MatrixIn, double **MatrixOut);
//????????((n????? WS))
extern void getInverse(int n, double **MatrixIn, double **MatrixOut);

/******************
*?????????????
*data??????????
*Len:????????
*************/
extern void GetMax(float* Data, int Len, double *MaxData, int *MaxID);

/******************
*??????????С?????ID
*data??????????
*Len:????????
*************/
extern void GetMin(double* Data, int Len, double *MinData, int *MinID);

/******************
*???????????
*data??????????
*Len:????????
*************/
extern double GetVar(double* Data, int Len);

/******************
*?????
*data??????????
*Len:????????
*************/
extern double GetMean(double* Data, int Len);

///******************
//*??float????????
//*  ????
//*      double *Source??????????????
//*      char * Des????????????
//*      int Len:????????
//*************/
//extern void DoulbeArrayToDisk(double *Source, char * Des, int Len);
//extern void DoulbeArrayToDisk2(double *Source, char * Des, int Len);
///******************
//*FFT??IFFT
//*real_T *x??????/???? ???
//*real_T *y??????/???? ?鲿
//*int flag??0?fft??1?ifft
//*************/
//extern void FFT(double *x, double *y, int n, int flag);

/* Function: ????????????????
* E:?????Var???????
*/
extern double randn(double E, double Var);

///******************
//*????????
//*double data:?????????????????
//*************/
//extern int Round(double data);

//extern void WriteToDisk(void *pBuffer, char *pFile, int bytes);

#endif
