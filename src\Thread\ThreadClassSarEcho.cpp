/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassSarEcho.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassSarEcho.cpp $
 *         $Author: weiyingzhen $
 *         $Revision: 503 $
 *         $Date: 2025-05-27 10:41:15 $
*******************************************************************************************/
#include "ThreadClassSarEcho.h"
#include<QThread>
#include <QDateTime>

#include <cuda_runtime_api.h>
#include<cuda_runtime.h>
#include<cuda_device_runtime_api.h>
#include<cufft.h>
#include<curand.h>

#include <QFile>
#include <QDebug>

#include "KernelSar.h"
#include "KernelPublic.h"

ThreadClassSarEcho::ThreadClassSarEcho(int DevID,int buffer_size,char *d_Buffer,QObject *parent) : QObject(parent)
{
	SetMemPoolSrcFileName((char*)"ThreadClassSarEcho", sizeof("ThreadClassSarEcho"));
	_DefineMemPool(stTarLink, 50);
    //分配CPU伪内存池空间
    InitMyMalloc(64*1024*1024);
	m_DevID				= -1;
	m_StTarLink = (stTarLink*)_MallocMemObj(stTarLink); m_StTarLink->init();
    pClassCoorDinate = new ClassCoorDinate;

}

ThreadClassSarEcho::~ThreadClassSarEcho()
{

}
//初始化GPU设备工作参数
bool ThreadClassSarEcho::InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic)
{
	cudaSetDevice(DevID);
	m_BytesOffsetTarRCS		= 0;
	BufferInit_Cur			= 0;
	m_BytesOffsetTarRCS		= 0;
    if(d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
        m_DevID				= DevID;
		m_d_BufferTarRCS	= d_Buffer;
		d_BufferFather		= d_BufferPublic;
        //分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, 5.0*1024*1024*1024);
    }
    else{//作为独立的线程使用，单独使用一个GPU卡
        m_DevID         = -1;
        d_BufferFather  = nullptr;
        //分配GPU伪内存池空间
		cudaMalloc((void**)&m_d_BufferTarRCS, BufferInitTarRcs_Size);
        cudaMalloc((void**)&d_Buffer,buffer_size);
		d_BufferFather = d_Buffer;
		InitMyMallocCUDA(d_BufferFather, buffer_size);
    }
	m_pBufferTarRCS = (char*)malloc(64 * 1024 * 1024.f);
	memset(m_pBufferTarRCS, 0,64 * 1024 * 1024.f);

    return true;
}
//初始化cuda参数
bool ThreadClassSarEcho::InitCUDAPara()
{
	cudaSetDevice(m_DevID);
    
    return true;
}
//加载目标RCS系数
void ThreadClassSarEcho::InitTarRcsPara(void *p)
{
	cudaSetDevice(m_DevID);
	stTarPitLink *pStTarPitLinkRecv = (stTarPitLink*)p;

	stTarPitLink *pStTarPitLinkHead = (stTarPitLink*)_MallocMemObj(stTarPitLink); pStTarPitLinkHead->init();

	float *pTemp = (float*)m_pBufferTarRCS;

	stTarPitLink *pStTarPitLinkRecvNode = pStTarPitLinkRecv;
	while (pStTarPitLinkRecvNode->next)
	{
		pStTarPitLinkRecvNode = pStTarPitLinkRecvNode->next;
		stTarPitLink *pStTarPitLinkNode		= (stTarPitLink*)_MallocMemObj(stTarPitLink); pStTarPitLinkNode->init();
		stTarAziLink *pStTarAziLinkHead		= (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkHead->init();
		pStTarPitLinkNode->pStTarAziLink	= pStTarAziLinkHead;

		stTarAziLink *pStTarAziLinkRecv		= pStTarPitLinkRecvNode->pStTarAziLink;
		while (pStTarAziLinkRecv->next)
		{
			pStTarAziLinkRecv = pStTarAziLinkRecv->next;
			stTarAziLink *pStTarAziLinkNode = (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkNode->init();

			int Bytes = (pStTarAziLinkRecv->pointNum*sizeof(float) + 16) / 16 * 16;

			pStTarAziLinkNode->pMissileDataGPU.x = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.x, pStTarAziLinkRecv->pMissileData->x, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.y = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.y, pStTarAziLinkRecv->pMissileData->z, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.z = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.z, pStTarAziLinkRecv->pMissileData->y, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.rcs = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			for (int i = 0; i < pStTarAziLinkRecv->pointNum; i++){ pTemp[i] = pStTarAziLinkRecv->pMissileData->rcs[i] * pStTarAziLinkRecv->pMissileData->rcs[i]; }
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.rcs, pTemp, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.phi = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			for (int i = 0; i < pStTarAziLinkRecv->pointNum; i++){ pTemp[i] = (pStTarAziLinkRecv->pMissileData->phi[i] + 180) / 360; }
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.phi, pTemp, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);

			pStTarAziLinkNode->pointNum = pStTarAziLinkRecv->pointNum;
			pStTarAziLinkNode->pit = pStTarAziLinkRecv->pit;
			pStTarAziLinkNode->azi = pStTarAziLinkRecv->azi;
			AddLink(pStTarAziLinkHead, pStTarAziLinkNode);
		}
		AddLink(pStTarPitLinkHead, pStTarPitLinkNode);
	}
	stTarLink *pStTarLink = (stTarLink*)_MallocMemObj(stTarLink); pStTarLink->init();
	pStTarLink->pStTarPitLink = pStTarPitLinkHead;
	pStTarLink->iTarNo = pStTarPitLinkRecv->iTarNo;
	AddLink(m_StTarLink, pStTarLink);
}
//单脉冲模式生成回波
void ThreadClassSarEcho::ExtractTarScatterCoef(Target *pTarget, PLATFORM *pPlatForm, SimStruct *SimData)
{
	cudaError_t Err;
	float *pTempF = (float*)_Malloc(sizeof(float) * 131072);
    float *pTemp = (float*)_Malloc(sizeof(float) * 32);
    float *pMatixG		= (float*)pTemp;
    float *dev_Temp     = (float*)_MallocCUDA(sizeof(float) * 32);
    float *dev_MatixG	= (float*)(dev_Temp);
    float *dev_origin	= (float*)(dev_Temp + 16);

    memset(pTemp,0,sizeof(float) * 32);
    cudaMemset(dev_Temp,0,sizeof(float) * 32);

	pClassCoorDinate->_Release_Malloc();
	for (int frame = 0; frame < SimData->SenceTarNum; frame++)
	{
		//提取目标RCS链表
		int iTarNo = pTarget[frame].ModeNo;
		stTarPitLink *pStTarPitLink = nullptr;
		stTarLink *pStTarLinkTemp = m_StTarLink;
		bool IsTarExit = false;
		while (pStTarLinkTemp->next)
		{
			pStTarLinkTemp = pStTarLinkTemp->next;
			if (pStTarLinkTemp->iTarNo == iTarNo)
			{
				pStTarPitLink = pStTarLinkTemp->pStTarPitLink;
				IsTarExit = true;
				break;
			}
		}
		if (IsTarExit == false){
			qDebug() << "GPU:" << m_DevID << __LINE__ << __FUNCTION__ << "-----------------------------------IsTarExit: false!" << iTarNo;
			continue;
		}
		if (m_FdFlag == 1){
			dev_Target_ALL1 = m_pStEchoData->dev_Target_ALL1_Fd + m_pStEchoData->pStTarInfoFd.ScareNum;
			dev_Target_ALL2 = m_pStEchoData->dev_Target_ALL2_Fd + m_pStEchoData->pStTarInfoFd.ScareNum;
			dev_Target_ALL3 = m_pStEchoData->dev_Target_ALL3_Fd + m_pStEchoData->pStTarInfoFd.ScareNum;
			dev_Target_ALL4 = m_pStEchoData->dev_Target_ALL4_Fd + m_pStEchoData->pStTarInfoFd.ScareNum;
			dev_Target_ALL5 = m_pStEchoData->dev_Target_ALL5_Fd + m_pStEchoData->pStTarInfoFd.ScareNum;
		}

        //取出对应角度上的散射系数
        float *pTarDYTRang = (float*)_Malloc(3 * sizeof(float));
        pTarDYTRang[0] = pPlatForm[0].NorthPos;//
        pTarDYTRang[1] = pPlatForm[0].SkyPos;//
        pTarDYTRang[2] = pPlatForm[0].EastPos;//
        //计算目标旋转矩阵
        float pTarVel[3] = { 0 };
        pTarVel[0] = pTarget[frame].TargetVX; pTarVel[1] = pTarget[frame].TargetVY; pTarVel[2] = pTarget[frame].TargetVZ;
        float *TarPoz = (float*)(pTemp + 16);
        TarPoz[0] = pTarget[frame].TargetX;
        TarPoz[1] = pTarget[frame].TargetY;
        TarPoz[2] = pTarget[frame].TargetZ;

        float Azi = 0, pit = 0;
        pClassCoorDinate->ConvertAngleMatixG(pTarVel, TarPoz, pTarDYTRang, Azi, pit, pMatixG);
        stTarAziLink *pStTarAziLinkNode = CalTarRcs(abs(pit * 180 / PI), abs(Azi * 180 / PI), pStTarPitLink);

        //散射点从本体转到北天东
        cudaMemcpy(dev_Temp, pTemp, sizeof(float) * 32, cudaMemcpyHostToDevice);
        Err = cudaGetLastError();
        pClassCoorDinate->CoorBoatToBtd_CUDA(pStTarAziLinkNode->pointNum, dev_MatixG, dev_origin,
                                             pStTarAziLinkNode->pMissileDataGPU.x, pStTarAziLinkNode->pMissileDataGPU.y, pStTarAziLinkNode->pMissileDataGPU.z,
											 dev_Target_ALL1 + m_SacreNum, dev_Target_ALL2 + m_SacreNum, dev_Target_ALL3 + m_SacreNum);

		cudaMemcpy(dev_Target_ALL4 + m_SacreNum, pStTarAziLinkNode->pMissileDataGPU.rcs, pStTarAziLinkNode->pointNum*sizeof(float), cudaMemcpyDeviceToDevice);
		cudaMemcpy(dev_Target_ALL5 + m_SacreNum, pStTarAziLinkNode->pMissileDataGPU.phi, pStTarAziLinkNode->pointNum*sizeof(float), cudaMemcpyDeviceToDevice);

		Err = cudaGetLastError();
		if (m_FdFlag == 0){
			m_SacreNum += pStTarAziLinkNode->pointNum;
		}
		else{
			int &TarCnt = m_pStEchoData->pStTarInfoFd.TarNum;
			m_pStEchoData->pStTarInfoFd.pTarMoveInfo[TarCnt].offsetDot = m_pStEchoData->pStTarInfoFd.ScareNum;//偏移点数
			m_pStEchoData->pStTarInfoFd.pTarMoveInfo[TarCnt].ScareDot = pStTarAziLinkNode->pointNum;//目标散射点数
			m_pStEchoData->pStTarInfoFd.pTarMoveInfo[TarCnt].Vx = pTarVel[0];//目标速度
			m_pStEchoData->pStTarInfoFd.pTarMoveInfo[TarCnt].Vy = pTarVel[1];
			m_pStEchoData->pStTarInfoFd.pTarMoveInfo[TarCnt].Vz = pTarVel[2];
			TarCnt++;
			m_pStEchoData->pStTarInfoFd.ScareNum += (pStTarAziLinkNode->pointNum / 16 + 1) * 16;
		}
	}

}

void ThreadClassSarEcho::GenSenceEcho(void *p,stEchoData *pStEchoData)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	m_pStEchoData	= pStEchoData;
    stSarEchoPara   *pStSarEchoPara = (stSarEchoPara*)p;
    SimStruct       *SimData        = (SimStruct*)&pStSarEchoPara->SimData;
    PLATFORM        *pPlatForm      = pStSarEchoPara->PlatForm;
    Target          *pTarget        = pStSarEchoPara->pTarget;

	//兼容调制脉内多普勒
	m_FdFlag = pStEchoData->FdFlag;
	if (m_FdFlag == 0){
		dev_Target_ALL1 = pStEchoData->dev_Target_ALL1 + pStEchoData->SacreNum;
		dev_Target_ALL2 = pStEchoData->dev_Target_ALL2 + pStEchoData->SacreNum;
		dev_Target_ALL3 = pStEchoData->dev_Target_ALL3 + pStEchoData->SacreNum;
		dev_Target_ALL4 = pStEchoData->dev_Target_ALL4 + pStEchoData->SacreNum;
		dev_Target_ALL5 = pStEchoData->dev_Target_ALL5 + pStEchoData->SacreNum;
	}

    //目标散射系数提取
    m_SacreNum = 0;
    ExtractTarScatterCoef(pTarget, pPlatForm,SimData);
	if (m_FdFlag == 0){
		pStEchoData->SacreNum += m_SacreNum;
	}
}




