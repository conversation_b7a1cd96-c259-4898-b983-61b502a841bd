/*******************************************************************************************
 * FileProperties: 
 *     FileName: HQ_Echo_Sar.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/public/HQ_Echo_Sar.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include <QDebug>
#include <QCoreApplication>
#include<thread>
#include<ctime>
#include<chrono>
#include <stdio.h>
#include<time.h>

#ifdef linux
    static pthread_t qt_thread_t;
#else
#include<windows.h>
#include<stdio.h>
#include<process.h>
#endif

#include "HQ_Echo_Sar.h"

#include "ThreadClassSarEcho.h"
#include "ThreadClassBase.h"

ThreadClassBase     *pThreadClassBase       = nullptr;
ThreadClassSarEcho  *pThreadClassSarEcho    = nullptr;

static int g_argc;
static char **g_argv;

static bool ThreadInitSuccess = false;

void initQTEventLoop(void *p)
{
    qDebug()<<"QT Thread Init:"<<QThread::currentThreadId();
    QCoreApplication app(g_argc,g_argv);

    pThreadClassBase    = new ThreadClassBase;
    pThreadClassSarEcho = new ThreadClassSarEcho;

	ThreadInitSuccess = true;

    printf("hello dll lib \n");
    app.exec();
}

HQ_Echo_Sar::HQ_Echo_Sar()
{
	m_TargetNum = 0;

}

HQ_Echo_Sar::~HQ_Echo_Sar()
{
    
}
void HQ_Echo_Sar::InitQtLib(int argc, char *argv[])
{
    g_argc = argc;
    g_argv = argv;
#ifndef linux
    printf("CreateThread HANDLE created \n");
    HANDLE hand = CreateThread(nullptr,0,(LPTHREAD_START_ROUTINE)initQTEventLoop,nullptr,(DWORD)nullptr,nullptr);
    printf("CreateThread HANDLE %d \n",hand);
#elif
    pthread_create(&qt_thread_t,nullptr,initQTEventLoop,nullptr);
#endif

}
int HQ_Echo_Sar::GetDeviceCount()
{
	int count = 0;
	cudaGetDeviceCount(&count);

	return count;
}
bool HQ_Echo_Sar::InitCUDA(int Dev_ID)
{
	while (!ThreadInitSuccess);

    long long buffer_sizes = 1*1024*1024*1024;
    char *d_Buffer = nullptr;
    cudaSetDevice(Dev_ID);
    cudaMalloc((void**)&d_Buffer,buffer_sizes);
    bool ret = pThreadClassSarEcho->InitDeviceWorkPara(Dev_ID,buffer_sizes,d_Buffer);
	ret = pThreadClassSarEcho->InitCUDAPara();
    
    return ret;
}

int HQ_Echo_Sar::SettingSarSence(stSarParaCfg *pStParaRedCfg,char *pTar_Rcs_Pos)
{
    if(pStParaRedCfg == nullptr || pTar_Rcs_Pos == nullptr)
        return -1;
    
    stParaRedCfg *pStParaRedCfg2 = new stParaRedCfg;
    memset(pStParaRedCfg2,0,sizeof(stParaRedCfg));
    memcpy(&pStParaRedCfg2->SimData,pStParaRedCfg,sizeof(stSarParaCfg));
    //初始化地图和
    stSarEchoPara *pStSarEchoPara = pThreadClassBase->SettingSarSence(pStParaRedCfg2,pTar_Rcs_Pos);
    
    if(pStSarEchoPara)
    {
		m_TargetNum = pStSarEchoPara->TarNum;
        pStSarEchoPara->EchoType = 1;//SAR回波
        pThreadClassSarEcho->slotSettingSarSence(pStSarEchoPara);
    }
    
    return 0;
}
void HQ_Echo_Sar::Gen_PRI(RADAR *ParamR, double*PRI)
{
	pThreadClassBase->Gen_PRI(ParamR, PRI);
}

void HQ_Echo_Sar::GenSenceEcho(stSarEchoPara2 *pStSarEchoPara, stEchoData *pStEchoData)
{
	if (pStSarEchoPara->TarNum > 512 * 512)
		pStSarEchoPara->TarNum = 512 * 512;
	if (pStSarEchoPara->SimData.SenceTarNum > 512 * 512)
		pStSarEchoPara->SimData.SenceTarNum = 512 * 512;

	pThreadClassSarEcho->GenSenceEcho(pStSarEchoPara, pStEchoData);
}

double* HQ_Echo_Sar::GetSenceTarget()
{
	return pThreadClassBase->GetSenceTarget();
}

