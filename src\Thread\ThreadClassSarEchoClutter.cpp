/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassSarEchoClutter.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassSarEchoClutter.cpp $
 *         $Author: weiyingzhen $
 *         $Revision: 503 $
 *         $Date: 2025-05-27 10:41:15 $
*******************************************************************************************/
#include "ThreadClassSarEchoClutter.h"
#include<QThread>
#include <QDateTime>

#include <cuda_runtime_api.h>
#include<cuda_runtime.h>
#include<cuda_device_runtime_api.h>
#include<cufft.h>
#include<curand.h>

#include <QFile>
#include <QDebug>

#include "KernelSar.h"
#include "KernelPublic.h"
#include "KernelCultter.h"

#include "ThreadClassSendWaveGen.h"

ThreadClassSarEchoClutter::ThreadClassSarEchoClutter(int DevID,int buffer_size,char *d_Buffer,QObject *parent) : QObject(parent)
{
	SetMemPoolSrcFileName((char*)"ThreadClassSarEcho", sizeof("ThreadClassSarEcho"));
    _DefineMemPool(stClutterSeaStateLink, 50);
	_DefineMemPool(stClutterPitLink, 100);
	_DefineMemPool(ClutterRoeData, 10);
	_DefineMemPool(stTarClutterLink, 10);
    //分配CPU伪内存池空间
    InitMyMalloc(64*1024*1024);
	m_DevID				= -1;

    m_StClutterSeaStateLinkHead = (stClutterSeaStateLink*)_MallocMemObj(stClutterSeaStateLink); m_StClutterSeaStateLinkHead->init();
	m_stTarClutterLink = (stTarClutterLink*)_MallocMemObj(stTarClutterLink); m_stTarClutterLink->init();

    pClassCoorDinate = new ClassCoorDinate;

	m_Delat_X = 0;
	m_Delat_Z = 0;

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassSarEchoClutter::~ThreadClassSarEchoClutter()
{

}
//初始化GPU设备工作参数
bool ThreadClassSarEchoClutter::InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic)
{
	m_BytesOffsetTarRCS		= 0;
	BufferInit_Cur			= 0;
	//BufferInit_SizeTotal	= (long long)512 * 1024 * 1024;
	//BufferInitTarRcs_Size	= (long long)3584 * 1024 * 1024;
	m_BytesOffsetTarRCS		= 0;
    if(d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
        m_DevID				= DevID;
		m_d_BufferTarRCS	= d_Buffer;
		d_BufferFather		= d_BufferPublic;
        //分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, 5.0 * 1024 * 1024 * 1024);
		//InitMyMallocCUDA(d_BufferFather, buffer_size - ( BufferInit_SizeTotal));
    }
    else{//作为独立的线程使用，单独使用一个GPU卡
        m_DevID         = -1;
        d_BufferFather  = nullptr;
        //分配GPU伪内存池空间
		cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
		cudaMalloc((void**)&m_d_BufferTarRCS, BufferInitTarRcs_Size);
        cudaMalloc((void**)&d_Buffer,buffer_size);
		d_BufferFather = d_Buffer;
		InitMyMallocCUDA(d_BufferFather, buffer_size);
    }
	m_pBufferTarRCS = (char*)malloc(32 * 1024 * 1024.f);
	memset(m_pBufferTarRCS, 0, 32 * 1024 * 1024.f);

	//随机数句柄产生
	curandCreateGenerator(&gen_curand, CURAND_RNG_PSEUDO_MRG32K3A);

	cudaEventCreate(&e_start);
	cudaEventCreate(&e_stop);
	cudaEventCreate(&e_start2);
	cudaEventCreate(&e_stop2);

    return true;
}
//加载海杂波RCS系数
void ThreadClassSarEchoClutter::InitClutterRcsPara(void *p)
{
    cudaSetDevice(m_DevID);
    stClutterSeaStateLink *pStClutterSeaStateRecv = (stClutterSeaStateLink*)p;
	stClutterSeaStateLink *pStClutterSeaStateLinkRecvNode = pStClutterSeaStateRecv;
	stClutterPitLink *pStClutterPitLinkHead = (stClutterPitLink*)_MallocMemObj(stClutterPitLink); pStClutterPitLinkHead->init();
	stClutterPitLink *pStClutterPitLinkRecv = pStClutterSeaStateLinkRecvNode->pStClutterPitLink;

	while (pStClutterPitLinkRecv->next)
	{
		pStClutterPitLinkRecv = pStClutterPitLinkRecv->next;
		stClutterPitLink *pStClutterPitLinkNode = (stClutterPitLink*)_MallocMemObj(stClutterPitLink); 
		pStClutterPitLinkNode->init();

		int Bytes = (pStClutterPitLinkRecv->pointNum*sizeof(float) + 16) / 16 * 16;
		pStClutterPitLinkNode->pMissileDataGPU.x	= (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
		cudaMemcpy(pStClutterPitLinkNode->pMissileDataGPU.x, pStClutterPitLinkRecv->pMissileDataGPU.x, sizeof(float)*pStClutterPitLinkRecv->pointNum,cudaMemcpyHostToDevice);
		pStClutterPitLinkNode->pMissileDataGPU.y	= (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
		cudaMemcpy(pStClutterPitLinkNode->pMissileDataGPU.y, pStClutterPitLinkRecv->pMissileDataGPU.y, sizeof(float)*pStClutterPitLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStClutterPitLinkNode->pMissileDataGPU.z	= (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
		cudaMemcpy(pStClutterPitLinkNode->pMissileDataGPU.z, pStClutterPitLinkRecv->pMissileDataGPU.z, sizeof(float)*pStClutterPitLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStClutterPitLinkNode->pMissileDataGPU.rcs	= (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
		cudaMemcpy(pStClutterPitLinkNode->pMissileDataGPU.rcs, pStClutterPitLinkRecv->pMissileDataGPU.rcs, sizeof(float)*pStClutterPitLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStClutterPitLinkNode->pMissileDataGPU.phi	= (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
		cudaMemcpy(pStClutterPitLinkNode->pMissileDataGPU.phi, pStClutterPitLinkRecv->pMissileDataGPU.phi, sizeof(float)*pStClutterPitLinkRecv->pointNum, cudaMemcpyHostToDevice);

		pStClutterPitLinkNode->pointNum = pStClutterPitLinkRecv->pointNum;
		pStClutterPitLinkNode->SeaState = pStClutterPitLinkRecv->SeaState;
		pStClutterPitLinkNode->pit		= pStClutterPitLinkRecv->pit;
		AddLink(pStClutterPitLinkHead, pStClutterPitLinkNode);
	}
	stClutterSeaStateLink *pStClutterSeaStateLinkNode = (stClutterSeaStateLink*)_MallocMemObj(stClutterSeaStateLink); pStClutterSeaStateLinkNode->init();
	pStClutterSeaStateLinkNode->pStClutterPitLink = pStClutterPitLinkHead;
	pStClutterSeaStateLinkNode->iSeatState = pStClutterPitLinkHead->next->SeaState;
	AddLink(m_StClutterSeaStateLinkHead, pStClutterSeaStateLinkNode);


}
//根据海况和擦地角遍历数据文件，提取最终的面目标杂波散射数据
stClutterPitLink* ThreadClassSarEchoClutter::CalClutterRcs(int hk, float pit, stClutterSeaStateLink* pSt)
{
    float minpitDistance = FLT_MAX;
    float pitdistance;
	stClutterPitLink *finalPitLink = nullptr;
	stClutterSeaStateLink *pStClutterSeaStateLink = pSt;
	while (pStClutterSeaStateLink->next) {
		pStClutterSeaStateLink = pStClutterSeaStateLink->next;
		if (hk == pStClutterSeaStateLink->iSeatState)
		{
			stClutterPitLink * pStClutterPitLinkFind = pStClutterSeaStateLink->pStClutterPitLink;
			while (pStClutterPitLinkFind->next)
			{
				pStClutterPitLinkFind = pStClutterPitLinkFind->next;
				pitdistance = abs(pit - pStClutterPitLinkFind->pit);
				if (pitdistance < minpitDistance)
				{
					minpitDistance = pitdistance;
					finalPitLink = pStClutterPitLinkFind;
				}
			}
			return finalPitLink;
		}
    }
	return 0;
}

//加载遮挡目标系数
void ThreadClassSarEchoClutter::InitTarClutterPara(void *p)
{
	stTarClutterLink *pstTarClutterLinkRecv = (stTarClutterLink*)p;
	stTarClutterLink *pstTarClutterLinkRecvNode = pstTarClutterLinkRecv->next;
	stTarClutterLink *pstTarClutterLinkHead = (stTarClutterLink*)_MallocMemObj(stTarClutterLink); pstTarClutterLinkHead->init();
	stTarClutterLink *pstTarClutterLinkRecvTest;
	while (pstTarClutterLinkRecvNode->next)
	{
		pstTarClutterLinkRecvNode = pstTarClutterLinkRecvNode->next;
		stTarClutterLink *pstTarClutterLinkNode = (stTarClutterLink*)_MallocMemObj(stTarClutterLink); pstTarClutterLinkNode->init();
		ClutterRoeData *clutterRoeData = (ClutterRoeData*)_MallocMemObj(ClutterRoeData);
		pstTarClutterLinkNode->pClutterRoeData = clutterRoeData;
		pstTarClutterLinkNode->iTarNo = pstTarClutterLinkRecvNode->iTarNo;
		pstTarClutterLinkNode->pClutterRoeData->MaxX = pstTarClutterLinkRecvNode->pClutterRoeData->MaxX;
		pstTarClutterLinkNode->pClutterRoeData->MinX = pstTarClutterLinkRecvNode->pClutterRoeData->MinX;
		pstTarClutterLinkNode->pClutterRoeData->MaxZ = pstTarClutterLinkRecvNode->pClutterRoeData->MaxZ;
		pstTarClutterLinkNode->pClutterRoeData->MinZ = pstTarClutterLinkRecvNode->pClutterRoeData->MinZ;
		AddLink(pstTarClutterLinkHead, pstTarClutterLinkNode);
	}
	AddLink(m_stTarClutterLink, pstTarClutterLinkHead);

}

ClutterRoeData* ThreadClassSarEchoClutter::CalTarClutter(int iTarNo, stTarClutterLink* pSt)
{
	stTarClutterLink *pmstTarClutterLink = pSt;
	stTarClutterLink *pstTarClutterLinkRecv = pmstTarClutterLink->next;
	while (pstTarClutterLinkRecv->next)
	{
		pstTarClutterLinkRecv = pstTarClutterLinkRecv->next;
		if (pstTarClutterLinkRecv->iTarNo == iTarNo)
		{
			ClutterRoeData *pClutterRoeDataNode = pstTarClutterLinkRecv->pClutterRoeData;
			return pClutterRoeDataNode;
		}
	}
}


float ThreadClassSarEchoClutter::CalSigma(float cdj)
{
	if (cdj < 10)cdj = 10;
	if (cdj > 70)cdj = 70;
	float sigmaA;
	switch (SeaStateClass)
	{
	case 2:{
		sigmaA = -6.9833e-7*pow(cdj, 5) + 1.4437e-4*pow(cdj, 4) - 0.0112*pow(cdj, 3) + 0.4213*cdj*cdj - 7.2777*cdj + 4.9743;
		break;
	}
	case 3:{
		sigmaA = 4.05e-7*pow(cdj, 5) - 7.8201e-5*pow(cdj, 4) + 0.0053*pow(cdj, 3) - 0.1392*cdj*cdj + 1.4095*cdj - 40.5071;
		break;
	}
	case 4:{
		sigmaA = -1.1817e-6*pow(cdj, 5) + 2.3969e-4*pow(cdj, 4) - 0.018*pow(cdj, 3) + 0.6159*cdj*cdj - 9.1567*cdj + 13.3529;
		break;
	}
	case 5:{
		sigmaA = -9.7375e-7*pow(cdj, 5) + 1.9511e-4 *pow(cdj, 4) - 0.0144*pow(cdj, 3) + 0.4856*cdj*cdj - 6.9746*cdj + 5.2757;
		break;
	}
	default:
		break;
	}
	return sigmaA;
}

//************杂波系数提取************//
void ThreadClassSarEchoClutter::ExtractClutterScatterCoef(Target *pTarget,PLATFORM *pPlatForm, int ClutterDot, int SeaStateClass, int SaveNr, float PitAngle, float AziAngle, 
															double *Main3dBlobeWidth, double Fs)
{
	unsigned int TarNum = ClutterDot;//;

	float *dev_Target_ALL1_Out = (float*)_MallocCUDA(sizeof(float)*TarNum);
	float *dev_Target_ALL2_Out = (float*)_MallocCUDA(sizeof(float)*TarNum);
	float *dev_Target_ALL3_Out = (float*)_MallocCUDA(sizeof(float)*TarNum);
	
	const int NUM_VERTICES = 4;
	Complexf pVector[NUM_VERTICES];
	Complexf *dev_Vector = (Complexf*)_MallocCUDA(4 * sizeof(Complexf));

	cudaError_t Err = cudaGetLastError();
	if (Err != cudaSuccess){
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err;
	}

    //计算面目标杂波的擦地角
    float cdj = abs(PitAngle);
    //根据海况和擦地角获取散射系数
	stClutterPitLink *pStClutterPitLinkNode = CalClutterRcs(SeaStateClass, cdj, m_StClutterSeaStateLinkHead);
	//截取部分面杂波文件
	dim3 ThreadsPerBlock(512, 1);
	dim3 BlockNum((TarNum + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
	float PixStep = 3.f;
	int RowDot = sqrt(pStClutterPitLinkNode->pointNum);
	int CutRowDot = sqrt(TarNum);
	//计算风速导致的海浪运动，截取图像时考虑海浪运动
	int RowOffset	= round(m_Delat_X / PixStep);
	int ClomOffset  = round(m_Delat_Z / PixStep);
	int RowIndex	= (RowDot - CutRowDot) / 2 + RowOffset;//偏移行数   无风时不偏移，
	int ClomIndex	= (RowDot - CutRowDot) / 2 - ClomOffset;//偏移列数

	float *dev_Target_ALL1_In1 = pStClutterPitLinkNode->pMissileDataGPU.x + RowIndex*RowDot + ClomIndex;
	float *dev_Target_ALL2_In1 = pStClutterPitLinkNode->pMissileDataGPU.y + RowIndex*RowDot + ClomIndex;
	float *dev_Target_ALL3_In1 = pStClutterPitLinkNode->pMissileDataGPU.z + RowIndex*RowDot + ClomIndex;
	float *dev_Target_ALL4_In1 = pStClutterPitLinkNode->pMissileDataGPU.rcs + RowIndex*RowDot + ClomIndex;
	float *dev_Target_ALL5_In1 = pStClutterPitLinkNode->pMissileDataGPU.phi + RowIndex*RowDot + ClomIndex;
	CUDA_SarPictureCut(0, ThreadsPerBlock, BlockNum, TarNum, RowDot, CutRowDot, dev_Target_ALL1_In1, dev_Target_ALL2_In1, dev_Target_ALL3_In1, dev_Target_ALL4_In1, dev_Target_ALL5_In1,
						dev_Target_ALL1_Out, dev_Target_ALL2_Out, dev_Target_ALL3_Out, dev_Target_ALL4, dev_Target_ALL5);

	//计算波束波足坐标
	//float BeamX0 = -pPlatForm->SkyPos * cos(AntParam->AziAngle*PI / 180) / tan(AntParam->PitAngle*PI / 180) + pPlatForm->NorthPos - Pix;
	//float BeamY0 = 0;
	//float BeamZ0 = -pPlatForm->SkyPos * sin(AntParam->AziAngle*PI / 180) / tan(AntParam->PitAngle*PI / 180) + pPlatForm->EastPos - Piz;
	
	float temp = atan(pTarget[0].TargetVZ / pTarget[0].TargetVX);
	float alpha;

	if (pTarget[0].TargetVZ < 0 && pTarget[0].TargetVX>0)	alpha = 2 * PI + temp;
	else if (pTarget[0].TargetVX < 0)	alpha = PI + temp;
	else alpha = temp;

	float cosalpha = cos(alpha);
	float sinalpha = sin(alpha);

	ClutterRoeData* pClutterRoeDataNode = CalTarClutter(pTarget[0].ModeNo, m_stTarClutterLink);
	MinX = pClutterRoeDataNode->MinX;
	MinZ = pClutterRoeDataNode->MinZ;
	MaxX = pClutterRoeDataNode->MaxX;
	MaxZ = pClutterRoeDataNode->MaxZ;
	Complexf Vector[NUM_VERTICES] = { { MinX, MinZ },{ MaxX, MinZ },{ MaxX, MaxZ },{ MinX, MaxZ } };

	for (int i = 0; i < 4; i++)
	{
		pVector[i].x = cosalpha*Vector[i].x - sinalpha*Vector[i].y;
		pVector[i].y = sinalpha*Vector[i].x + cosalpha*Vector[i].y;
	}
	cudaMemcpy(dev_Vector, pVector, sizeof(Complexf) * 4, cudaMemcpyHostToDevice);
	
	float BeamX0 = pTarget[0].TargetX - m_Delat_X;
	float BeamY0 = 0;
	float BeamZ0 = pTarget[0].TargetZ + m_Delat_Z;
	//图像中心转换到波足位置
	CUDA_MultCoef(0, ThreadsPerBlock, BlockNum, TarNum, dev_Target_ALL1_Out, dev_Target_ALL1_Out, PixStep);//按照分辨率扩展图像(原图像为1米分辨率)
	CUDA_MultCoef(0, ThreadsPerBlock, BlockNum, TarNum, dev_Target_ALL3_Out, dev_Target_ALL3_Out, PixStep);
	CUDA_TargetToBeam(0, ThreadsPerBlock, BlockNum, TarNum, BeamX0, BeamY0, BeamZ0, dev_Target_ALL1_Out, dev_Target_ALL2_Out, dev_Target_ALL3_Out, dev_Target_ALL1, dev_Target_ALL2, dev_Target_ALL3);
	if (pTarget[0].ModeNo)
	{
		CUDA_TarClutter(0, ThreadsPerBlock, BlockNum, TarNum, dev_Target_ALL1, dev_Target_ALL3, dev_Target_ALL4, dev_Vector, pTarget[0].TargetX, pTarget[0].TargetZ);
		Err = cudaGetLastError();
	}
	if (m_DevID == 8)
	{
		char *pTemp = (char*)_Malloc(2048 * 2048 * sizeof(float) * 2);
		QFile *pQFile = new QFile;
		pQFile->setFileName("..\\..\\..\\data\\EchoDMC3");
		pQFile->open(QFile::WriteOnly);
		cudaMemcpy(pTemp, dev_Target_ALL1_Out, sizeof(float) *TarNum, cudaMemcpyDeviceToHost);
		pQFile->write((char*)pTemp, sizeof(float) * TarNum);
		cudaMemcpy(pTemp, dev_Target_ALL3_Out, sizeof(float) *TarNum, cudaMemcpyDeviceToHost);
		pQFile->write((char*)pTemp, sizeof(float) * TarNum);
		pQFile->close();
		delete pQFile;
	}

	//计算照射面积
	float *pTarDYTRang = (float*)_Malloc(3 * sizeof(float));
	pTarDYTRang[0] = -pPlatForm->SkyPos * cos(AziAngle*PI / 180) / tan(PitAngle*PI / 180);
	pTarDYTRang[1] = -pPlatForm->SkyPos;
	pTarDYTRang[2] = -pPlatForm->SkyPos * sin(AziAngle*PI / 180) / tan(PitAngle*PI / 180);
	float Range = sqrt(pTarDYTRang[0] * pTarDYTRang[0] + pTarDYTRang[1] * pTarDYTRang[1] + pTarDYTRang[2] * pTarDYTRang[2]);
	float b = Range * tan(Main3dBlobeWidth[1] / 2 * PI / 180);//短半轴计算
	float a = PixStep*CutRowDot;										//边长
	float k = b / a;
	//对图像进行幅度加权
	CUDA_MultCoef(0, ThreadsPerBlock, BlockNum, TarNum, dev_Target_ALL4, dev_Target_ALL4, k*30);

	char *pTemp = (char*)_Malloc(2048 * 2048 * sizeof(float) * 2);
	if (m_DevID == 8)
	{
		QFile *pQFile = new QFile;
		pQFile->setFileName("..\\..\\..\\data\\EchoDMC3");
		pQFile->open(QFile::WriteOnly);
		cudaMemcpy(pTemp, dev_Target_ALL4, sizeof(float) * 2048 * 2048, cudaMemcpyDeviceToHost);
		pQFile->write((char*)pTemp, sizeof(float) * 2048 * 2048);
		pQFile->close();
		delete pQFile;
	}
	Err = cudaGetLastError();

     
}
void ThreadClassSarEchoClutter::GenSenceEcho(void *p,stEchoData *pStEchoData)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	stSarEchoPara   *pStSarEchoPara = (stSarEchoPara*)p;
	SimStruct       *SimData		= (SimStruct*)&pStSarEchoPara->SimData;
	PLATFORM        *pPlatForm		= pStSarEchoPara->PlatForm;
	Target			*pTarget		= pStSarEchoPara->pTarget;
	int SeaStateClass				= pStSarEchoPara->SeaStateClass;
	m_Delat_X	= pStSarEchoPara->SeaMoveX;
	m_Delat_Z	= pStSarEchoPara->SeaMoveZ;
	int SaveNr	= SimData->SaveFlie_Nr;

	dev_Target_ALL1 = pStEchoData->dev_Target_ALL1 + pStEchoData->SacreNum;
	dev_Target_ALL2 = pStEchoData->dev_Target_ALL2 + pStEchoData->SacreNum;
	dev_Target_ALL3 = pStEchoData->dev_Target_ALL3 + pStEchoData->SacreNum;
	dev_Target_ALL4 = pStEchoData->dev_Target_ALL4 + pStEchoData->SacreNum;
	dev_Target_ALL5 = pStEchoData->dev_Target_ALL5 + pStEchoData->SacreNum;

	//目标散射系数提取
	m_SacreNum = 1024 * 1024;
	ExtractClutterScatterCoef(pTarget, pPlatForm, m_SacreNum, SeaStateClass, SimData->SaveFlie_Nr, pStSarEchoPara->AntParam[0].PitAngle, pStSarEchoPara->AntParam[0].AziAngle,
								pStSarEchoPara->AntParam[0].Main3dBlobeWidth, pStSarEchoPara->ParamR->Fs);
	pStEchoData->SacreNum += m_SacreNum;

}




