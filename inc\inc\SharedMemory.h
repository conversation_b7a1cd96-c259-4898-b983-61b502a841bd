/*******************************************************************************************
 * FileProperties: 
 *     FileName: SharedMemory.h
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/SharedMemory.h $
 *         $Author: xiayuhui $
 *         $Revision: 378 $
 *         $Date: 2025-04-07 20:36:36 $
*******************************************************************************************/
#ifndef SHAREDMEMORY_H
#define SHAREDMEMORY_H
#ifdef linux
    #define Q_DECL_EXPORT __attribute__((visibility("default")))
    #define Q_DECL_IMPORT __attribute__((visibility("default")))
#else
    #define Q_DECL_EXPORT __declspec(dllexport)
    #define Q_DECL_IMPORT __declspec(dllimport)
#endif

#if defined(_STUMEMPOOL_LIBRARY)
#  define _SHAREDMEMORY_EXPORT Q_DECL_EXPORT
#else
#  define _SHAREDMEMORY_EXPORT Q_DECL_IMPORT
#endif
#include <string>
#include <cstddef>
using namespace std;

    // 初始化共享内存（返回首地址指针）
    _SHAREDMEMORY_EXPORT void* Init(const string &key, size_t size, bool create_new = true);

    // 销毁共享内存（若为创建者）
    _SHAREDMEMORY_EXPORT bool Destroy(const string& key,void *memAddr);

    // 获取当前进程的共享内存首地址
     _SHAREDMEMORY_EXPORT void* GetSharedMemoryPtr();

//    // 写入数据

//    bool WriteData(char* data,size_t Length, size_t offset = 0);

//    // 读取数据

//    bool ReadData(char *Source,size_t Length,size_t offset = 0);

     void* s_shared_ptr;        // 共享内存首地址
     size_t s_size;             // 内存块大小
     string s_key;         // 唯一标识符
     bool s_is_creator;         // 是否为创建者

//};

#endif // SHAREDMEMORY_H
