/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassSarGenImag.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassSarGenImag.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassSarGenImag.h"
#include<QThread>
#include <QDateTime>

#include <cuda_runtime_api.h>
#include<cuda_runtime.h>
#include<cuda_device_runtime_api.h>
#include<cufft.h>
#include<curand.h>

#include <QFile>
#include <QDebug>
#include <QImage>

#include "KernelSarGenImag.h"
#include "KernelPublic.h"

#include "ThreadClassSendWaveGen.h"
#include "ThreadSocket.h"

ThreadClassSarGenImag::ThreadClassSarGenImag(int DevID,int buffer_size,char *d_Buffer,QObject *parent) : QObject(parent)
{
    SetMemPoolSrcFileName((char*)"ThreadClassSarGenImag", sizeof("ThreadClassSarGenImag"));
	_DefineMemPool(stEchoData, 5);
    _DefineMemPool(stWaveShow,10);
    //分配CPU伪内存池空间
	InitMyMalloc((long long)2048 * (long long)1024 * (long long)1024);
    m_DevID				= DevID;

	//三角函数查表
	double CosValueSample = 1.0 / 4096;
	CosValueLen = 4100;
	m_CosValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_CosValue, 0, sizeof(float)*CosValueLen);
	m_SinValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_SinValue, 0, sizeof(float)*CosValueLen);
	for (int idd = 0; idd < CosValueLen; idd++)
	{
		if (CosValueSample*idd <= 1)
		{
			m_CosValue[idd] = cos(idd*CosValueSample * 2 * PI);
			m_SinValue[idd] = sin(idd*CosValueSample * 2 * PI);
		}
	}
	m_Fs = m_Br = m_Tp = 0;

    m_SARParam = new SARPARAM;
    m_SARParam->PixelNorth  = 1536;         //成像场景北向点数
	m_SARParam->PixelEast	= 1536;         //成像场景东向点数
    m_SARParam->NorthSpace  = 1;            //北向分辨率
    m_SARParam->EastSpace   = 1;            //东向分辨率
    m_SARParam->OverSample  = 4;			//升采样倍数
    m_SARParam->NorthOffSet = 0;            //波足北向坐标
    m_SARParam->SkyOffSet   = 0;            //波足天向坐标（通常为0）
    m_SARParam->EastOffSet  = 0;            //波足东向坐标

    m_ParamR    = new RADAR;       memset(m_ParamR,0,sizeof(RADAR));
    m_SimData   = new SimStruct;   memset(m_SimData,0,sizeof(SimStruct));

	m_PixelNorth = m_PixelEast = m_NorthSpace = m_EastSpace = 0;
	m_FrameNumAcc = 0;

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassSarGenImag::~ThreadClassSarGenImag()
{

}

void writeFile(char *p, int bytes, QString str)
{
	//to do

	QString strFilePath = QString("D:\\yening\\PRJ\\PR2401_063\\5_from_yening\\EchoInterfeCalcutePool\\doc\\%1").arg(str);
	QFile *pQFile = new QFile;
	pQFile->setFileName(strFilePath);
	int ret = pQFile->open(QFile::WriteOnly);
	if (ret == 1)
	{
		//char *pBuffer = (char*)_Malloc(1024 * 1024);
		//cudaMemcpy(pBuffer, (T*)dev_EchoCompress2 + OverSample*Nr*i, OverSample*SimData->SaveFlie_Nr*sizeof(T), cudaMemcpyDeviceToHost);
		pQFile->write((char*)(p), bytes);
		pQFile->close();
	}
}
//初始化GPU设备工作参数
bool ThreadClassSarGenImag::InitDeviceWorkPara(int DevID,long long buffer_size,char *d_Buffer)
{
	BufferInit_Cur			= 0;
	BufferInit_SizeTotal	= (long long)1280 * 1024 * 1024;
    if(d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
        m_DevID         = DevID;
		d_BufferInit	= d_Buffer;
		d_BufferFather = d_Buffer + BufferInit_SizeTotal;
        //分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, buffer_size - BufferInit_SizeTotal);
    }
    else{//作为独立的线程使用，单独使用一个GPU卡
		m_DevID			= DevID;
		cudaSetDevice(m_DevID);
        d_BufferFather  = nullptr;
        //分配GPU伪内存池空间
		cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
        cudaMalloc((void**)&d_Buffer,buffer_size);
		d_BufferFather = d_Buffer;
		InitMyMallocCUDA(d_BufferFather, buffer_size);
    }
	//发射信号缓存区
	dev_Signal_Send = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += 128*1024*sizeof(float);
	cudaMemset(dev_Signal_Send, 0, 16 * sizeof(float));

	dev_Imag_Buffer = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += 4*1024*1024 * sizeof(Complexf);
	cudaMemset(dev_Imag_Buffer, 0, 4 * 1024 * 1024 * sizeof(Complexf));
	cudaError_t Err = cudaGetLastError();
	//三角函数查表
	long long Bytes = (CosValueLen*sizeof(float) + 16) / 16 * 16;
	dev_CosValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_CosValue, m_CosValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
	dev_SinValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_SinValue, m_SinValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
	Bytes = 4 * 1024 * 1024 * sizeof(float);
	dev_ImagX = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	cudaMemset(dev_ImagX, 0, Bytes);
	dev_ImagY = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	cudaMemset(dev_ImagY, 0, Bytes);
	dev_ImagZ = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	cudaMemset(dev_ImagZ, 0, Bytes);
	cufftHandle planTemp;
	cufftPlan1d(&planTemp, 32768, CUFFT_C2C, 1);

    return true;
}
//初始化cuda参数
bool ThreadClassSarGenImag::InitCUDAPara()
{
	int    BATCH = 1;
	for (int i = 0; i < CUDAStreamMaxNum; i++)
	{
		cudaStreamCreate(&cudaStream[i]);
		cufftPlan1d(&plan[i], m_SimData->SaveFlie_Nr, CUFFT_C2C, BATCH);
		cufftSetStream(plan[i], cudaStream[i]);
		cufftPlan1d(&plan2[i], m_SARParam->OverSample*m_SimData->SaveFlie_Nr, CUFFT_C2C, BATCH);
		cufftSetStream(plan2[i], cudaStream[i]);
	}
    
    return true;
}
//产生发射信号
void ThreadClassSarGenImag::RadarSendWaveGen(RADAR *ParamR,Complex *BaseSignal)
{

}
template <class T>
cufftHandle _CufftExecC2C(cufftHandle plan, T *idata, T *odata, int direction)
{
	cufftResult ret = CUFFT_NOT_SUPPORTED;
	if (sizeof(T) > 8){
		ret = cufftExecZ2Z(plan, (cuDoubleComplex*)idata, (cuDoubleComplex*)odata, direction);
	}
	else{
		ret = cufftExecC2C(plan, (cufftComplex*)idata, (cufftComplex*)odata, direction);
	}

	return ret;
}

template <class T,class T2>
void ThreadClassSarGenImag::GenSarImag(RADAR *ParamR, SimStruct *SimData, SARPARAM *SARParam, PLATFORM *PlatForm, T *BaseSignal, T2 *SarEchoData)
{
	cudaEvent_t e_start, e_stop;
	cudaEventCreate(&e_start);
	cudaEventCreate(&e_stop);
	cudaEventRecord(e_start, 0);

	cudaError_t Err = cudaGetLastError();

    int Nr = SimData->SaveFlie_Nr;
    int Na = SimData->SaveFlie_Na;
    int OverSample = 4;//升采样

    //成像参数
    long long		PixelNorth  = SARParam->PixelNorth;
	long long		PixelEast	= SARParam->PixelEast;
    float			NorthSpace  = SARParam->NorthSpace;
    float			EastSpace   = SARParam->EastSpace;
    float			NorthOffSet = SARParam->NorthOffSet;
    float			SkyOffSet   = SARParam->SkyOffSet;
    float			EastOffSet  = SARParam->EastOffSet;
	double			Rmin		= SimData->Rmin;

	double   Fs = ParamR->Fs;
	double   Fc = ParamR->Fc;
    double   Lambda      = SPEEDLIGHT / Fc;


	double *Param = (double*)_Malloc(32*sizeof(double));
    Param[0] = NorthSpace;
    Param[1] = EastSpace;
    Param[2] = NorthOffSet;
    Param[3] = SkyOffSet;
    Param[4] = EastOffSet;
	Param[5] = 1.0 / Lambda * 2;
    Param[6] = Rmin;
	Param[7] = 2.0*Fs / SPEEDLIGHT*OverSample;//(2 * dev_Param[7] / SPEEDLIGHT)*dev_ElseParam[2]

	int *ElseParam = (int*)_Malloc(32*sizeof(int));
    ElseParam[0] = PixelNorth;
    ElseParam[1] = PixelEast;
    ElseParam[2] = OverSample;
    ElseParam[3] = Nr;

	double *Ps_i = (double*)_Malloc(32 * Na * sizeof(double));

    dim3 ThreadsPerBlock2(32, 32); //单个block的Thread数目
    dim3 BlockNum2((PixelNorth + ThreadsPerBlock2.x - 1) / ThreadsPerBlock2.x, (PixelEast + ThreadsPerBlock2.y - 1) / ThreadsPerBlock2.y);	//block数目
	//dim3 BlockNum2((1 + ThreadsPerBlock2.x - 1) / ThreadsPerBlock2.x, 1);
	Err = cudaGetLastError();
    T *dev_Signal = (T*)dev_Signal_Send;

    //参考脉冲做脉压
    if (abs(m_Fs - ParamR->Fs) > 1 || abs(m_Br - ParamR->Br) > 1 || abs(m_Tp*1e6 - ParamR->Tp*1e6) > 0.1f)
    {
        m_Fs = ParamR->Fs; m_Br = ParamR->Br; m_Tp = ParamR->Tp;
        cudaMemset(dev_Signal, 0, 16*sizeof(T));
		Err = cudaGetLastError();
        cudaMemcpy(dev_Signal, BaseSignal, SimData->SaveFlie_Nr*sizeof(T), cudaMemcpyHostToDevice);
		cufftHandle ret =  _CufftExecC2C(plan[0], dev_Signal, dev_Signal, CUFFT_FORWARD);

        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum((Nr + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        CUDA_ComplexConj(0, ThreadsPerBlock, BlockNum, Nr,dev_Signal);//共轭
    }

	Err = cudaGetLastError();
    double  *dev_Param          = (double*)_MallocCUDA(sizeof(double)*32*Na);
	double  *dev_Ps				= (double*)_MallocCUDA(sizeof(double) * 32 * Na);
    int     *dev_ElseParam      = (int*)_MallocCUDA(sizeof(int)*32*Na);
    T       *dev_Echo0          = (T*)_MallocCUDA(sizeof(T)*Nr*Na);
	T       *dev_Echo01			= (T*)_MallocCUDA(sizeof(T)*Nr*Na);
	T2       *dev_EchoTemp		= (T2*)_MallocCUDA(sizeof(T2)*Nr*Na);
    T       *dev_EchoCompress   = (T*)_MallocCUDA(sizeof(T)*Nr*Na*OverSample);
    T       *dev_EchoCompress2  = (T*)_MallocCUDA(sizeof(T)*Nr*Na*OverSample);
	float   *dev_ImageAbs		= (float*)_MallocCUDA(sizeof(T)*PixelNorth*PixelEast);

	T       *dev_Image			= (T*)dev_Imag_Buffer;

	Err = cudaGetLastError();
    cudaMemcpy(dev_ElseParam, ElseParam, 10 * sizeof(int), cudaMemcpyHostToDevice);   //成像参数拷贝到显存

	//图像像素点坐标生成
	if (abs(m_PixelNorth - PixelNorth) > 0.5f || abs(m_PixelEast - PixelEast) > 0.5f || abs(m_NorthSpace - NorthSpace) > 0.01f || abs(m_EastSpace - EastSpace) > 0.01f)
	{
		dim3 ThreadsPerBlock(32, 32); //单个block的Thread数目
		dim3 BlockNum((PixelNorth + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, (PixelEast + ThreadsPerBlock.y - 1) / ThreadsPerBlock.y);	//block数目
		cudaMemcpy(dev_Param, Param, 15 * sizeof(double), cudaMemcpyHostToDevice);   //成像参数拷贝到显存
		CUDA_GenImagePix(0, ThreadsPerBlock, BlockNum, dev_Param, dev_ElseParam, dev_ImagX, dev_ImagY, dev_ImagZ);
		Err = cudaGetLastError();
	}

	dim3 ThreadsPerBlock0(512, 1);
	dim3 BlockNum0((Nr + ThreadsPerBlock0.x - 1) / ThreadsPerBlock0.x, 1);

	int iFrame = 1;
	Err = cudaGetLastError();
	for (int j = 0; j < iFrame; j++)
	{
		int cnt = 0;
		int cnt_Ps = 0;
		for (int i = 0; i < Na; i++)
		{
			int cudaStr		= i;
			Ps_i[cnt_Ps++]	= PlatForm[i].NorthPos;
			Ps_i[cnt_Ps++]	= PlatForm[i].SkyPos;
			Ps_i[cnt_Ps++]	= PlatForm[i].EastPos;
			cnt_Ps++;
			cudaMemcpyAsync(dev_Ps + 32 * i, Ps_i + 4 * i, 4 * sizeof(double), cudaMemcpyHostToDevice, cudaStream[cudaStr]);
			cudaMemcpyAsync(dev_Param + 32 * i, Param, 15 * sizeof(double), cudaMemcpyHostToDevice, cudaStream[cudaStr]);   //成像参数拷贝到显存
			cudaMemcpyAsync(dev_EchoTemp + i*Nr, SarEchoData + i*Nr, Nr * sizeof(T2), cudaMemcpyHostToDevice, cudaStream[cudaStr]);   //回波->GPU
			Err = cudaGetLastError();
			//cudaMemset(dev_Echo0 + i*Nr, 0, Nr * OverSample  * sizeof(Complexf));
			CUDA_ComplexTrans(cudaStream[cudaStr], ThreadsPerBlock0, BlockNum0, dev_EchoTemp + i*Nr, dev_Echo01 + i*Nr);
			Err = cudaGetLastError();
			cufftExecC2C(plan[cudaStr], (cufftComplex*)(dev_Echo01 + i*Nr), (cufftComplex*)(dev_Echo0 + i*Nr), CUFFT_FORWARD);
			Err = cudaGetLastError();
			dim3 ThreadsPerBlock(512, 1);
			dim3 BlockNum((Nr/2 + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
			CUDA_ComplexMutiple(cudaStream[cudaStr], ThreadsPerBlock, BlockNum, dev_Signal, dev_Echo0 + i*Nr, dev_EchoCompress + i*Nr*OverSample, Nr*1e4);
			CUDA_ComplexMutiple(cudaStream[cudaStr], ThreadsPerBlock, BlockNum, dev_Signal + Nr/2, dev_Echo0 + i*Nr + Nr / 2, dev_EchoCompress + (i + 1)*OverSample*Nr - Nr / 2, Nr*1e4);

			Err = cudaGetLastError();
			cudaMemsetAsync(dev_EchoCompress + i*OverSample*Nr + Nr / 2, 0, Nr*(OverSample - 1)*sizeof(cuFloatComplex), cudaStream[cudaStr]);       //中心填0
			cufftExecC2C(plan2[cudaStr], (cufftComplex*)(dev_EchoCompress + i*OverSample*Nr), (cufftComplex*)(dev_EchoCompress2 + i*OverSample*Nr), CUFFT_INVERSE);             //ifft
			//Kernel_ComplexDivConst << <BlockNumComp, ThreadsPerBlockComp >> >(dev_Signal, dev_Signal, Nr, ComplexDivCoeff);			//归一化
			
			Err = cudaGetLastError();

			dim3 ThreadsPerBlock2(512, 1);
			dim3 BlockNum2((PixelNorth*PixelEast + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
			CUDA_GenImage(cudaStream[cudaStr], ThreadsPerBlock2, BlockNum2, dev_Image, dev_EchoCompress2 + i*OverSample*Nr, dev_Ps + 32 * i, dev_Param + 32 * i, dev_ElseParam,
							dev_CosValue, dev_SinValue, dev_ImagX, dev_ImagY, dev_ImagZ);

			Err = cudaGetLastError();
		}
		cudaEventRecord(e_stop, 0);
		cudaEventSynchronize(e_stop);
	}

	m_FrameNumAcc += Na;
    
	if (m_FrameNumAcc >= 512)
	{
		m_FrameNumAcc = 0;
		dim3 ThreadsPerBlock(512, 1);
		dim3 BlockNum((PixelNorth*PixelEast + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
		CUDA_ComplexAbs(cudaStream[0], ThreadsPerBlock, BlockNum, dev_Image, dev_ImageAbs);//取模
		cudaMemset(dev_Image, 0, PixelNorth*PixelEast * sizeof(T));

		//float *pImag = (float*)_Malloc(PixelNorth*PixelEast * sizeof(Complexf));

		//cudaMemcpy(pImag, dev_ImageAbs, PixelNorth*PixelEast * sizeof(float), cudaMemcpyDeviceToHost);
		//writeFile((char*)pImag, PixelNorth*PixelEast * sizeof(float), QString("Echo105"));

		//QImage grayImage(PixelNorth, PixelEast, QImage::Format_Grayscale8);
	}
	

	float elapsedTime = 0;
	cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
	qDebug() << "GPU:" << m_DevID << "============  CUDA Imag Cost Time is  " << (elapsedTime) << " ms";



}
//
void ThreadClassSarGenImag::slotGenImag(void *p)
{
    cudaSetDevice(m_DevID);
    _Release_Malloc();      //初始化伪内存池
    _Release_MallocCUDA();

    RADAR       *ParamR     = m_ParamR;
    SimStruct   *SimData    = m_SimData;
    SARPARAM    *SARParam   = m_SARParam;

	stEchoData *pStEchoData = (stEchoData*)p;

	SimData->SaveFlie_Na = pStEchoData->childFrameNum;
	//发射信号生成
	Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) * ParamR->Nr);
	memset(BaseSignal, 0, sizeof(Complexf) * ParamR->Nr);
	int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数
	ThreadClassSendWaveGen::Instance()->TransSignalSim(ParamR, SimData, TrNum, BaseSignal);

	Complexs *SarEchoData	= (Complexs*)pStEchoData->SumEchoDataPolar1;

	GenSarImag(ParamR, SimData, SARParam, pStEchoData->PlatForm, BaseSignal, SarEchoData);



	_ReleaseMemObj(stEchoData, pStEchoData);
}
void ThreadClassSarGenImag::slotEchoDataImag(void *p)
{
	stEchoDataNuffer *pStEchoDataNuffer = (stEchoDataNuffer*)p;

	stEchoData *pStEchoData = (stEchoData*)_MallocMemObj(stEchoData);
	ExtractEchoData(pStEchoDataNuffer, pStEchoData);

	_ReleaseMemObj(stEchoDataNuffer, pStEchoDataNuffer);

	slotGenImag(pStEchoData);
}
//参数初始化
void ThreadClassSarGenImag::slotSettingSarGenImag(void *p)
{
	cudaSetDevice(m_DevID);
    stWhitePara *pStWhitePara = (stWhitePara*)p;
    memcpy(m_ParamR,&pStWhitePara->RadarParam,sizeof(RADAR));
    memcpy(m_SimData,&pStWhitePara->SimData,sizeof(SimStruct));
	_ReleaseMemObj(stWhitePara, pStWhitePara);
	

	InitCUDAPara();

	return;
	
	char *pBuffer = (char*)_Malloc(32 * 1024 * 1024);
	ReadEchoDate(pBuffer);
	stEchoData *pStEchoData = (stEchoData*)_MallocMemObj(stEchoData);
	ExtractEchoData(pBuffer, pStEchoData);

	slotGenImag(pStEchoData);

}
//解析回波数据
bool ThreadClassSarGenImag::ReadEchoDate(void *p)
{
	QString strFileName = QString("Echo1028");
	QString strFilePath = QString("D:\\yening\\PRJ\\PR2401_063\\5_from_yening\\EchoInterfeCalcutePool\\doc\\%1").arg(strFileName);

	QFile *pQFile = new QFile;
	pQFile->setFileName(strFilePath);
	int ret = pQFile->open(QFile::ReadOnly);
	if (ret == 1)
	{
		pQFile->read((char*)p, pQFile->size());
		pQFile->close();
		return true;
	}
	return false;
}
void ThreadClassSarGenImag::EchoCmp(char *pBufferDst, char *pBufferSrc, int &dot, int &offset)
{
	int Bytes = *(int*)(pBufferSrc + offset); offset += sizeof(int);//
	if (Bytes > 0)
	{
		memcpy(pBufferDst, pBufferSrc + offset, Bytes); offset += Bytes;
		dot += Bytes / sizeof(Complexs);
	}
	
}
bool ThreadClassSarGenImag::ExtractEchoData(void *p, stEchoData *pStEchoData)
{
	char *pBuffer = (char*)p;

	int Size_MessageEnd = sizeof(MessageEnd);

	MessageHead *pMsgHead = (MessageHead*)pBuffer;	//报文的头部

	int BufferBytes = pMsgHead->uLen;
	int offset		= 0;

	switch ((DecodeType)pMsgHead->DeviceID)//区别当前报文的发送方
	{
	case DecodeType_WhiteCalcutePool: //白方计算池
	{
		offset += sizeof(MessageHead);//大帧头
		
		int cntPulse = 0;
		while ((BufferBytes - offset) > Size_MessageEnd)
		{
			offset += sizeof(MessageHead);//小帧帧头
			//运动平台参数
			memcpy(&pStEchoData->PlatForm[cntPulse], pBuffer + offset, sizeof(PLATFORM)); offset += sizeof(PLATFORM);

			EchoCmp((char*)&pStEchoData->SumEchoDataPolar1[pStEchoData->SumEchoData1Dot / 2], pBuffer, pStEchoData->SumEchoData1Dot, offset);//极化1
			EchoCmp((char*)&pStEchoData->SumEchoDataPolar2[pStEchoData->SumEchoData2Dot / 2], pBuffer, pStEchoData->SumEchoData2Dot, offset);//极化2

			EchoCmp((char*)&pStEchoData->AziEchoDataPolar1[pStEchoData->AziEchoData1Dot / 2], pBuffer, pStEchoData->AziEchoData1Dot, offset);//方位差1
			EchoCmp((char*)&pStEchoData->AziEchoDataPolar2[pStEchoData->AziEchoData2Dot / 2], pBuffer, pStEchoData->AziEchoData2Dot, offset);//方位差2

			EchoCmp((char*)&pStEchoData->EleEchoDataPolar1[pStEchoData->EleEchoData1Dot / 2], pBuffer, pStEchoData->EleEchoData1Dot, offset);//俯仰差1
			EchoCmp((char*)&pStEchoData->EleEchoDataPolar2[pStEchoData->EleEchoData2Dot / 2], pBuffer, pStEchoData->EleEchoData2Dot, offset);//俯仰差2

			EchoCmp((char*)&pStEchoData->ExEchoDataPolar1[pStEchoData->ExEchoData1Dot / 2], pBuffer, pStEchoData->ExEchoData1Dot, offset);//辅助1
			EchoCmp((char*)&pStEchoData->ExEchoDataPolar2[pStEchoData->ExEchoData2Dot / 2], pBuffer, pStEchoData->ExEchoData2Dot, offset);//辅助2
			cntPulse++;
			offset += sizeof(MessageEnd);//小帧帧尾
		}
		pStEchoData->childFrameNum = cntPulse;
		break;
	}
	default:
		break;
	}
	return true;
}

