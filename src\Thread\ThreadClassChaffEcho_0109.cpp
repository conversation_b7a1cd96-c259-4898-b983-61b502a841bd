/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassChaffEcho.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassChaffEcho_0109.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassChaffEcho.h"
#include<QThread>
#include <QDateTime>

#include <cuda_runtime_api.h>
#include<cuda_runtime.h>
#include<cuda_device_runtime_api.h>
#include<cufft.h>
#include<curand.h>
#include <fstream> 
#include <QFile>
#include <QDebug>

#include "KernelCultter.h"
#include "KernelChaff.h"
#include "KernelSar.h"
#include "KernelPublic.h"

#include "ThreadClassSendWaveGen.h"


ThreadClassChaffEcho::ThreadClassChaffEcho(int DevID, int buffer_size, char *d_Buffer, QObject *parent) : QObject(parent)
{
    SetMemPoolSrcFileName((char*)"ThreadClassSarEcho", sizeof("ThreadClassSarEcho"));
    _DefineMemPoolCUDA(stEchoData, 2);
    _DefineMemPool(stWaveShow,10);
	_DefineMemPool(stChaffTimeLink, 50);
	_DefineMemPool(stChaffLink, 650);
	_DefineMemPool(stDateStruct1, 200);
    //分配CPU伪内存池空间
    InitMyMalloc(1024*1024*1024);
    m_DevID				= -1;
    m_bWaveDataShowFlag = false;
	//m_StChaffLinkHead = (stChaffTimeLink*)malloc(sizeof(stChaffTimeLink)); m_StChaffLinkHead->init();
	m_StChaffLinkHead = (stChaffTimeLink*)_MallocMemObj(stChaffTimeLink); m_StChaffLinkHead->init();
    pClassCoorDinate = new ClassCoorDinate;

    //三角函数查表
    double CosValueSample = 1.0 / 4096;
    CosValueLen = 4100;
    m_CosValue = (float *)malloc(sizeof(float)*CosValueLen);
    memset(m_CosValue, 0, sizeof(float)*CosValueLen);
    m_SinValue = (float *)malloc(sizeof(float)*CosValueLen);
    memset(m_SinValue, 0, sizeof(float)*CosValueLen);
    for (int idd = 0; idd < CosValueLen; idd++)
    {
        if (CosValueSample*idd <= 1)
        {
            m_CosValue[idd] = cos(idd*CosValueSample * 2 * PI);
            m_SinValue[idd] = sin(idd*CosValueSample * 2 * PI);
        }
    }
    m_Fs = m_Br = m_Tp = 0;
	//ChaffSet();

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}
ThreadClassChaffEcho::~ThreadClassChaffEcho()
{

}
//初始化GPU设备工作参数
bool ThreadClassChaffEcho::InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic)
{
    m_BytesOffsetChaffRCS		= 0;
    BufferInit_Cur			= 0;
    BufferInit_SizeTotal	= (long long)512 * 1024 * 1024;
	BufferInitChaffRcs_Size = (long long)3584 * 1024 * 1024;
	m_BytesOffsetChaffRCS = 0;
    if(d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
        m_DevID				= DevID;
        d_BufferInit		= d_Buffer;
		d_BufferFather		= d_BufferPublic;
		m_d_BufferChaffRCS = d_Buffer + BufferInit_SizeTotal;
        //分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, 5.0 * 1024 * 1024 * 1024);
    }
    else{//作为独立的线程使用，单独使用一个GPU卡
        m_DevID         = -1;
        d_BufferFather  = nullptr;
        //分配GPU伪内存池空间
        cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
		cudaMalloc((void**)&m_d_BufferChaffRCS, BufferInitChaffRcs_Size);
        cudaMalloc((void**)&d_Buffer,buffer_size);
        d_BufferFather = d_Buffer;
        InitMyMallocCUDA(d_BufferFather, buffer_size);
    }
	m_pBufferChaffRCS = (char*)malloc(64 * 1024 * 1024.f);
	memset(m_pBufferChaffRCS, 0, 64 * 1024 * 1024.f);
    //发射信号缓存区
    dev_Signal_Send = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += 128*1024*sizeof(float);
    //三角函数查表
    long long Bytes = (CosValueLen*sizeof(float) + 16) / 16 * 16;
    dev_CosValue = (float*)(d_BufferInit + BufferInit_Cur);
    cudaMemcpy(dev_CosValue, m_CosValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
    dev_SinValue = (float*)(d_BufferInit + BufferInit_Cur);
    cudaMemcpy(dev_SinValue, m_SinValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;

    Bytes = 2048.f * 2048.f * sizeof(float);
    dev_Target_ALL1 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_Target_ALL2 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_Target_ALL3 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_Target_ALL4 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_Target_ALL5 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;

    dev_DMCTarget_ALL1 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_DMCTarget_ALL2 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_DMCTarget_ALL3 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_DMCTarget_ALL4 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_DMCTarget_ALL5 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;

	Bytes = 5000.f * 5000.f * sizeof(float);
    dev_AziSubAntennaFunction = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_SumAntennaFunction = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes ;
    dev_PitSubAntennaFunction = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	Bytes = 1024.f * 100.f * sizeof(float);
	dev_ChaffReal = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	dev_ChaffImag = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    int AntBufferLength = 2048;
    dev_Matrix          = (double*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += AntBufferLength;


	m_AntParam = (ANTPARAM*)malloc(sizeof(ANTPARAM));
	m_Chaff = (ChaffRead *)malloc(sizeof(ChaffRead));
	ChaffSet();
	stChaffTimeLink *ptChaffTimeLink = readChaffRCSFile(nullptr);
	InitChaffRcsPara(ptChaffTimeLink);
	//DestroyLink(stChaffLink, ptChaffTimeLink->pstChaffLink, true);
    return true;
}
//初始化cuda参数
bool ThreadClassChaffEcho::InitCUDAPara()
{
    int    BATCH = 1;
    for (int i = 0; i < CUDAStreamMaxNum; i++)
    {
        cudaStreamCreate(&cudaStream[i]);
        cufftPlan1d(&plan[i], 16384, CUFFT_C2C, BATCH);
        cufftSetStream(plan[i], cudaStream[i]);
    }
    //随机数句柄产生
    curandCreateGenerator(&gen_curand,CURAND_RNG_PSEUDO_MRG32K3A);

    return true;
}
template <class T>
void _CufftExecC2C(cufftHandle plan,T *idata,T *odata,int direction)
{
    if (sizeof(T) > 8){
        cufftExecZ2Z(plan, (cuDoubleComplex*)idata, (cuDoubleComplex*)odata, direction);
    }
    else{
        cufftExecC2C(plan, (cufftComplex*)idata, (cufftComplex*)odata, direction);
    }
}
template<class T>
void HSYS_CALCUTE(int SenceNum, double Rbin_min, double *pTarget_ALL, double *RadarPos, T *hsys)
{
    double Lambda = C_ / 300.0e6;
    for (int i = 0; i < SenceNum; i++)
    {
        float Ptarget[3];
        double Radar2Rcs[3];

        double R_XY;
        float need_phase;
        float sigma_re, sigma_im, dem_i;

        double *dev_Target_All = pTarget_ALL + i*TARGET_LEN;

        Ptarget[0] = dev_Target_All[0];
        Ptarget[1] = dev_Target_All[1];
        Ptarget[2] = dev_Target_All[2];
        dev_Target_All[4] = 0.7485;
        sigma_re = (float)(dev_Target_All[3]) *cos(2 * PI*(float)(dev_Target_All[4]));
        sigma_im = (float)(dev_Target_All[3]) *sin(2 * PI*(float)(dev_Target_All[4]));

        Radar2Rcs[0] = Ptarget[0] - RadarPos[0];	//北
        Radar2Rcs[1] = Ptarget[1] - RadarPos[1];	//天
        Radar2Rcs[2] = Ptarget[2] - RadarPos[2];	//东

        R_XY = sqrt(Radar2Rcs[0] * Radar2Rcs[0] + Radar2Rcs[1] * Radar2Rcs[1] + Radar2Rcs[2] * Radar2Rcs[2]);       //弹目距

        int id = (int)((R_XY - Rbin_min)*(2.0*300.0e6 / C_) + 0.5);
        need_phase = -4.0*PI*R_XY / Lambda;						//相位
        qDebug() << "id" << id;

        double need_re = sigma_re*cos(need_phase) - sigma_im*sin(need_phase);
        double need_im = sigma_re*sin(need_phase) + sigma_im*cos(need_phase);

        hsys[id].x += need_re;
        hsys[id].y += need_im;
    }
}


template <class T, class T2, class T3, class T4, class T5>
void ThreadClassChaffEcho::GenDMCChaffEcho(JAM_PARAM *state, PLATFORM *pPlatForm, RADAR *ParamR, ANTPARAM *AntParam, SimStruct *SimData, T *RadarAPC, T2 *RadarVel, T3 *TargetAll, T4 *BaseSignal,
	T5 *SumCluDataPolar1, T5 *AziCluDataPolar1, T5 *EleCluDataPolar1, T5 *ExCluDataPolar1)
{
	cudaEvent_t start, stop;
	cudaError_t rettt = cudaEventCreate(&start);
	cudaError_t rettt1 = cudaEventCreate(&stop);

	cudaError_t Err = cudaGetLastError();

	int  N_comp = 1;//散射点数
	float t1 = 1;//扩散时间
	float t2 = 3;//稳定时间
	float t3 = 2;//消散时间
	float lamda = C_ / ParamR->Fc;

	int TarNum = 8192;// 此值用来预分配内存和显存
	int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5f);		//一个脉宽的点数

	int Nr = 16384;//ParamR->Nr;				//原ParamR->Nr
	int SaveNr = ParamR->Fs*ParamR->Tp;
	int Na = ParamR->Na;	//原ParamR->Na

	double *param = (double*)_Malloc(sizeof(double) * 20);//GPU计算时需要的双精度参数，内存
	param[0] = SimData->Rmin;
	param[1] = 1.0 / ParamR->Fs;
	param[2] = ParamR->Fc / C_;//1/lambda
	param[3] = 1.0 / (AntParam->AntAziBound * 2 / AntParam->AntFuncAziNum);
	param[4] = 1.0 / (AntParam->AntEleBound * 2 / AntParam->AntFuncEleNum);
	param[5] = 2.0*PI*ParamR->Fc / SPEEDLIGHT*AntParam->AziSpace;		//方位方向间距
	param[6] = 2.0*PI*ParamR->Fc / SPEEDLIGHT*AntParam->EleSpace;		//俯仰方向间距
	param[7] = 1.0 / (2.0*PI);// ParamR->Amp;             //发射信号功率
	param[8] = AntParam->AziSlope;		//方位向差斜率
	param[9] = AntParam->EleSlope;		//俯仰方向差斜率(比幅)
	param[10] = AntParam->AziOverlap;	//方位方向重叠波束（比幅）
	param[11] = AntParam->EleOverlap;	//俯仰方向重叠波束（比幅）
	param[12] = ParamR->RadomeLoss;		//天线罩损耗
	param[13] = AntParam->AziAngle;	//波束方位角
	param[14] = AntParam->PitAngle;		//波束俯仰角


	double *dev_param = (double*)_MallocCUDA(sizeof(double) * 20);//GPU计算时需要的双精度参数，显存
	Err = cudaGetLastError();
	cudaMemcpy(dev_param, param, 20 * sizeof(double), cudaMemcpyHostToDevice);
	Err = cudaGetLastError();
	double *param2 = (double*)_Malloc(sizeof(double) * 8);
	param2[0] = SimData->Rmin;
	param2[1] = 2.0*ParamR->Fs / C_;
	param2[2] = -2.0 / (C_ / ParamR->Fc);//-4.0*PI / (C_ / ParamR->Fc)
	param2[3] = ParamR->Amp * 1 * (C_ / ParamR->Fc)*(C_ / ParamR->Fc) / (64.0 * PI*PI*PI);
	param2[4] = 2 * PI;
	param2[5] = 1.0 / (2.0 * PI);
	double *dev_param2 = (double*)_MallocCUDA(sizeof(double) * 8);//GPU计算时需要的双精度参数，显存
	cudaMemcpy(dev_param2, param2, 8 * sizeof(double), cudaMemcpyHostToDevice);

	int AziOverlapLen = (int)(AntParam->Main3dBlobeWidth[0] * param[3] / 2 + 0.5f);//方位向半重叠波束长度
	int EleOverlapLen = (int)(AntParam->Main3dBlobeWidth[1] * param[4] / 2 + 0.5f);//俯仰向半重叠波束长度

	int *else_param;//GPU计算时需要的整形参数，内存
	else_param = (int*)_Malloc(sizeof(int) * 20);
	else_param[0] = 32;
	else_param[1] = TrNum;//发射信号的实际点数
	else_param[2] = AntParam->AntFuncAziNum;
	else_param[3] = AntParam->AntFuncEleNum;
	else_param[4] = SimData->AziEleFalg;
	else_param[5] = SimData->TransLossFlag;
	else_param[6] = (int)(-AziOverlapLen + AntParam->AntFuncAziNum / 2.0 + 0.5f);//方位起始照射索引
	else_param[7] = (int)(AziOverlapLen + AntParam->AntFuncAziNum / 2.0 + 0.5f);//方位结束照射索引
	else_param[8] = (int)(-EleOverlapLen + AntParam->AntFuncEleNum / 2.0 + 0.5f);//俯仰起始照射索引
	else_param[9] = (int)(EleOverlapLen + AntParam->AntFuncEleNum / 2.0 + 0.5f);//俯仰结束照射索引

	int *dev_else_param;//GPU计算时需要的整形参数，显存
	dev_else_param = (int*)_MallocCUDA(15 * sizeof(int));
	cudaMemcpy(dev_else_param, else_param, 15 * sizeof(double), cudaMemcpyHostToDevice);

	//
	//-------------------------------------------------------------------------------//
	Err = cudaGetLastError();
	double *Ps_i = (double*)_Malloc(6 * sizeof(double));
	memset(Ps_i, 0, sizeof(double) * 6);
	double *RadarVel_All = (double*)_Malloc(3 * sizeof(double));
	memset(RadarVel_All, 0, sizeof(double) * 3);
	double *dev_Ps = (double*)_MallocCUDA(sizeof(double) * 3);//雷达的位置信息，显存
	cudaMemset(dev_Ps, 0, sizeof(double) * 3);
	double *dev_RadarVel_ALL = (double*)_MallocCUDA(sizeof(double) * 3);
	cudaMemset(dev_RadarVel_ALL, 0, sizeof(double) * 3);



	T4 *hsys;//产生系统响应函数，内存
	hsys = (T4*)_Malloc(Nr*sizeof(T4));
	memset(hsys, 0, sizeof(T4)*Nr);
	Err = cudaGetLastError();


	T4 *dev_hsys, *dev_Azihsys, *dev_Elehsys;//产生系统响应函数，显存dev_Azihsys,dev_Elehsys
	dev_hsys = (T4*)_MallocCUDA(Nr*sizeof(T4)* Na);
	dev_Azihsys = (T4*)_MallocCUDA(Nr*sizeof(T4)* Na);
	dev_Elehsys = (T4*)_MallocCUDA(Nr*sizeof(T4)* Na);
	//cudaMemset(dev_hsys, 0, sizeof(T4)*Nr* Na);
	//cudaMemset(dev_Azihsys, 0, sizeof(T4)* Na*Nr);
	//cudaMemset(dev_Elehsys, 0, sizeof(T4)* Na*Nr);

	Err = cudaGetLastError();
	T4 *dev_Sumecho, *dev_Aziecho, *dev_Eleecho;//产生系统响应函数，显存dev_Azihsys,dev_Elehsys
	dev_Sumecho = (T4*)_MallocCUDA(sizeof(T4)*Nr* Na);
	dev_Aziecho = (T4*)_MallocCUDA(sizeof(T4)*Nr* Na);
	dev_Eleecho = (T4*)_MallocCUDA(sizeof(T4)*Nr* Na);

	Err = cudaGetLastError();
	float *dev_ant_factor, *dev_AziAnt_factor, *dev_EleAnt_factor;
	dev_ant_factor = (float*)_MallocCUDA(Nr*sizeof(float)*Na);
	dev_AziAnt_factor = (float*)_MallocCUDA(Nr*sizeof(float)*Na);
	dev_EleAnt_factor = (float*)_MallocCUDA(Nr*sizeof(float)*Na);
	//cudaMemset(dev_ant_factor, 0, Nr*sizeof(float)*Na);  //之前double
	//cudaMemset(dev_AziAnt_factor, 0, Nr*sizeof(float)*Na);
	//cudaMemset(dev_EleAnt_factor, 0, Nr*sizeof(float)*Na);
	Err = cudaGetLastError();




	float *ChaffX = (float*)_MallocCUDA(50 * Nr*sizeof(float));
	float *ChaffY = (float*)_MallocCUDA(50 * Nr*sizeof(float));
	float *ChaffZ = (float*)_MallocCUDA(50 * Nr*sizeof(float));
	float *ChaffBX = (float*)_MallocCUDA(50 * Nr*sizeof(float));
	float *ChaffBY = (float*)_MallocCUDA(50 * Nr*sizeof(float));
	float *ChaffBZ = (float*)_MallocCUDA(50 * Nr*sizeof(float));
	float *time0 = (float*)_MallocCUDA(Nr*sizeof(float));
	float *sawtooth = (float*)_MallocCUDA(Nr*sizeof(float));
	float *cos0 = (float*)_MallocCUDA(Nr*sizeof(float));

	float *dev_Range = (float*)_MallocCUDA(Nr*sizeof(float));
	float *dev_Range_2 = (float*)_MallocCUDA(TarNum*sizeof(float));
	float *dev_Fd = (float*)_MallocCUDA(TarNum*sizeof(float));
	int *dev_id = (int*)_MallocCUDA(TarNum*sizeof(int));
	Err = cudaGetLastError();

	T4 *EchoLine = (T4*)_Malloc(sizeof(T4)*Nr);	//一个距离向的回波
	T4 *EchoLineTemp = (T4*)_Malloc(sizeof(T4)*Nr);
	T4 *dev_EchoSum = (T4*)_MallocCUDA(sizeof(T4)*Nr* Na);
	T4 *dev_EchoAzi = (T4*)_MallocCUDA(sizeof(T4)*Nr* Na);
	T4 *dev_EchoPit = (T4*)_MallocCUDA(sizeof(T4)*Nr* Na);
	T4*dev_hsysSum = (T4*)_MallocCUDA(sizeof(T4)*Nr* Na);//产生系统响应函数
	T4*dev_hsysAzi = (T4*)_MallocCUDA(sizeof(T4)*Nr* Na);
	T4*dev_hsysPit = (T4*)_MallocCUDA(sizeof(T4)*Nr* Na);


	dev_hsysSum = SumCluDataPolar1;
	dev_hsysAzi = AziCluDataPolar1;
	dev_hsysPit = EleCluDataPolar1;

	int FdFlag = 0; //0=卷积方式调制 1=散射点叠加方式
	T4		*dev_Signal = (T4*)dev_Signal_Send;


	//cudaEventRecord(start, 0);

	Err = cudaGetLastError();

	float *p11 = (float*)_Malloc(sizeof(float) * 100);
	cudaMemcpy(p11, m_StChaffLinkHead->next->pstChaffLink->next->pChaffData.x, sizeof(float) * 100, cudaMemcpyDeviceToHost);
	float time;
	float vx, vy, vz;
	int start_index = -1;
	int count = 0;
	for (int idx = 0; idx < state->ChaffNum; idx++)
	{

		float deltat = 1 / ParamR->Prf;
		time = state[idx].Chafftime;
		vx = state[idx].ChaffVelf[0];
		vy = state[idx].ChaffVelf[1];
		vz = state[idx].ChaffVelf[2];

		stChaffLink *pStChaffLinkNode = CalChaffRcs(time, m_StChaffLinkHead);
		Err = cudaGetLastError();
		float* dev_ChaffX = pStChaffLinkNode->pChaffData.x;
		Err = cudaGetLastError();
		float* dev_ChaffY = pStChaffLinkNode->pChaffData.y;
		float* dev_ChaffZ = pStChaffLinkNode->pChaffData.z;
		float* dev_ChaffRcs = pStChaffLinkNode->pChaffData.rcs;
		float* dev_ChaffAzi = pStChaffLinkNode->pChaffData.phi;
		count = (int)pStChaffLinkNode->pointNum;
		
		cudaMemcpy(p11, dev_ChaffX, sizeof(float) * 100, cudaMemcpyDeviceToHost);

		dim3 ThreadsPerBlock(512, 1);
		dim3 BlockNum((count + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
		CUDA_Pos(ThreadsPerBlock, BlockNum, ChaffX, ChaffY, ChaffZ, dev_ChaffX, dev_ChaffY, dev_ChaffZ, state[idx].ChaffPos[0], state[idx].ChaffPos[1], state[idx].ChaffPos[2], count);
		Err = cudaGetLastError();

	
		cudaMemcpy(p11, dev_ChaffRcs, sizeof(float) * 100, cudaMemcpyDeviceToHost);
		CUDA_Vbian(ThreadsPerBlock, BlockNum, ChaffX, ChaffY, ChaffZ, ChaffBX, ChaffBY, ChaffBZ, time, deltat, count, time0, sawtooth, cos0, vx, vy, vz, idx);
		int index = time * 100;
		for (int k = 0; k < Na; k++)
		{
			Ps_i[0] = pPlatForm[k].NorthPos;
			Ps_i[1] = pPlatForm[k].SkyPos;
			Ps_i[2] = pPlatForm[k].EastPos;

			cudaMemcpy(dev_Ps, Ps_i, sizeof(double) * 3, cudaMemcpyHostToDevice);

			CUDA_cal_ant_all_kernel_DMC1(0, ThreadsPerBlock, BlockNum, dev_Ps, ChaffBX, ChaffBY, ChaffBZ,
				dev_RadarVel_ALL, dev_SumAntennaFunction, dev_AziSubAntennaFunction, dev_PitSubAntennaFunction,
				dev_Matrix, dev_param, dev_else_param, dev_CosValue, dev_SinValue, dev_ant_factor, dev_AziAnt_factor, dev_EleAnt_factor,
				dev_Range, start_index, count);
			float *p12 = (float*)malloc(sizeof(float) * 1024);
			cudaMemcpy(p12, dev_ant_factor, sizeof(float) * 1024, cudaMemcpyDeviceToHost);

			Err = cudaGetLastError();
			CUDA_cal_hsys_all_kernel_jushuDMC1(0, ThreadsPerBlock, BlockNum, count, dev_ChaffRcs, dev_ChaffAzi, dev_ChaffReal, dev_ChaffImag, dev_RadarVel_ALL,
				dev_param2, dev_else_param, dev_CosValue, dev_SinValue,
				dev_ant_factor, dev_AziAnt_factor, dev_EleAnt_factor, dev_Range, start_index,
				dev_hsysSum + Nr*k, dev_hsysAzi + Nr*k, dev_hsysPit + Nr*k, (index - 1) * 128, k);
			float *p13 = (float*)malloc(sizeof(float) * 1024);
			cudaMemcpy(p13, dev_ChaffRcs, sizeof(float) * 1024, cudaMemcpyDeviceToHost);
			Err = cudaGetLastError();
		}
	}
	//cudaEventRecord(stop, 0);
	//cudaEventSynchronize(stop);
	//float elapsedTime = 0, elapsedTime2 = 0;
	//cudaEventElapsedTime(&elapsedTime, start, stop);
	//Err = cudaGetLastError();
	

}
stChaffLink* ThreadClassChaffEcho::CalChaffRcs(float time, stChaffTimeLink* pSt)
{
	float minpitDistance = FLT_MAX;
	float pitdistance;
	cudaError_t Err = cudaGetLastError();
	stChaffLink *finalLink = nullptr;
	stChaffTimeLink *pStChaffStateLink = pSt;
	while (pStChaffStateLink->next) {
		pStChaffStateLink = pStChaffStateLink->next;
		stChaffLink * pStChaffLinkFind = pStChaffStateLink->pstChaffLink;

		while (pStChaffLinkFind->next)
		{
			pStChaffLinkFind = pStChaffLinkFind->next;
			pitdistance = abs(time - pStChaffLinkFind->timeNo);
			if (pitdistance < minpitDistance)
			{
				minpitDistance = pitdistance;
				finalLink = pStChaffLinkFind;
			}
		}


		return finalLink;

	}

	return 0;
}

void ThreadClassChaffEcho::InitChaffRcsPara(void *p)
{
	cudaSetDevice(m_DevID);
	stChaffTimeLink *pstChaffTimeRecv = (stChaffTimeLink*)p;
	stChaffTimeLink *pstChaffTimeLinkRecvNode = pstChaffTimeRecv;

	stChaffLink *pStChaffrLinkHead = (stChaffLink*)_MallocMemObj(stChaffLink); pStChaffrLinkHead->init();

	stChaffLink *pStChaffLinkRecv = pstChaffTimeLinkRecvNode->pstChaffLink;

	while (pStChaffLinkRecv->next)
	{
		pStChaffLinkRecv = pStChaffLinkRecv->next;

		stChaffLink *pStChaffLinkNode = (stChaffLink*)_MallocMemObj(stChaffLink);
		pStChaffLinkNode->init();

		int Bytes = (pStChaffLinkRecv->pointNum*sizeof(float) + 16) / 16 * 16;

		pStChaffLinkNode->pChaffData.x = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.x, pStChaffLinkRecv->pChaffData.x, sizeof(float)*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStChaffLinkNode->pChaffData.y = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.y, pStChaffLinkRecv->pChaffData.y, sizeof(float)*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStChaffLinkNode->pChaffData.z = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.z, pStChaffLinkRecv->pChaffData.z, sizeof(float)*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStChaffLinkNode->pChaffData.rcs = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.rcs, pStChaffLinkRecv->pChaffData.rcs, sizeof(float)*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStChaffLinkNode->pChaffData.phi = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.phi, pStChaffLinkRecv->pChaffData.phi, sizeof(float)*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStChaffLinkNode->timeNo = pStChaffLinkRecv->timeNo;
		pStChaffLinkNode->pointNum = pStChaffLinkRecv->pointNum;;
		AddLink(pStChaffrLinkHead, pStChaffLinkNode);
	}
	
	stChaffTimeLink *pStChaffLinkNode = (stChaffTimeLink*)_MallocMemObj(stChaffTimeLink); pStChaffLinkNode->init();
	pStChaffLinkNode->pstChaffLink = pStChaffrLinkHead;
	AddLink(m_StChaffLinkHead, pStChaffLinkNode);



}

stChaffTimeLink* ThreadClassChaffEcho::readChaffRCSFile(char *path)
{
	cudaError_t Err = cudaGetLastError();
	stChaffTimeLink *ptChaffTimeLink = (stChaffTimeLink*)_MallocMemObj(stChaffTimeLink); ptChaffTimeLink->init();

	stChaffLink *pstChaffLinkHead = (stChaffLink*)_MallocMemObj(stChaffLink); pstChaffLinkHead->init();

	ptChaffTimeLink->pstChaffLink = pstChaffLinkHead;
	QFile *pQFile = new QFile;
	pQFile->setFileName("..\\..\\..\\rcs\\Chaff");
	pQFile->open(QFile::ReadOnly);
	for (int i = 0; i < 601; i++)
	{
		stChaffLink *pstChaffLink = (stChaffLink*)_MallocMemObj(stChaffLink); pstChaffLink->init();

		pQFile->read((char*)&(pstChaffLink->timeNo), sizeof(float));
		pQFile->read((char*)&(pstChaffLink->pointNum), sizeof(float));
		pstChaffLink->pChaffData.x = (float*)_MallocMemObj(stDateStruct1);
		pstChaffLink->pChaffData.y = (float*)_MallocMemObj(stDateStruct1);
		pstChaffLink->pChaffData.z = (float*)_MallocMemObj(stDateStruct1);
		pstChaffLink->pChaffData.rcs = (float*)_MallocMemObj(stDateStruct1);
		pstChaffLink->pChaffData.phi = (float*)_MallocMemObj(stDateStruct1);

		pQFile->read((char*)pstChaffLink->pChaffData.x, sizeof(float)* pstChaffLink->pointNum);
		pQFile->read((char*)pstChaffLink->pChaffData.y, sizeof(float)* pstChaffLink->pointNum);
		pQFile->read((char*)pstChaffLink->pChaffData.z, sizeof(float)* pstChaffLink->pointNum);
		pQFile->read((char*)pstChaffLink->pChaffData.rcs, sizeof(float)* pstChaffLink->pointNum);
		pQFile->read((char*)pstChaffLink->pChaffData.phi, sizeof(float)* pstChaffLink->pointNum);
		AddLink(ptChaffTimeLink->pstChaffLink, pstChaffLink);

	}
	pQFile->close();
	delete pQFile;
	return ptChaffTimeLink;
}



//箔条
void ThreadClassChaffEcho::ChaffSet()
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	ChaffRead  *Chaff = m_Chaff;

	QFile *pQFile1 = new QFile;
	pQFile1->setFileName("..\\..\\..\\data\\Chaffdefalt");
	pQFile1->open(QFile::ReadOnly);
	Chaff->ChaffReal = (float*)malloc(sizeof(float) * 76800);
	pQFile1->read((char*)Chaff->ChaffReal, 76800 * sizeof(float));
	Chaff->ChaffImag = (float*)malloc(sizeof(float) * 76800);
	pQFile1->read((char*)Chaff->ChaffImag, 76800 * sizeof(float));
	pQFile1->close();

	cudaMemcpy(dev_ChaffReal, Chaff->ChaffReal, sizeof(float) * 76800, cudaMemcpyHostToDevice);
	cudaMemcpy(dev_ChaffImag, Chaff->ChaffImag, sizeof(float) * 76800, cudaMemcpyHostToDevice);

}


void ThreadClassChaffEcho::slotGenChaffEcho(void *p)
{
    //if (m_DevID == 0)return;
    int ret = cudaSetDevice(m_DevID);

    qDebug() << "-------------------------------------------------------------------------------------------------------------------Thread ID:" << this->thread()->currentThreadId();

    _Release_Malloc();      //初始化伪内存池
    _Release_MallocCUDA();

 /*   cudaEventRecord(e_start, 0);*/

   // stSarEchoPara *pStSarEchoPara = (stSarEchoPara*)p;
	_stChaffEchoPara *pstChaffEchoPara = (_stChaffEchoPara*)p;
	RADAR *ParamR = pstChaffEchoPara->ParamR;
	ANTPARAM *AntParam = pstChaffEchoPara->AntParam;
	SimStruct *SimData = (SimStruct*)&pstChaffEchoPara->SimData;
	PLATFORM *pPlatForm = pstChaffEchoPara->PlatForm;
	JAM_PARAM *state = pstChaffEchoPara->Jammer;
	double *RadarAPC = pstChaffEchoPara->RadarAPC;
	double	*RadarVel = pstChaffEchoPara->RadarVel;
	double *SenceTarget = pstChaffEchoPara->SenceTarget;
    //发射信号生成
    Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) * ParamR->Nr);
    memset(BaseSignal, 0, sizeof(Complexf) * ParamR->Nr);
    int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数
    ThreadClassSendWaveGen::Instance()->TransSignalSim(ParamR, SimData, TrNum, BaseSignal);

    stEchoData *pStEchoData		= _MallocMemObj(stEchoData);
    Complexf *SumEchoDataPolar1	= pStEchoData->SumEchoDataPolar1;
    Complexf *AziEchoDataPolar1 = pStEchoData->AziEchoDataPolar1;
    Complexf *EleEchoDataPolar1 = pStEchoData->EleEchoDataPolar1;
    Complexf *ExEchoDataPolar1	= pStEchoData->ExEchoDataPolar1;

        //单脉冲模式生成回波
	GenDMCChaffEcho(state, pPlatForm, ParamR, AntParam, SimData, RadarAPC, RadarVel, SenceTarget, BaseSignal,
                    SumEchoDataPolar1, AziEchoDataPolar1, EleEchoDataPolar1, ExEchoDataPolar1);
        pStEchoData->SumEchoData1Dot	= SimData->SaveFlie_Nr;
        pStEchoData->ExEchoData1Dot		= 0;
        if (SimData->AziEleFalg > 0){
            pStEchoData->AziEchoData1Dot = SimData->SaveFlie_Nr;
            pStEchoData->EleEchoData1Dot = SimData->SaveFlie_Nr;
        }


		pStEchoData->Nr_Save = SimData->SaveFlie_Nr;
		pStEchoData->Na_Save = ParamR->Na;
		pStEchoData->Rmin = SimData->Rmin;
		pStEchoData->AziK = AntParam->AziSlope;
		pStEchoData->PitK = AntParam->EleSlope;
 //   pStEchoData->Nr_Save = SimData->SaveFlie_Nr;
 //   pStEchoData->Na_Save = ParamR->Na;
		memcpy(pStEchoData->PlatForm, pstChaffEchoPara->PlatForm, sizeof(PLATFORM)*ParamR->Na);

	//pStEchoData->FrameSerial = pstChaffEchoPara->FrameSerial;
	//pStEchoData->childFrameSerial = pstChaffEchoPara->childFrameSerial;
	//pStEchoData->childFrameNum = pstChaffEchoPara->childFrameNum;
	//pStEchoData->Rmin = pstChaffEchoPara->SimData.Rmin;

    //生成的回波发送到回波整合线程进行数据格式转换
 //   emit sendEchoScheduling(pStEchoData);

	//if (pstChaffEchoPara->RadarAPC)
	//	_ReleaseMemObj(stRadarAPC, pstChaffEchoPara->RadarAPC);
	//if (pstChaffEchoPara->RadarVel)
	//	_ReleaseMemObj(stRadarAPC, pstChaffEchoPara->RadarVel);
	//_ReleaseMemObj(pstChaffEchoPara, p);

    //cudaEventRecord(e_stop, 0);
    //cudaEventSynchronize(e_stop);
    //float elapsedTime = 0;
    //cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
    //if (elapsedTime > 9.95f)
    //    qDebug() << "GPU:" << m_DevID << "============  ThreadClassSarEcho Cost Time is  " << (elapsedTime) << " ms";
}


void ThreadClassChaffEcho::GenChaffEcho(void *p, stEchoData *pStEchoData)
{
    cudaSetDevice(m_DevID);
    _Release_Malloc();      //初始化伪内存池
    _Release_MallocCUDA();

	_stChaffEchoPara *pstChaffEchoPara = (_stChaffEchoPara*)p;
	RADAR *ParamR = pstChaffEchoPara->ParamR;
    ANTPARAM *AntParam = m_AntParam;
    AntParam->AziAngle = pstChaffEchoPara->AntParam->AziAngle;
    AntParam->PitAngle = pstChaffEchoPara->AntParam->PitAngle;
    //to do
	memcpy(AntParam->G_Matrix, pstChaffEchoPara->AntParam->G_Matrix, sizeof(double) * 9);
    cudaMemcpy(dev_Matrix, AntParam->G_Matrix, sizeof(double) * 9, cudaMemcpyHostToDevice);

	SimStruct *SimData = (SimStruct*)&pstChaffEchoPara->SimData;
	PLATFORM *pPlatForm = pstChaffEchoPara->PlatForm;
	JAM_PARAM *Pjammer = pstChaffEchoPara->Jammer;
	double *RadarAPC = pstChaffEchoPara->RadarAPC;
	double	*RadarVel = pstChaffEchoPara->RadarVel;
    //发射信号生成
    Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) * ParamR->Nr);
    memset(BaseSignal, 0, sizeof(Complexf) * ParamR->Nr);
    int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数
    ParamR->Kr = ParamR->Br / ParamR->Tp;
    ThreadClassSendWaveGen::Instance()->TransSignalSim(ParamR, SimData, TrNum, BaseSignal);

	Complexf *SumEchoDataPolar1 = pStEchoData->dev_SumEchoDataPolar1;
	Complexf *AziEchoDataPolar1 = pStEchoData->dev_AziEchoDataPolar1;
	Complexf *EleEchoDataPolar1 = pStEchoData->dev_EleEchoDataPolar1;
	Complexf *ExEchoDataPolar1 = pStEchoData->dev_ExEchoDataPolar1;

	if (pstChaffEchoPara->EchoType == 0)//单脉冲
    {
        //单脉冲模式生成回波
		GenDMCChaffEcho(Pjammer, pPlatForm, ParamR, AntParam, SimData, RadarAPC, RadarVel, (double*)nullptr, BaseSignal,
            SumEchoDataPolar1, AziEchoDataPolar1, EleEchoDataPolar1, ExEchoDataPolar1);
        pStEchoData->SumEchoData1Dot = SimData->SaveFlie_Nr;
        pStEchoData->ExEchoData1Dot = 0;
        if (SimData->AziEleFalg > 0){
            pStEchoData->AziEchoData1Dot = SimData->SaveFlie_Nr;
            pStEchoData->EleEchoData1Dot = SimData->SaveFlie_Nr;
        }
    }
    else
    {
        //面目标回波生成
    //    GenSenceEcho(ParamR, AntParam, SimData, RadarAPC, (double*)nullptr, BaseSignal,
    //        SumEchoDataPolar1, AziEchoDataPolar1, EleEchoDataPolar1, ExEchoDataPolar1);
    //    pStEchoData->SumEchoData1Dot = SimData->SaveFlie_Nr;
    }

    pStEchoData->Nr_Save = SimData->SaveFlie_Nr;
    pStEchoData->Na_Save = ParamR->Na;
    pStEchoData->Rmin = SimData->Rmin;
    pStEchoData->AziK = AntParam->AziSlope;
    pStEchoData->PitK = AntParam->EleSlope;
	memcpy(pStEchoData->PlatForm, pstChaffEchoPara->PlatForm, sizeof(PLATFORM)*ParamR->Na);

}

void ThreadClassChaffEcho::slotASKWaveDate()
{
    m_bWaveDataShowFlag = true;
}
//天线
void ThreadClassChaffEcho::slotSettingChaffAntPara(void *p)
{
    cudaSetDevice(m_DevID);
    _Release_Malloc();      //初始化伪内存池
    _Release_MallocCUDA();

    ANTPARAM        *AntParam = (ANTPARAM*)p;
    //***********天线加权gpu***********//
    cudaMemcpy(dev_SumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_AziSubAntennaFunction, AntParam->AziSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_PitSubAntennaFunction, AntParam->PitSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_Matrix, AntParam->G_Matrix, sizeof(double) * 9, cudaMemcpyHostToDevice);

    memcpy(m_AntParam, AntParam, sizeof(ANTPARAM));
}
//场景
void ThreadClassChaffEcho::slotSettingChaffSence(void *p)
{
    cudaSetDevice(m_DevID);
    _Release_Malloc();      //初始化伪内存池
    _Release_MallocCUDA();
    stSarEchoPara   *pStSarEchoPara	= (stSarEchoPara*)p;
    ANTPARAM        *AntParam       = (ANTPARAM*)&pStSarEchoPara->AntParam[0];
    //***********天线加权gpu***********//
    cudaMemcpy(dev_SumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_AziSubAntennaFunction, AntParam->AziSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_PitSubAntennaFunction, AntParam->PitSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_Matrix, AntParam->G_Matrix, sizeof(double) * 9, cudaMemcpyHostToDevice);

}
