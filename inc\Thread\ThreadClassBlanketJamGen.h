/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassInterference.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassBlanketJamGen.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef THREADCLASSBLANKKETJAMGEN_H
#define THREADCLASSBLANKKETJAMGEN_H

#include <QObject>

#include"MyMalloc.h"
#include"MyMallocCUDA.h"
#include "_stumempool.h"
#include "WhiteCalGlobel.h"
#include"globalJamStruct.h"

#include<cufft.h>
#include <curand.h>

class ThreadClassBlanketJamGen:public QObject,MyMalloc,MyMallocCUDA,_StuMemPool
{
    Q_OBJECT
public:
    explicit ThreadClassBlanketJamGen(QObject *parent = 0);
    ~ThreadClassBlanketJamGen();
public:
    //初始化GPU设备工作参数
	bool InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic);
    //初始化cuda参数
    bool InitCUDAPara();

	//压制干扰主函数
	void GenBlanketJam(void *p, stEchoData *pStEchoData);
	//天线
	void slotSettingAntPara(void *p);

signals:
    void sendJamDataFormat(void *p);

public slots:
	void slotBlanketJamGen(void *p);


private:
	//压制噪声参数初始化
	void JamParamSet(JAM_SUPPRESS_PARAM *JamParam);
	template<class T, class T2, class T3, class T4>
	void CalAntWeight(int DYT, ANTPARAM *AntParam, RADAR *ParamR, T *Radar_Pos, T *JamDev_Pos, T2 *AntFuction, T2 *AziAntFuction, T2 *PitAntFuction, T3 *AntGain, T4 *Range);
    
	//扫频模式频点计算
	template <class T, class T2, class T3, class T4>
	void SweepFreGen(int SweepType, T SweepFmin, T2 SweepFmax, T3 SweepVel, int SweepCyclePRTNum, int SweepPRTNum, T4 *SweepFCur);
	//噪声生成
    template <class T,class T2,class T3,class T4,class T5,class T6,class T7,class T8>
	void NoiseModelGen(int NoiseSigLen, T MeanValue, T2 StdValue, T3 NoiseFc, T4 NoiseBr, T5 NoiseAmp, T6 Fs, T7 *dev_NoiseModel, T8 *dev_RandomComplexf);

    //连续噪声
	template <class T,class T2>
	void BlockJamGen(JAM_SUPPRESS_PARAM *JamParam, double Fs, int PRFID, int PRTLen, T *BlanketJamSig, T2 *dev_RandomComplexf);//
    //间断噪声
	template <class T, class T2>
	void PulseJamGen(JAM_SUPPRESS_PARAM *JamParam, double Fs, int JamSigLen, int PRFID, int PRTLen, T *BlanketJamSig, T2 *dev_RandomComplexf);
    //扫频噪声
	template <class T, class T2>
	void SweepJamGen(JAM_SUPPRESS_PARAM *JamParam, double Fs, int JamSigLen, int PRFID, int PRTLen, T *BlanketJamSig, T2 *dev_RandomComplexf);
    //梳状谱
	template <class T, class T2>
	void CombSpecJamGen(JAM_SUPPRESS_PARAM *JamParam, double Fs, int JamSigLen, int PRFID, int PRTLen, T *BlanketJamSig, T2 *dev_RandomComplexf);





private:
    int m_DevID;
    char *d_BufferFather, *d_BufferInit;
    long long BufferInit_SizeTotal, BufferInit_Cur;

    curandGenerator_t	gen_curand;
    cufftHandle			plan1;

    cufftHandle plan_1k;
    cufftHandle plan_2k;
    cufftHandle plan_4k;
    cufftHandle plan_8k;
    cufftHandle plan_16k;
    cufftHandle plan_32k;
    cufftHandle plan_64k;
    cufftHandle plan_128k;
    cufftHandle plan_256k;
    cufftHandle plan_512k;
    cufftHandle CufftPlanCheck(int fftDot);

    int CosValueLen;//三角函数表长
    float *dev_CosValue, *dev_SinValue;//三角函数查表显存
    float *m_CosValue, *m_SinValue;
    Complexf *dev_RandomComplexf;

	float   *dev_SumAntennaFunction, *dev_AziSubAntennaFunction, *dev_PitSubAntennaFunction;
	float   *pSumAntennaFunction, *pAziSubAntennaFunction, *pPitSubAntennaFunction;
	ANTPARAM *m_AntParam, *m_AntParamJam;
};

#endif // THREADCLASSBLANKKETJAMGEN_H
