<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <layout class="QGridLayout" name="gridLayout_2">
    <item row="0" column="0">
     <layout class="QHBoxLayout" name="horizontalLayout_4">
      <item>
       <widget class="QGroupBox" name="groupBox">
        <property name="title">
         <string>自测试仿真参数</string>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <item row="0" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1">
           <item>
            <widget class="QLabel" name="label">
             <property name="text">
              <string>仿真模式</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="comboBoxSimMode">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>25</height>
              </size>
             </property>
             <item>
              <property name="text">
               <string>回波生成</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>杂波生成</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>干扰生成</string>
              </property>
             </item>
            </widget>
           </item>
          </layout>
         </item>
         <item row="1" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,1">
           <item>
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>单个CPI时长/ms</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSpinBox" name="spinBoxCPITime">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>25</height>
              </size>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="2" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,1">
           <item>
            <widget class="QPushButton" name="pushButtonSettingPara">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>30</height>
              </size>
             </property>
             <property name="text">
              <string>参数预加载</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButtonRun">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>30</height>
              </size>
             </property>
             <property name="text">
              <string>开始仿真</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QPushButton" name="pushButtonWaveShow">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="text">
           <string>波形显示</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButtonSARRPL">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="text">
           <string>SAR回放</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>38</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
