/*******************************************************************************************
 * FileProperties: 
 *     FileName: MyMallocCUDA.cpp
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/MemPool/MyMallocCUDA.cpp $
 *         $Author: yening $
 *         $Revision: 21 $
 *         $Date: 2024-12-23 13:21:07 $
*******************************************************************************************/
#include"MyMallocCUDA.h"
#include<stdlib.h>
#include<string.h>
#include<stdio.h>

MyMallocCUDA::MyMallocCUDA()
{
	m_BufferLengthCUDA = 0;
    m_BufferCUDA    = 0;
    m_lengthCur     = 0;

}

bool MyMallocCUDA::InitMyMallocCUDA(void *p,long long bytes)
{
    m_BufferCUDA = (char*)p;
    if(m_BufferCUDA == nullptr)
        return false;
    m_BufferLengthCUDA = bytes;

    return true;
}

char* MyMallocCUDA::_MallocCUDA(int bytes)
{
    long long length = m_lengthCur;
    if (length + bytes < m_BufferLengthCUDA)
    {
		long long N = (bytes + 32) / 32 * (32);
        m_lengthCur += N + 32;
        return m_BufferCUDA + length + 32;
    }
    return 0;
}
void MyMallocCUDA::_Release_MallocCUDA()
{
	m_lengthCur = 0;
}
