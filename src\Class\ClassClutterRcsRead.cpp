/*******************************************************************************************
 * FileProperties: 
 *     FileName: ClassClutterRcsRead.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Class/ClassClutterRcsRead.cpp $
 *         $Author: yening $
 *         $Revision: 160 $
 *         $Date: 2025-02-05 17:40:22 $
*******************************************************************************************/
#include "ClassClutterRcsRead.h"
#include <QFile>
#include <QDir>
#include "ThreadSocketTCP.h"

#include <iostream>
#include<fstream>
#include<cmath>
#include<sstream>
#include<string>
#include<cstdlib>
#include<limits>
#include<locale>
#include<codecvt>

ClassClutterRcsRead::ClassClutterRcsRead()
{
    SetMemPoolSrcFileName((char*)"ClassClutterRcsRead", sizeof("ClassClutterRcsRead"));
    _DefineMemPool(stClutterSeaStateLink, 20);
    _DefineMemPool(stClutterPitLink, 20);
    _DefineMemPool(stDateStruct, 200);


}

ClassClutterRcsRead::~ClassClutterRcsRead()
{

}

void ClassClutterRcsRead::extractNumbers(char* filename3, double &SeaState, double &pit)
{
    int numbercount = 0;
    string filename2 = (string)filename3;
    string filename1;
    size_t lastSlash = filename2.find_last_of("/\\");
    if (lastSlash != string::npos) {
        filename1 = filename2.substr(lastSlash + 1);
    }
    else {
        filename1 = filename2;
    }
    const char *pBuffer = filename1.c_str();
    string number;
    char pName[512]; memset(pName, 0, 512);
    int ll = filename1.length();
    memcpy(pName, pBuffer, ll);
    for (int i = 0; i < ll; i++) {
        if (pName[i] >= 48 && pName[i] <= 57) {
            number += pName[i];
        }
        else {
            if (!number.empty()) {
                if (numbercount == 0) {
					SeaState = stof(number);
                }
                else if(numbercount == 1)  {
					pit = stof(number);
                }
                number.clear();
                numbercount++;
            }
        }
    }

}

stClutterSeaStateLink* ClassClutterRcsRead::readClutterRCSFile(char* filename2)
{
    string filename = (string)filename2;
	vector<string> files;

    int filecount = 0;

	QDir dir(filename.c_str());
	if (!dir.exists()){
		return 0;
	}
	QStringList filters("*.sea");
	dir.setFilter(QDir::Files | QDir::NoSymLinks);
	dir.setNameFilters(filters);
	filecount = dir.count();
	if (filecount <= 0){
		return 0;
	}

	QFileInfoList list = dir.entryInfoList();
	std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;
	for (int i = 0; i < list.size(); i++)
	{
		QFileInfo fileInfo = list.at(i);
		QString qstr = fileInfo.absoluteFilePath();
		QByteArray byteArr = qstr.toLocal8Bit();
		string str = string(byteArr);
		files.push_back(str);
	}
	stClutterSeaStateLink *pStClutterSeaStateLinkNode = (stClutterSeaStateLink*)_MallocMemObj(stClutterSeaStateLink); pStClutterSeaStateLinkNode->init();
	stClutterPitLink *pStClutterPitLinkHead = (stClutterPitLink*)_MallocMemObj(stClutterPitLink); pStClutterPitLinkHead->init();
	pStClutterSeaStateLinkNode->pStClutterPitLink = pStClutterPitLinkHead;

	for (int fileIndex = 0; fileIndex < filecount; fileIndex++) 
	{
        double SeatSatae = 0;
        double Pit = 0;
        extractNumbers((char*)files[fileIndex].c_str(), SeatSatae, Pit);

		stClutterPitLink *pStClutterPitLink = (stClutterPitLink*)_MallocMemObj(stClutterPitLink); pStClutterPitLink->init();
        pStClutterPitLink->pMissileDataGPU.x    = (float*)_MallocMemObj(stDateStruct);
        pStClutterPitLink->pMissileDataGPU.y    = (float*)_MallocMemObj(stDateStruct);
        pStClutterPitLink->pMissileDataGPU.z    = (float*)_MallocMemObj(stDateStruct);
        pStClutterPitLink->pMissileDataGPU.rcs  = (float*)_MallocMemObj(stDateStruct);
        pStClutterPitLink->pMissileDataGPU.phi  = (float*)_MallocMemObj(stDateStruct);

		pStClutterPitLink->SeaState = SeatSatae;
		pStClutterPitLink->pit = Pit;

        string file = files[fileIndex];
        QFile *pQFile = new QFile;
        QString str = QString(file.c_str());
        pQFile->setFileName(str);
        if(pQFile->open(QFile::ReadOnly))
        {
            pQFile->read((char*)&pStClutterPitLink->pointNum,sizeof(int));
            pQFile->read((char*)pStClutterPitLink->pMissileDataGPU.x,sizeof(float)*pStClutterPitLink->pointNum);
            pQFile->read((char*)pStClutterPitLink->pMissileDataGPU.y,sizeof(float)*pStClutterPitLink->pointNum);
            pQFile->read((char*)pStClutterPitLink->pMissileDataGPU.z,sizeof(float)*pStClutterPitLink->pointNum);
            pQFile->read((char*)pStClutterPitLink->pMissileDataGPU.rcs,sizeof(float)*pStClutterPitLink->pointNum);
            pQFile->read((char*)pStClutterPitLink->pMissileDataGPU.phi,sizeof(float)*pStClutterPitLink->pointNum);
            pQFile->close();
        }
        delete pQFile;

		AddLink(pStClutterSeaStateLinkNode->pStClutterPitLink, pStClutterPitLink);
    }

	return pStClutterSeaStateLinkNode;
}

//根据海况和擦地角遍历数据文件，提取最终的面目标杂波散射数据
stClutterPitLink* ClassClutterRcsRead::CalClutterRcs(int hk, float pit,stClutterSeaStateLink* pSt)
{
    stClutterSeaStateLink* closestFile = nullptr;
    stClutterSeaStateLink* pStClutterSeaStateLinkFind = pSt;
    while (pStClutterSeaStateLinkFind->next) {
        pStClutterSeaStateLinkFind = pStClutterSeaStateLinkFind->next;
        if(hk == pStClutterSeaStateLinkFind->iSeatState){
            closestFile = pStClutterSeaStateLinkFind;
        }
    }
    float minpitDistance = FLT_MAX;
    float pitdistance;
    stClutterPitLink*  finalClosest = nullptr;
    stClutterPitLink * pStClutterPitLinkFind = closestFile->pStClutterPitLink;
    while (pStClutterPitLinkFind->next) {
        pStClutterPitLinkFind = pStClutterPitLinkFind->next;
        pitdistance = abs(pit - pStClutterPitLinkFind->pit);
        if (pitdistance < minpitDistance) {
            minpitDistance = pitdistance;
            finalClosest = pStClutterPitLinkFind;
        }
    }
    return finalClosest;
}


