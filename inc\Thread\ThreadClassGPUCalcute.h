/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassGPUCalcute.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassGPUCalcute.h $
 *         $Author: yening $
 *         $Revision: 507 $
 *         $Date: 2025-05-27 19:14:15 $
*******************************************************************************************/
#ifndef THREADCLASSGPUCALCUTE_H
#define THREADCLASSGPUCALCUTE_H

#include <QObject>

#include "ThreadClassSarEcho.h"
#include "ThreadClassClutter.h"
#include "ThreadClassGenJAM.h"
#include "ThreadClassAntennaGen.h"
#include "ThreadClassChaffEcho.h"
#include "ThreadSocket.h"
#include "ThreadEchoDataFormat.h"
#include"ThreadEchoDataSave.h"
#include"ThreadClassJFEcho.h"
#include "ThreadClassBlanketJamGen.h"
#include "ThreadClassSarEchoClutter.h"
#include "ThreadClassDeceptionJamGen.h"

#include"_stumempool.h"
#include "MyMalloc.h"
#include "MyMallocCUDA.h"

#include "globalExternalStruct.h"

#define MAX_GPU_NUM 8

class ThreadClassGPUCalcute : public QObject, _StuMemPool, MyMalloc,MyMallocCUDA
{
    Q_OBJECT
public:
    explicit ThreadClassGPUCalcute(int Dev = 0,QObject *parent = 0);
    ~ThreadClassGPUCalcute();
    
    void InitClutterRcsPara(void *p);

private:
    //运算参数参数初始化
    void UpdateCalcutePara(RADAR *ParamR, ANTPARAM *AntParam, SimStruct *SimData);

public:
    //初始化GPU运算资源
    bool InitGPUResource(int *GPU_IDInfo,stTaskInfo *pStTaskInfo);
	//目标RCS初始化
	void InitTarRcsPara(void *p);
	void InitTarClutterPara(void *p);

signals:
    void sendASKWaveDate();
    void sendWaveShow(void *p);
	void sendGenSenceEcho(void *p);
	void sendEchoScheduling(void *p);

public slots:
    //回波生成
    void slotASKWaveDate();
    void slotWaveShow(void *p);
    void slotSettingSarSence(void *p);
	void slotEchoGPUCalcute(void *p);
	void slotTarEchoDateGen(int Dev, void*p, void*p2);

	void slotSettingAntPara(void *p, void *pc);
	void slotSettingClutter(void *p, void *pc);
	//void slotActiveJamEchoDateGen(void *p);

    
private:
    ThreadClassSarEcho			*pThreadClassSarEcho;
    ThreadClassClutter			*pThreadClassClutter;
    ThreadClassGenJAM			*pThreadClassGenJAM;
    ThreadClassAntennaGen		*pThreadClassAntennaGen;
    ThreadClassJFEcho			*pThreadClassJFEcho;
	ThreadClassChaffEcho		*pThreadClassChaffEcho;
	ThreadClassSarEchoClutter	*pThreadClassSarEchoClutter;
	ThreadClassBlanketJamGen	*pThreadClassBlanketJamGen;
	ThreadClassDeceptionJamGen	*pThreadClassDeceptionJamGen;
    
private:
    int         m_DevID;
    char        *d_BufferFather, *d_BufferInit;
    long long   BufferInit_SizeTotal, BufferInitTarRcs_Size,BufferInit_Cur;
    float       *dev_SumAntennaFunction,*dev_AziSubAntennaFunction,*dev_PitSubAntennaFunction;
    int         CosValueLen;//三角函数表长
    float       *dev_CosValue, *dev_SinValue;//三角函数查表显存
    float       *m_CosValue, *m_SinValue;

    int     m_GPU_Num;
    int     m_GPU_ID[MAX_GPU_NUM];
    bool    bGenJAM_Flag;//蓝方发送是否启动干扰信号生成
    int     m_Dev;
	cufftHandle m_planEchoCvntModu[128];
    curandGenerator_t   gen_curand;
    cufftHandle         plan[CUDAStreamMaxNum];
    cudaStream_t        cudaStream[CUDAStreamMaxNum];

	Complexf *dev_SumEchoDataPolar1;
	Complexf *dev_AziEchoDataPolar1;
	Complexf *dev_EleEchoDataPolar1;
	Complexf *dev_ExEchoDataPolar1;

	Complexf *dev_SumEchoDataPolarFinal;
	Complexf *dev_AziEchoDataPolarFinal;
	Complexf *dev_EleEchoDataPolarFinal;
	Complexf *dev_ExEchoDataPolarFinal;

    float *dev_Target_ALL1;//x
    float *dev_Target_ALL2;//y
    float *dev_Target_ALL3;//z
    float *dev_Target_ALL4;//amp
    float *dev_Target_ALL5;//phi

	double m_Fs, m_Br, m_Tp;
	int m_TrNum;
	Complexf *dev_Signal_Send;
	Complexf *dev_Signal;

    ANTPARAM *m_AntParam;
	stEchoPara *pStEchoPara;
    
	//初始化cuda参数
	bool InitCUDAPara();
    //初始化GPU设备工作参数
    bool InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic);
    //天线参数初始化
    void SettingAntPara(void *p);
	//回波卷积调制
	void EchoCovtModu(stEchoData *pStEchoData, Complexf *dev_Signal, float *dev_fd);

    //传函回波调制
    void GenEchoWave(void *p,stSarEchoPara *pStSarEchoPara,stEchoData *pStEchoData);
	//传函回波调制HCC
	void GenEchoWaveHCC(void *p, stSarEchoPara *pStSarEchoPara, stEchoData *pStEchoData);
	//回波调制FD
	void GenEchoWaveFd(void *p, stSarEchoPara *pStSarEchoPara, stEchoData *pStEchoData);
};

#endif // THREADCLASSGPUCALCUTE_H
