/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassSendWaveGen.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassSendWaveGen.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef THREADCLASSSENDWAVEGEN_H
#define THREADCLASSSENDWAVEGEN_H

#include <QObject>
#include"MyMalloc.h"
#include"MyMallocCUDA.h"

#include "WhiteCalGlobel.h"

class ThreadClassSendWaveGen : public QObject,MyMalloc,MyMallocCUDA
{
    Q_OBJECT
public:
    explicit ThreadClassSendWaveGen(QObject *parent = 0);
    ~ThreadClassSendWaveGen();
public:
	//为防止该类被多次实例化，造成不稳定因素，故设计为单例模式
	static ThreadClassSendWaveGen* Instance()
	{
		static ThreadClassSendWaveGen	Obj;
		return &Obj;
	}
    
public:
    //产生发射信号
    void RadarSendWaveGen(RADAR *ParamR,Complex *BaseSignal);
    void RadarSendWaveGen(RADAR *ParamR,Complexd *BaseSignal);
    void RadarSendWaveGen(RADAR *ParamR,double *Real,double *Imag,double FreqShift = 0);
    
    //生成发射信号
    void TransSignalSim(RADAR *ParamR, SimStruct *SimData, int TrNum, Complexf *BaseSignal);
    //计算池生成辐射源
    void RadiatorSignalSim(RADAR *SourceRadar[40], SimStruct *SimData, 
                            Complex *SumSourcePolar1, Complex *AziSourcePolar1, Complex *EleSourcePolar1, Complex *ExSourcePolar1, 
                            Complex *SumSourcePolar2, Complex *AziSourcePolar2, Complex *EleSourcePolar2, Complex *ExSourcePolar2);
    
    //相位编码码元生成
    void PSK_Code_Gen(int CodeLen,int *CodeSymbol);

signals:

public slots:
    
private:
    //产生发射信号
    template <class T,class T2>
    void SendWaveGen(RADAR *ParamR,T *BaseSignal,T2 *Real,T2 *Imag,int GenMode);
    //产生发射信号
    template <class T,class T2>
    void SendWaveGen(RADAR *ParamR,T *BaseSignal,T2 *Real,T2 *Imag,int GenMode,double FreqShift);
};

#endif // THREADCLASSSENDWAVEGEN_H
