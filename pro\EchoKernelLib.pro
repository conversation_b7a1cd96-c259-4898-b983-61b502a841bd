# =========================================================================================
# FileProperties:
#     FileName: EchoKernelLib.pro
#     SvnProperties: 
#         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/pro/EchoKernelLib.pro $
#         $Author: yening $
#         $Revision: 202 $
#         $Date: 2025-02-13 18:50:42 $
# =========================================================================================
#-------------------------------------------------
#
# Project created by QtCreator 2024-04-01T10:50:55
#
#-------------------------------------------------

QT       -= core gui opengl

greaterThan(QT_MAJOR_VERSION, 4): QT -= widgets

CONFIG(debug, debug|release){
    TARGET = _Echo_Kerneld
}
else{
    TARGET = _Echo_Kernel
}


TEMPLATE = lib

DESTDIR += ../dll

DEFINES += _ECHO_KERNEL_LIBRARY

CONFIG +=plugin
CONFIG  += c++11


INCLUDEPATH +=  ../inc \
                ../../../inc \
                ../../../cuda/inc \


SOURCES +=  \

HEADERS  += ../../../inc/WhiteCalGlobel.h \
            ../../../cuda/inc/KernelSar.h     \
            ../../../cuda/inc/KernelCultter.h \
            ../../../cuda/inc/KernelPublic.h \
            ../../../cuda/inc/KernelJAM.h \
            ../../../cuda/inc/KernelAntenna.h \
            ../../../cuda/inc/KernelSpline.h

#Cuda sources
CUDA_SOURCES += \
                ../../../cuda/src/KernelSar.cu    \
                ../../../cuda/src/KernelCultter.cu \
                ../../../cuda/src/KernelJAM.cu    \
                ../../../cuda/src/KernelPublic.cu \
                ../../../cuda/src/KernelAntenna.cu \
                ../../../cuda/src/KernelSpline.cu

#windows下配置CUDA环境依赖
win32{


}
unix{
#cuda

     LIBS += -L"/usr/local/lib" \
         -L"/usr/local/cuda/lib64" \
         -lcuda \
         -lcufft \
         -lcudart

     CUDA_SDK = "/usr/local/cuda"   # Path to cuda SDK install
     CUDA_DIR = "/usr/local/cuda"            # Path to cuda toolkit install
     SYSTEM_NAME = linux         # Depending on your system either 'Win32', 'x64', or 'Win64'
     SYSTEM_TYPE = 64            # '32' or '64', depending on your system
     CUDA_ARCH = sm_61          # Type of CUDA architecture, for example 'compute_10', 'compute_11', 'sm_10'
     NVCC_OPTIONS = --use_fast_math

    NVCC_OPTIONS = -g
    NVCC_FLAGS += -Xcompiler -fPIC


     INCLUDEPATH += $$CUDA_DIR/include
     QMAKE_LIBDIR += $$CUDA_DIR/lib64/

     CUDA_OBJECTS_DIR = ./

     CUDA_LIBS = cuda cudart cufft
     CUDA_INC = $$join(INCLUDEPATH,'" -I"','-I"','"')
     NVCC_LIBS = $$join(CUDA_LIBS,' -l','-l', '')

     CONFIG(debug, debug|release) {
         # Debug mode
         cuda_d.input = CUDA_SOURCES
         cuda_d.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}_cuda.o
         cuda_d.commands = $$CUDA_DIR/bin/clang++ -D_DEBUG $$NVCC_OPTIONS $$CUDA_INC $$NVCC_LIBS --machine $$SYSTEM_TYPE -arch=$$CUDA_ARCH -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME}
         cuda_d.dependency_type = TYPE_C
         QMAKE_EXTRA_COMPILERS += cuda_d
     }
     else {
         # Release mode
         cuda.input = CUDA_SOURCES
         cuda.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}_cuda.o
         cuda.commands = $$CUDA_DIR/bin/clang++ $$NVCC_OPTIONS $$CUDA_INC $$NVCC_LIBS --machine $$SYSTEM_TYPE -arch=$$CUDA_ARCH -O3 -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME}
         cuda.dependency_type = TYPE_C
         QMAKE_EXTRA_COMPILERS += cuda
     }

}


