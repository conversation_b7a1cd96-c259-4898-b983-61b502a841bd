/*******************************************************************************************
 * FileProperties: 
 *     FileName: MyMalloc.cpp
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/MemPool/MyMalloc.cpp $
 *         $Author: yening $
 *         $Revision: 19 $
 *         $Date: 2024-12-22 15:19:29 $
*******************************************************************************************/
#include"MyMalloc.h"
#include<stdlib.h>
#include<string.h>
#include<stdio.h>
#ifdef cuda
#include <cuda_runtime_api.h>
#endif

MyMalloc::MyMalloc()
{
    m_Buffer = 0;

}

bool MyMalloc::InitMyMalloc(long long bytes)
{
#ifdef cuda
	cudaMallocHost((void**)&m_Buffer, bytes + 32);
#else
    m_Buffer = (char*)malloc(bytes + 32);
#endif
    if(m_Buffer == 0)
        return false;
	memset(m_Buffer, 0, bytes + 32);
    m_BufferLength = bytes;

    return true;
}

char* MyMalloc::_Malloc(long long bytes)
{
	long long length = *(long long*)m_Buffer;
    if (length + bytes < m_BufferLength)
    {
         int N = (bytes + 8)/8*8;
		 *(long long*)m_Buffer = (long long)(length + N);
        return m_Buffer + length + 32;
    }
    return 0;
}
void MyMalloc::_Release_Malloc()
{
	*(long long*)m_Buffer = (long long)(0);
}
