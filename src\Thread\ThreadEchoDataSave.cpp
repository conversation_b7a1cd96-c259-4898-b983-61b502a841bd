/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadEchoDataFormat.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadEchoDataSave.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadEchoDataSave.h"
#include<QThread>
#include <QFile>
#include<QDateTime>
#include "ThreadSocketTCP.h"

ThreadEchoDataSave::ThreadEchoDataSave(QObject *parent) : QObject(parent)
{
    SetMemPoolSrcFileName((char*)"ThreadEchoDataSave", sizeof("ThreadEchoDataSave"));
	_DefineMemPool(stEchoDataNuffer, 10);

    pQFile = nullptr;
	m_AmpMax = 1;
    m_pBuffer = (char*)malloc(10*1024*1024);

	pQFileReplay	= nullptr;
	m_ReplayFlagSAR = false;

	connect(this, SIGNAL(sendReplaySar()), this, SLOT(slotReplay()));
	//定时器监视UDP连接
	timer = new QTimer(this);
	connect(timer, SIGNAL(timeout()), this, SLOT(handleTimeout()));
	timer->start(10);

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadEchoDataSave::~ThreadEchoDataSave()
{

}
void ThreadEchoDataSave::handleTimeout()
{
	if (pQFileReplay != nullptr)
	{
		emit sendReplaySar();
	}
}
void ThreadEchoDataSave::EchoWaveDataFormat(int dot,int &offset,Complexf *pComplexf,char *pBuffer)
{
    //*(int*)(pBuffer + offset) = m_Nr;offset += sizeof(int);
    //*(int*)(pBuffer + offset) = m_Rmin;offset += sizeof(int);
    Complexs *pComplexs = (Complexs*)(pBuffer + offset);
    if(dot > 0)
    {
		float ampCoef = 1.0 / m_AmpMax*2e4;
        for(int i = 0; i < dot;i++)
        {
			pComplexs[i].x = pComplexf[i].x*ampCoef + 0.5f;
			pComplexs[i].y = pComplexf[i].y*ampCoef + 0.5f;
        }
        offset += dot*sizeof(Complexs);
    }
}

void ThreadEchoDataSave::platformPara(int &offset, PLATFORM *PlatForm, char *pBuffer)
{

}

float ThreadEchoDataSave::AmpMax(int dot, float m_AmpMax,Complexf *pComplexf)
{
	float ampMax = -1e5;
	for (int i = 0; i < dot; i++)
	{
		float amp = abs(pComplexf[i].x);
		if (ampMax < amp){
			ampMax = amp;
		}

		amp = abs(pComplexf[i].y);
		if (ampMax < amp){
			ampMax = amp;
		}
	}

	if (abs(m_AmpMax - ampMax) / m_AmpMax > 0.2f)
	{
		m_AmpMax = ampMax;
	}

	return m_AmpMax;
}
void ThreadEchoDataSave::EchoDataSave(void *p)
{
	stEchoData *pStEchoData = (stEchoData*)p;
	double X = 0, Y = 0, Z = 0;
	QFile *pQFile = new QFile;
	pQFile->setFileName("..\\..\\..\\data\\SaveDataNew1");
	pQFile->open(QFile::WriteOnly | QFile::Append);
	for (int i = 0; i < pStEchoData->Na_Save; i++)
	{
		pQFile->write((char*)&pStEchoData->Rmin, sizeof(double));
		pQFile->write((char*)&pStEchoData->PlatForm[i].NorthPos, sizeof(double));
		pQFile->write((char*)&pStEchoData->PlatForm[i].SkyPos, sizeof(double));
		pQFile->write((char*)&pStEchoData->PlatForm[i].EastPos, sizeof(double));
		pQFile->write((char*)&X, sizeof(double));
		pQFile->write((char*)&Y, sizeof(double));
		pQFile->write((char*)&Z, sizeof(double));
		pQFile->write((char*)&pStEchoData->AziEleFlag, sizeof(int));
		pQFile->write((char*)&pStEchoData->SimData.SaveFlie_Nr, sizeof(int));
		if (pStEchoData->AziEleFlag == 0)
		{
			pQFile->write((char*)&pStEchoData->SumEchoDataPolar1[i*pStEchoData->SimData.SaveFlie_Nr], sizeof(Complexf)*pStEchoData->SimData.SaveFlie_Nr);
		}
		else
		{
			pQFile->write((char*)&pStEchoData->SumEchoDataPolar1[i*pStEchoData->SimData.SaveFlie_Nr], sizeof(Complexf)*pStEchoData->SimData.SaveFlie_Nr);
			pQFile->write((char*)&pStEchoData->AziEchoDataPolar1[i*pStEchoData->SimData.SaveFlie_Nr], sizeof(Complexf)*pStEchoData->SimData.SaveFlie_Nr);
			pQFile->write((char*)&pStEchoData->EleEchoDataPolar1[i*pStEchoData->SimData.SaveFlie_Nr], sizeof(Complexf)*pStEchoData->SimData.SaveFlie_Nr);
		}
	}
	pQFile->close();
	delete pQFile;
}

void ThreadEchoDataSave::slotEchoDataSave(void *p)
{
    stFrameLink *pStFrameLinkNode = (stFrameLink*)p;

    int Frame = pStFrameLinkNode->childFrameNum;

    for(int i = 0; i < Frame;i++)
    {
		EchoDataSave(pStFrameLinkNode->p[i]);
        _ReleaseMemObj(stEchoData,pStFrameLinkNode->p[i]);
    }
    _ReleaseMemObj(stFrameLink,pStFrameLinkNode);

}

void ThreadEchoDataSave::slotCreateFile()
{
    qint64 cur = QDateTime::currentMSecsSinceEpoch();
    QString strFileName = QString("Echo%1").arg(cur%10000);
    strFilePath = QString("D:\\yening\\PRJ\\PR2401_063\\5_from_yening\\EchoInterfeCalcutePool\\doc\\%1").arg(strFileName);
}

void ThreadEchoDataSave::slotReplayFlag(int flag)
{
	if (flag == 1)
	{
		QString strFileName = QString("Echo1029");
		strFilePath = QString("D:\\yening\\PRJ\\PR2401_063\\5_from_yening\\EchoInterfeCalcutePool\\doc\\%1").arg(strFileName);
		pQFileReplay = new QFile;
		pQFileReplay->setFileName(strFilePath);
		int ret = pQFileReplay->open(QFile::ReadOnly);
		if (ret == 1)
		{
			m_ReplayFlagSAR = flag;
			return;
		}
		delete pQFileReplay;
		pQFileReplay = nullptr;
	}
	else
	{
		m_ReplayFlagSAR = false;
		if (pQFileReplay != nullptr)
		{
			if (pQFileReplay->isOpen())
			{
				pQFileReplay->close();
			}
			delete pQFileReplay;
			pQFileReplay = nullptr;
		}
	}


	
}
void ThreadEchoDataSave::slotReplay()
{
	stEchoDataNuffer *pStEchoDataBuffer = (stEchoDataNuffer*)_MallocMemObj(stEchoDataNuffer);
	char *pBuffer = (char*)pStEchoDataBuffer;
	qint64 ret = pQFileReplay->read(pBuffer, sizeof(MessageHead));
	if (ret < sizeof(MessageHead))
	{
		pQFileReplay->close();
		delete pQFileReplay;
		pQFileReplay = nullptr;
		m_ReplayFlagSAR = false;
		_ReleaseMemObj(stEchoDataNuffer, pStEchoDataBuffer);
		return;
	}
	MessageHead *pMessageHead = (MessageHead*)pBuffer;
	ret = pQFileReplay->read(pBuffer + sizeof(MessageHead), pMessageHead->uLen - sizeof(MessageHead));
	if (ret == (pMessageHead->uLen - sizeof(MessageHead)))
		emit sendEchoDataImag(pStEchoDataBuffer);
	else
	{
		pQFileReplay->close();
		delete pQFileReplay;
		pQFileReplay = nullptr;
		m_ReplayFlagSAR = false;
		_ReleaseMemObj(stEchoDataNuffer, pStEchoDataBuffer);
	}
		
}

