/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassSarEcho.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassSarEcho.h $
 *         $Author: yening $
 *         $Revision: 185 $
 *         $Date: 2025-02-10 13:48:55 $
*******************************************************************************************/
#ifndef THREADCLASSSARECHO_H
#define THREADCLASSSARECHO_H

#include <QObject>

#include<curand.h>
#include<cufft.h>

#include"MyMalloc.h"
#include"MyMallocCUDA.h"
#include"_stumempool.h"
#include"ClassRcsRead.h"
#include"ClassCoorDinate.h"

#include "WhiteCalGlobel.h"


class ThreadClassSarEcho : public QObject, MyMalloc, MyMallocCUDA, ClassRcsRead
{
    Q_OBJECT
public:
    explicit ThreadClassSarEcho(int DevID = -1,int buffer_size = 0,char *d_Buffer = nullptr,QObject *parent = 0);
    ~ThreadClassSarEcho();


	//加载目标RCS系数
	void InitTarRcsPara(void *p);
    
public:
    //初始化GPU设备工作参数
	bool InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic);
    //初始化cuda参数
    bool InitCUDAPara();
    //目标散射系数提取
    void ExtractTarScatterCoef(Target *pTarget, PLATFORM *pPlatForm, SimStruct *SimData);
    void GenSenceEcho(void *p, stEchoData *pStEchoData);

signals:

private:
    int m_DevID;
	char *d_BufferFather, *d_BufferInit;
	long long BufferInit_SizeTotal, BufferInitTarRcs_Size,BufferInit_Cur;


	float *dev_Target_ALL1, *dev_Target_ALL2, *dev_Target_ALL3, *dev_Target_ALL4, *dev_Target_ALL5;
    
	cudaEvent_t e_start, e_stop, e_start2, e_stop2;

	
	stTarLink *m_StTarLink;
	char				*m_d_BufferTarRCS, *m_pBufferTarRCS;
	unsigned long long	m_BytesOffsetTarRCS;
    unsigned int m_SacreNum;
	int m_FdFlag;

    ClassCoorDinate *pClassCoorDinate;
	stEchoData *m_pStEchoData;
};

#endif // THREADCLASSSARECHO_H
