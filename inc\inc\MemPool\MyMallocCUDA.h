/*******************************************************************************************
 * FileProperties: 
 *     FileName: MyMallocCUDA.h
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/MemPool/MyMallocCUDA.h $
 *         $Author: yening $
 *         $Revision: 19 $
 *         $Date: 2024-12-22 15:19:29 $
*******************************************************************************************/
#ifndef MYMALLOCCUDA
#define MYMALLOCCUDA

class MyMallocCUDA
{
public:
    MyMallocCUDA();

public:
	bool InitMyMallocCUDA(void *p, long long bytes);
    char *_MallocCUDA(int bytes);
    void _Release_MallocCUDA();

private:
    char		*m_BufferCUDA;
    long long	m_BufferLengthCUDA;
    
    long long	m_lengthCur;


};

#endif // MYMALLOCCUDA

