/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassInterference.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassGenJAM.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassGenJAM.h"
#include<QThread>
#include <QFile>
#include <QDebug>

#include "ThreadClassSendWaveGen.h"
#include <curand.h>
#include <cuda_runtime_api.h>
#include"KernelJAM.h"
#include"KernelPublic.h"

ThreadClassGenJAM::ThreadClassGenJAM(QObject *parent) : QObject(parent)
{
    _DefineMemPoolCUDA(stEchoData, 2);
	//分配CPU伪内存池空间
    InitMyMalloc(64 * 1024 * 1024);

	//三角函数查表
	double CosValueSample = 1.0 / 4096;
	CosValueLen = 4100;
	m_CosValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_CosValue, 0, sizeof(float)*CosValueLen);
	m_SinValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_SinValue, 0, sizeof(float)*CosValueLen);
	for (int idd = 0; idd < CosValueLen; idd++)
	{
		if (CosValueSample*idd <= 1)
		{
			m_CosValue[idd] = cos(idd*CosValueSample * 2 * PI);
			m_SinValue[idd] = sin(idd*CosValueSample * 2 * PI);
		}
	}


    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassGenJAM::~ThreadClassGenJAM()
{
    
}
//初始化GPU设备工作参数
bool ThreadClassGenJAM::InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer)
{
	BufferInit_Cur = 0;
	BufferInit_SizeTotal = (long long)32 * 1024 * 1024;
	if (d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
		m_DevID = DevID;
		d_BufferInit = d_Buffer;
		d_BufferFather = d_Buffer + BufferInit_SizeTotal;
		//分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, buffer_size);
	}
	else{//作为独立的线程使用，单独使用一个GPU卡
		m_DevID = -1;
		d_BufferFather = nullptr;
		//分配GPU伪内存池空间
		cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
		cudaMalloc((void**)&d_Buffer, buffer_size);
		d_BufferFather = d_Buffer;
		InitMyMallocCUDA(d_BufferFather, buffer_size);
	}
	//三角函数查表
	long long Bytes = (CosValueLen*sizeof(float) + 16) / 16 * 16;
	dev_CosValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_CosValue, m_CosValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
	dev_SinValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_SinValue, m_SinValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
    return true;
}
//初始化cuda参数
bool ThreadClassGenJAM::InitCUDAPara()
{
	int    BATCH = 1;
	
	cufftPlan1d(&plan, 16384, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_1k, 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_2k, 2*1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_4k, 4 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_8k, 8 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_16k, 16 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_32k, 32 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_64k, 64 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_128k, 128 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_256k, 256 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_512k, 512 * 1024, CUFFT_C2C, BATCH);

    curandCreateGenerator(&gen_curand, CURAND_RNG_PSEUDO_MRG32K3A);

	return true;
}
cufftHandle ThreadClassGenJAM::CufftPlanCheck(int fftDot)
{
    switch (fftDot)
    {
    case 1024:return plan_1k;
    case 2*1024:return plan_2k;
    case 4*1024:return plan_4k;
    case 8*1024:return plan_8k;
    case 16*1024:return plan_16k;
    case 32*1024:return plan_32k;
    case 64 * 1024:return plan_64k;
    case 128 * 1024:return plan_128k;
    case 256 * 1024:return plan_256k;
    case 512 * 1024:return plan_512k;
    default:return -1;
    }
    return -1;
}
template<class T,class T2,class T3,class T4,class T5,class T6,class T7>
void ThreadClassGenJAM::CalAntWeight(ThreeDimension Tar2Radar, T *G_Matrix, T2 *AntFuction, T3 *AziAntFuction, T4 *PitAntFuction, T5 *Param, T6 *AntGain, T6 *AziGain, T6 *EleGain, T7 *Range, int AziEleFalg)
{
    double DeltaAzi = Param[0];
    double DeltaEle = Param[1];
    double AziBeamLen = Param[2];
    double EleBeamLen = Param[3];
    double AziSpace = Param[4];
    double EleSpace = Param[5];
    double Fc = Param[6];
    double AziSlope = Param[7];
    double EleSlope = Param[8];
    double AziOverlap = Param[9];
    double EleOverlap = Param[10];
    int AziOverlapLen = (int)(AziOverlap / DeltaAzi / 2 + 0.5);		//方位向半重叠波束长度
    int EleOverlapLen = (int)(EleOverlap / DeltaEle / 2 + 0.5);		//俯仰向半重叠波束长度
    int AziStartID, AziEndID, EleStartID, EleEndID;
    AziStartID	= int(-AziOverlapLen + AziBeamLen / 2.0);
    AziEndID	= int(AziOverlapLen + AziBeamLen / 2.0);
    EleStartID	= int(-EleOverlapLen + EleBeamLen / 2.0);
    EleEndID	= int(EleOverlapLen + EleBeamLen / 2.0);

    double Radar2Target[3];
    //Radar2Target[0] = Tar2Radar.x;
    //Radar2Target[1] = Tar2Radar.y;
    //Radar2Target[2] = Tar2Radar.z;
    Radar2Target[0] = G_Matrix[0] * Tar2Radar.x + G_Matrix[3] * Tar2Radar.y + G_Matrix[6] * Tar2Radar.z;
    Radar2Target[1] = G_Matrix[1] * Tar2Radar.x + G_Matrix[4] * Tar2Radar.y + G_Matrix[7] * Tar2Radar.z;
    Radar2Target[2] = G_Matrix[2] * Tar2Radar.x + G_Matrix[5] * Tar2Radar.y + G_Matrix[8] * Tar2Radar.z;
    *Range = sqrt(Tar2Radar.x*Tar2Radar.x + Tar2Radar.y*Tar2Radar.y + Tar2Radar.z*Tar2Radar.z);
    double AziAngle = -atan(Radar2Target[2] / Radar2Target[0]);	// 方位角
    double EleAngle = asin(Radar2Target[1] / (*Range));			// 俯仰角
    //double AziAngle    = atan(Radar2Target[2]/Radar2Target[0]) ;	// 方位角
    //double EleAngle  = asin(Radar2Target[1]/(*Range));		// 俯仰角
    if (Radar2Target[0] <= 0)	//背瓣
    {
        AziAngle = PI + AziAngle;
        //EleAngle = -PI - EleAngle;
    }
    int AziID = (int)(AziAngle / DeltaAzi + AziBeamLen / 2.0);	//方位向由负到正
    int EleID = (int)(-EleAngle / DeltaEle + EleBeamLen / 2.0);	//俯仰向由正到负
    //差斜率计算
    //AziSlope = 2*AntFuction[int(EleBeamLen/2) + (int)EleBeamLen*AziStartID]/(AziEndID-AziStartID)/AntFuction[int(EleBeamLen/2) + (int)EleBeamLen*AziID];
    //EleSlope = 2*AntFuction[EleStartID + (int)EleBeamLen*int(AziBeamLen/2)]/(AziEndID-AziStartID)/AntFuction[EleID + (int)EleBeamLen*int(AziBeamLen/2)];


    if (AziID >= 0 && AziID<AziBeamLen && EleID >= 0 && EleID<EleBeamLen)
    {
        //*AntGain = (AntFuction[EleID*(int)AziBeamLen + AziID]);
        *AntGain = AntFuction[EleID + (int)EleBeamLen*AziID];
    }
    else
        //*AntGain = (AntFuction[0]);
        *AntGain = 0;		//不在天线方向图内默认为0

    if (AziEleFalg == 1)
    {
        *AziGain = 2.0*PI*Fc / SPEEDLIGHT*AziSpace*sin(AziAngle);
        *EleGain = 2.0*PI*Fc / SPEEDLIGHT*EleSpace*sin(EleAngle);
    }
    else if (AziEleFalg == 2)
    {
        if (AziID >= 0 && AziID<AziBeamLen && EleID >= 0 && EleID<EleBeamLen)
        {
            *AntGain = (AntFuction[EleID*(int)AziBeamLen + AziID]);
            *AziGain = (AziAntFuction[EleID*(int)AziBeamLen + AziID]);
            *EleGain = (PitAntFuction[EleID*(int)AziBeamLen + AziID]);
        }
        else
        {
            *AntGain = 0;		//不在天线方向图内默认为0
            *AziGain = 0;
            *EleGain = 0;
        }
    }

}

//扫频模式频点计算
template <class T, class T2, class T3, class T4>
void ThreadClassGenJAM::SweepFreGen(int SweepType, T SweepFmin, T2 SweepFmax, T3 SweepVel, int SweepCyclePRTNum, int SweepPRTNum, T4 *SweepFCur)
{
    //// SweepType：扫频类型：0锯齿波扫描 1三角波扫描 2正弦扫描
    //// SweepFmin：扫频最小频率
    //// SweepFmax：扫频最大频率
    //// SweepVel：扫频速度
    //// SweepCyclePRTNum：扫频周期内PRT个数
    //// SweepPRTNum：扫频时间内PRT个数
    //// PRT：脉冲重复周期

    int idx;
    if (SweepType == SawtoothSweep)
    {
        //锯齿波扫频
        for (idx = 0; idx < SweepPRTNum; idx++)
        {
            SweepFCur[idx] = SweepFmin + SweepVel*(idx % SweepCyclePRTNum);
        }
    }
    else if (SweepType == TriangleSweep)
    {
        //三角波扫频
        int SweepTriMod;
        for (idx = 0; idx < SweepPRTNum; idx++)
        {
            SweepTriMod = idx % SweepCyclePRTNum;
            if (SweepTriMod < round(SweepCyclePRTNum / 2.0))
            {
                SweepFCur[idx] = SweepFmin + 2 * SweepVel*SweepTriMod;
            }
            else
            {
                SweepFCur[idx] = SweepFmax + 2 * SweepVel*(round(SweepCyclePRTNum / 2) - 1 - SweepTriMod);
            }
        }
    }
    else if (SweepType == CosSweep)
    {
        ///正弦扫频
        int PhaseTime;
        for (idx = 0; idx < SweepPRTNum; idx++)
        {
            PhaseTime = idx % SweepCyclePRTNum;
            SweepFCur[idx] = (SweepFmin + SweepFmax) / 2.0 + (SweepFmax - SweepFmin) / 2.0 * sin(2 * PI * PhaseTime / SweepCyclePRTNum);
        }
    }
}

//生成APC矩阵
template <class T,class T2>
void ThreadClassGenJAM::GenAPC(int Na,T Prf,PLATFORM *Target,T2 *APC)
{
	int idx ;
	for(idx = 0;idx<Na;idx++)
	{
		APC[idx*3 + 0] = Target->NorthPos + Target->NorthVel*idx/Prf  + 0.5*Target->NorthAcc*(idx/Prf)*(idx/Prf);
		APC[idx*3 + 1] = Target->SkyPos   + Target->SkyVel  *idx/Prf  + 0.5*Target->SkyAcc  *(idx/Prf)*(idx/Prf);
		APC[idx*3 + 2] = Target->EastPos  + Target->EastVel *idx/Prf  + 0.5*Target->EastAcc *(idx/Prf)*(idx/Prf);
	}
}
//生成任意PRF的APC矩阵(仅用了Na-1个PRI序列的值，生成Na*3个APC序列)
template <class T,class T2>
void ThreadClassGenJAM::PRIGenAPC(int Na,T *PRI,PLATFORM *Target,ANTPARAM *AntParam,T2 *APC)
{
	int idx ;
	T SumPRI = 0;
	for(idx = 0;idx<Na;idx++)
	{
		APC[idx*3 + 0] = Target->NorthPos + Target->NorthVel*SumPRI + 0.5*Target->NorthAcc*SumPRI*SumPRI;
		APC[idx*3 + 1] = Target->SkyPos   + Target->SkyVel  *SumPRI + 0.5*Target->SkyAcc  *SumPRI*SumPRI;
		APC[idx*3 + 2] = Target->EastPos  + Target->EastVel *SumPRI + 0.5*Target->EastAcc *SumPRI*SumPRI;
		SumPRI += PRI[idx];
	}
}
//******************生成变PRT的序列************//
template <class T>
void ThreadClassGenJAM::GenPRI(RADAR *ParamR, T *PRI)
{
	int idx,idy,idz;
	int k,g;
	if(ConstantPRF == ParamR->TriggerType)	//恒定重频
	{
		for(idx=0 ,idy=0;idx < ParamR->Na;idx++,idy++)
		{
			PRI[idx] = 1/ParamR->Prf;
		}
	}
	else if(StaggeredPRF == ParamR->TriggerType)	//重频参差
	{ 
		switch(ParamR->FreamNum)
		{
			case 8:
				k = 4;g = 3;
				break;
			case 9:
				k = 1;g = 1;
				break;
			case 12:
				k = 0;g = 7;
				break;
			case 14:
				k = 0;g = 5;
				break;
			default:
				printf("雷思尼克参差信号模型中输入的帧周期数需要确认！");
		}
		idx=0 ;
		for(idy=0;idy < ParamR->Na;idy=idy+ParamR->StagNum)
		{
			idx = idx %(ParamR->FreamNum - 1);
			PRI[idy] = ParamR->PRIMin + ParamR->Tp*((k+idx*g)%(ParamR->FreamNum-1));
			for(idz= 0;idz < ParamR->StagNum;idz++)
			{
				PRI[idy+idz] = PRI[idy];
			}
			idx++;
		}
	}
	else if(JitteringPRF == ParamR->TriggerType)	//重频抖动
	{
		for(idx= 0;idx < ParamR->Na;idx = idx+ParamR->StagNum)
		{
			PRI[idx] = ParamR->BasePRI + ParamR->BasePRI*ParamR->JitGama*((T)rand() / RAND_MAX - 0.5) * 2;
			for(idz= 0;idz < ParamR->StagNum;idz++)
			{
				PRI[idx+idz] = PRI[idx];
			}
		}
	}
	else if(SlipPRF == ParamR->TriggerType)	//重频滑动
	{
		T TimeEachSum = 0;
		for(idx= 0;idx < ParamR->Na;idx = idx+ParamR->StagNum)
		{
			PRI[idx] = ParamR->BasePRI + ParamR->BasePRI*ParamR->SlipGama*sin(2*PI*TimeEachSum/ParamR->CicleTime);
			for(idz= 0;idz < ParamR->StagNum;idz++)
			{
				PRI[idx+idz] = PRI[idx];
			}
			TimeEachSum += PRI[idx];
		}
	}
}
template <class T,class T2,class T3>
void ThreadClassGenJAM::PassiveJamming(PLATFORM *PassJamPlat[TARGET_NUM], RCS Rcs[TARGET_NUM], RADAR *ParamR, ANTPARAM *AntParam, SimStruct *SimData,
                                        T *RadarAPC[TARGET_NUM], T *PassJamPlatAPC[TARGET_NUM], T *PRI[TARGET_NUM], int PassJamNum, T2 *dev_BaseSignal,
                                        T3 *dev_SumJamDataPolar1, T3 *dev_AziJamDataPolar1, T3 *dev_EleJamDataPolar1, T3 *dev_ExJamDataPolar1)
{
    int idx, idy, idz, idn;
    //double *PassJamPlatAPC[TARGET_NUM];
    int TargetNum = PassJamNum;
    long int SaveFlieLen = (long int)(SimData->SaveFlie_Na*SimData->SaveFlie_Nr);
    int PRTNum = (int)(ParamR->Fs / ParamR->Prf + 0.5);	//一个prf的点数
    int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数

    ThreeDimension TargetPos;	//目标位置
    double TranGain;			//传送功率损耗
    double DopPhase = 0;		//4.0*PI*R/Lambda;
    int DelayID = 0;			//延时
    double AntGain[1];			//和通道天线加权
    double AziGain[1];			//方位差通道天线加权
    double EleGain[1];			//俯仰差通道天线加权
    double Range[1];			//弹目距
    double Param[11];

    Param[0] = AntParam->AntAziBound / AntParam->AntFuncAziNum;
    Param[1] = AntParam->AntEleBound / AntParam->AntFuncEleNum;
    Param[2] = AntParam->AntFuncAziNum;
    Param[3] = AntParam->AntFuncEleNum;
    //if(ArryElement == AntParam->SubAntType)
    //{
    //	Param[4] = (AntParam->ArrayNumRow - 1)	 /2*C/2/ParamR->Fc;	//差波束阵元间距-行
    //	Param[5] = (AntParam->ArrayNumColumn - 1)/2*C/2/ParamR->Fc;	//差波束阵元间距-列
    //}
    //else
    //{
    //	Param[4] = (AntParam->SubArrayNumRow - 1)	/2*AntParam->SubArraySpcaeRow;	//差波束阵元间距-行
    //	Param[5] = (AntParam->SubArrayNumColumn - 1)/2*AntParam->SubArraySpcaeRow;	//差波束阵元间距-列
    //}
    Param[4] = AntParam->AziSpace;
    Param[5] = AntParam->EleSpace;
    Param[6] = ParamR->Fc;	//波长
    Param[7] = AntParam->AziSlope;		//方位方向差斜率(比幅)
    Param[8] = AntParam->EleSlope;		//俯仰方向差斜率(比幅)
    Param[9] = AntParam->AziOverlap;	//方位方向重叠波束（比幅）
    Param[10] = AntParam->EleOverlap;	//俯仰方向重叠波束（比幅）


    for (idn = 0; idn < SimData->SaveFlie_Na; idn++)
    {
        for (idy = 0; idy<TargetNum; idy++)
        {
            TargetPos.x = *(PassJamPlatAPC[idy] + idn * 3 + 0) - *(RadarAPC[idy] + idn * 3 + 0);
            TargetPos.y = *(PassJamPlatAPC[idy] + idn * 3 + 1) - *(RadarAPC[idy] + idn * 3 + 1);
            TargetPos.z = *(PassJamPlatAPC[idy] + idn * 3 + 2) - *(RadarAPC[idy] + idn * 3 + 2);
            CalAntWeight(TargetPos, AntParam->G_Matrix, AntParam->SumAntennaFunction, AntParam->AziSubAntennaFunction, AntParam->PitSubAntennaFunction, Param, AntGain, AziGain, EleGain, Range, SimData->AziEleFalg);

            DopPhase = -4.0*PI*Range[0] / ParamR->Lambda;
            TranGain = ParamR->Amp*ParamR->RadomeLoss*Rcs[idy].Sigma0*ParamR->Lambda*ParamR->Lambda
                / (64 * PI*PI*PI) / (Range[0] * Range[0] * Range[0] * Range[0]);//(Pt*Loss*lambda^2*sigma/((4pi)^3*R^4))

            if (SimData->RangeGateMode == 0)
            {
                //DelayID = (int)((2*Range[0])*(ParamR->Fs/C)) + (int)(PRTTime*ParamR->Fs+0.5);	//流信号ID
                DelayID = (int)((2 * (Range[0] - SimData->Rmin))*(ParamR->Fs / SPEEDLIGHT) + 0.5);					//帧信号ID
                if (TrNum + DelayID >= SaveFlieLen)
                    continue;

                if (0 == SimData->AziEleFalg)	//单波束
                {
                    if ((DelayID >= 0) && (DelayID < SimData->SaveFlie_Nr))		//默认不跨重
                    {
                        if (SimData->TransLossFlag == 1)
                            TranGain = TranGain*AntGain[0] * AntGain[0];			//(G^2*lambda^2*sigma/((4pi)^3*R^4))
                        else
                            TranGain = sqrt(TranGain * 50 * AntGain[0] * AntGain[0]);
                        float CosDopPhase = TranGain*cos(DopPhase);
                        float SinDopPhase = TranGain*sin(DopPhase);
                        int dot = TrNum > (SimData->SaveFlie_Nr - DelayID) ? (SimData->SaveFlie_Nr - DelayID):TrNum;
                        dim3 ThreadsPerBlock(512,1);
                        dim3 BlockNum((dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                        CUDA_JAM_PassiveAdd(0,ThreadsPerBlock, BlockNum,dot,dev_BaseSignal,CosDopPhase,SinDopPhase,(T3*)&dev_SumJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID]);
                        CUDA_JAM_PassiveAdd(0,ThreadsPerBlock, BlockNum,dot,dev_BaseSignal,CosDopPhase,SinDopPhase,(T3*)&dev_ExJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID]);
//                        for (idz = 0; (idz<TrNum) && ((DelayID + idz)<SimData->SaveFlie_Nr); idz++)
//                        {
//                            (SumJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase;
//                            (SumJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase;
//                            (SumJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase;
//                            (SumJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase;
//                            (ExJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase;
//                            (ExJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase;
//                            (ExJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase;
//                            (ExJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase;
//                        }
                    }
                }
                else if (1 == SimData->AziEleFalg)	//和差差比相
                {
                    if ((DelayID >= 0) && (DelayID < SimData->SaveFlie_Nr))    //默认不跨重
                    {
                        double Pos_Re, Pos_Im, Azi_Re, Azi_Im, Ele_Re, Ele_Im, Phase_Re, Phase_Im;
                        double SumPosCashRe, SumPosCashIm, ElePosCashRe, ElePosCashIm, AziPosCashRe, AziPosCashIm;
                        Pos_Re = cos(DopPhase);   //RCS+多普勒相位
                        Pos_Im = sin(DopPhase);
                        Azi_Re = cos(-AziGain[0]);
                        Azi_Im = sin(-AziGain[0]);
                        Ele_Re = cos(-EleGain[0]);
                        Ele_Im = sin(-EleGain[0]);

                        if (SimData->TransLossFlag == 1)
                            TranGain = TranGain*AntGain[0] * AntGain[0];		//(G^2*lambda^2*sigma/((4pi)^3*R^4))
                        else
                            TranGain = sqrt(TranGain * 50 * AntGain[0] * AntGain[0]);
                        Phase_Re = TranGain*(Pos_Re*(Azi_Re + 1) - Pos_Im* Azi_Im);
                        Phase_Im = TranGain*(Pos_Re* Azi_Im + Pos_Im*(Azi_Re + 1));
                        SumPosCashRe = Phase_Re*(1 + Ele_Re) - Phase_Im*Ele_Im;
                        SumPosCashIm = Phase_Re*Ele_Im + Phase_Im*(1 + Ele_Re);
                        ElePosCashRe = Phase_Re*(1 - Ele_Re) + Phase_Im*Ele_Im;
                        ElePosCashIm = -Phase_Re*Ele_Im + Phase_Im*(1 - Ele_Re);

                        Phase_Re = TranGain*(Pos_Re*(Ele_Re + 1) - Pos_Im* Ele_Im);
                        Phase_Im = TranGain*(Pos_Re* Ele_Im + Pos_Im*(Ele_Re + 1));
                        AziPosCashRe = Phase_Re*(1 - Azi_Re) + Phase_Im*Azi_Im;
                        AziPosCashIm = -Phase_Re*Azi_Im + Phase_Im*(1 - Azi_Re);

                        int dot = TrNum > (SimData->SaveFlie_Nr - DelayID) ? (SimData->SaveFlie_Nr - DelayID):TrNum;
                        dim3 ThreadsPerBlock(512,1);
                        dim3 BlockNum((dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                        CUDA_JAM_PassiveAdd(0,ThreadsPerBlock, BlockNum,dot,dev_BaseSignal,SumPosCashRe,SumPosCashIm,(T3*)&dev_SumJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID]);
                        CUDA_JAM_PassiveAdd(0,ThreadsPerBlock, BlockNum,dot,dev_BaseSignal,SumPosCashRe,SumPosCashIm,(T3*)&dev_ExJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID]);

                        CUDA_JAM_PassiveAdd(0,ThreadsPerBlock, BlockNum,dot,dev_BaseSignal,AziPosCashRe,AziPosCashIm,(T3*)&dev_AziJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID]);
                        CUDA_JAM_PassiveAdd(0,ThreadsPerBlock, BlockNum,dot,dev_BaseSignal,ElePosCashRe,ElePosCashIm,(T3*)&dev_EleJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID]);
//                        for (idz = 0; (idz<TrNum) && ((DelayID + idz)<SimData->SaveFlie_Nr); idz++)
//                        {
//                            (SumJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*SumPosCashRe - BaseSignal[idz].im*SumPosCashIm;
//                            (SumJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*SumPosCashRe + BaseSignal[idz].re*SumPosCashIm;
//                            (SumJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*SumPosCashRe - BaseSignal[idz].im*SumPosCashIm;
//                            (SumJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*SumPosCashRe + BaseSignal[idz].re*SumPosCashIm;
//                            (ExJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*SumPosCashRe - BaseSignal[idz].im*SumPosCashIm;
//                            (ExJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*SumPosCashRe + BaseSignal[idz].re*SumPosCashIm;
//                            (ExJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*SumPosCashRe - BaseSignal[idz].im*SumPosCashIm;
//                            (ExJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*SumPosCashRe + BaseSignal[idz].re*SumPosCashIm;
//                            (AziJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*AziPosCashRe - BaseSignal[idz].im*AziPosCashIm;
//                            (AziJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*AziPosCashRe + BaseSignal[idz].re*AziPosCashIm;
//                            (AziJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*AziPosCashRe - BaseSignal[idz].im*AziPosCashIm;
//                            (AziJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*AziPosCashRe + BaseSignal[idz].re*AziPosCashIm;
//                            (EleJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*ElePosCashRe - BaseSignal[idz].im*ElePosCashIm;
//                            (EleJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*ElePosCashRe + BaseSignal[idz].re*ElePosCashIm;
//                            (EleJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += BaseSignal[idz].re*ElePosCashRe - BaseSignal[idz].im*ElePosCashIm;
//                            (EleJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += BaseSignal[idz].im*ElePosCashRe + BaseSignal[idz].re*ElePosCashIm;
//                        }
                    }
                }
                else if (2 == SimData->AziEleFalg) //和差差比幅
                {
                    if ((DelayID >= 0) && (DelayID < SimData->SaveFlie_Nr))    //默认不跨重
                    {
                        double SumTranGain, AziTranGain, EleTranGain;
                        if (SimData->TransLossFlag == 1)
                        {
                            SumTranGain = TranGain*AntGain[0] * AntGain[0];
                            AziTranGain = TranGain*AziGain[0] * AziGain[0];
                            EleTranGain = TranGain*EleGain[0] * EleGain[0];
                        }
                        else
                        {
                            SumTranGain = sqrt(TranGain * AntGain[0] * AntGain[0]);
                            AziTranGain = sqrt(TranGain * AziGain[0] * AziGain[0]);
                            EleTranGain = sqrt(TranGain * EleGain[0] * EleGain[0]);
                        }
                        float CosDopPhase = cos(DopPhase);
                        float SinDopPhase = sin(DopPhase);
                        int dot = TrNum > (SimData->SaveFlie_Nr - DelayID) ? (SimData->SaveFlie_Nr - DelayID):TrNum;
                        dim3 ThreadsPerBlock(512,1);
                        dim3 BlockNum((dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                        CUDA_JAM_PassiveAdd(0,ThreadsPerBlock, BlockNum,dot,dev_BaseSignal,CosDopPhase,SinDopPhase,(T3*)&dev_SumJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID]);
                        CUDA_JAM_PassiveAdd(0,ThreadsPerBlock, BlockNum,dot,dev_BaseSignal,CosDopPhase,SinDopPhase,(T3*)&dev_ExJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID]);

                        CUDA_JAM_PassiveAdd(0,ThreadsPerBlock, BlockNum,dot,dev_BaseSignal,CosDopPhase,SinDopPhase,(T3*)&dev_AziJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID]);
                        CUDA_JAM_PassiveAdd(0,ThreadsPerBlock, BlockNum,dot,dev_BaseSignal,CosDopPhase,SinDopPhase,(T3*)&dev_EleJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID]);


                        CUDA_ComplexMultCoef(0,ThreadsPerBlock,BlockNum, dot, (T3*)&dev_SumJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID], (T3*)&dev_SumJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID], SumTranGain);
                        CUDA_ComplexMultCoef(0,ThreadsPerBlock,BlockNum, dot, (T3*)&dev_ExJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID], (T3*)&dev_ExJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID], SumTranGain);
                        CUDA_ComplexMultCoef(0,ThreadsPerBlock,BlockNum, dot, (T3*)&dev_AziJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID], (T3*)&dev_AziJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID], AziTranGain);
                        CUDA_ComplexMultCoef(0,ThreadsPerBlock,BlockNum, dot, (T3*)&dev_EleJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID], (T3*)&dev_EleJamDataPolar1[idn*SimData->SaveFlie_Nr + DelayID], EleTranGain);
//                        for (idz = 0; (idz<TrNum) && ((DelayID + idz)<SimData->SaveFlie_Nr); idz++)
//                        {
//                            (SumJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += SumTranGain*(BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase);
//                            (SumJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += SumTranGain*(BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase);
//                            (SumJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += SumTranGain*(BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase);
//                            (SumJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += SumTranGain*(BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase);
//                            (ExJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += SumTranGain*(BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase);
//                            (ExJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += SumTranGain*(BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase);
//                            (ExJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += SumTranGain*(BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase);
//                            (ExJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += SumTranGain*(BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase);
//                            (AziJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += AziTranGain*(BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase);
//                            (AziJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += AziTranGain*(BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase);
//                            (AziJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += AziTranGain*(BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase);
//                            (AziJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += AziTranGain*(BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase);
//                            (EleJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += EleTranGain*(BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase);
//                            (EleJamDataPolar1 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += EleTranGain*(BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase);
//                            (EleJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->re += EleTranGain*(BaseSignal[idz].re*CosDopPhase - BaseSignal[idz].im*SinDopPhase);
//                            (EleJamDataPolar2 + idn*SimData->SaveFlie_Nr + DelayID + idz)->im += EleTranGain*(BaseSignal[idz].im*CosDopPhase + BaseSignal[idz].re*SinDopPhase);
//                        }
                    }
                }
            }//End of:if(SimData->RangeGateMode == 0)固定距离门选通
        }//End of :for(idy = 0;idy<TargetNum;idy++)
    }//End of:  for(idn = 0;idn < SimData->SaveFlie_Na;idn++)
}
//和差差波形生成
void ThreadClassGenJAM::AziEleWaveGen(int AziEleFalg,int DelayID,int TrNum,Complex *JamData,Complex *AziJamData, Complex *EleJamData,double TransLoss,double *CosPhase,double *SinPhase,
       double CosDop,double SinDop,double *AziGain,double *EleGain,double DopPhase)
{
    int idx = 0,idy = 0,idz = 0;
    if(0 == AziEleFalg)	//单波束
    {
		//for (idy = 0, idz = DelayID; idy<TrNum; idy++, idz++)
        for(idy = 0,idz=0;idy<TrNum;idy++,idz++)
        {
            (JamData + idz)->x += TransLoss*(CosPhase[idy]*CosDop - SinPhase[idy]*SinDop);
            (JamData + idz)->y += TransLoss*(SinPhase[idy]*CosDop + CosPhase[idy]*SinDop);
        }
    }
    else if(1 == AziEleFalg)		//和差差（比相）
    {
        double Pos_Re,Pos_Im,Azi_Re, Azi_Im, Ele_Re, Ele_Im, Phase_Re, Phase_Im;
        double SumPosCashRe,SumPosCashIm,ElePosCashRe,ElePosCashIm,AziPosCashRe,AziPosCashIm;
        Pos_Re = cos(DopPhase); 
        Pos_Im = sin(DopPhase);
        Azi_Re = cos(-AziGain[0]);
        Azi_Im = sin(-AziGain[0]);
        Ele_Re = cos(-EleGain[0]);
        Ele_Im = sin(-EleGain[0]);

        Phase_Re        = TransLoss*(Pos_Re*(Azi_Re+1) - Pos_Im* Azi_Im);    //计算多普勒与方位乘积
        Phase_Im        = TransLoss*(Pos_Re* Azi_Im    + Pos_Im*(Azi_Re+1));
        SumPosCashRe	= Phase_Re*(1+Ele_Re) - Phase_Im*Ele_Im;      //计算和通道相位
        SumPosCashIm	= Phase_Re*Ele_Im     + Phase_Im*(1+Ele_Re);
        ElePosCashRe	= Phase_Re*(1-Ele_Re) + Phase_Im*Ele_Im;      //计算俯仰差通道相位
        ElePosCashIm	= -Phase_Re*Ele_Im    + Phase_Im*(1-Ele_Re);

        Phase_Re        = TransLoss*(Pos_Re*(Ele_Re+1) - Pos_Im* Ele_Im);    //计算多普勒与俯仰相位乘积
        Phase_Im        = TransLoss*(Pos_Re* Ele_Im    + Pos_Im*(Ele_Re+1));
        AziPosCashRe    = Phase_Re*(1-Azi_Re)  + Phase_Im*Azi_Im;     //计算方位差通道相位
        AziPosCashIm    = -Phase_Re*Azi_Im + Phase_Im*(1-Azi_Re);

        for(idy = 0,idz = DelayID;idy<TrNum;idy++,idz++)
        {
            (JamData +idz)->x    += CosPhase[idy]*SumPosCashRe - SinPhase[idy]*SumPosCashIm;
            (JamData +idz)->y    += SinPhase[idy]*SumPosCashRe + CosPhase[idy]*SumPosCashIm;
            (AziJamData +idz)->x += CosPhase[idy]*AziPosCashRe - SinPhase[idy]*AziPosCashIm;
            (AziJamData +idz)->y += SinPhase[idy]*AziPosCashRe + CosPhase[idy]*AziPosCashIm;
            (EleJamData +idz)->x += CosPhase[idy]*ElePosCashRe - SinPhase[idy]*ElePosCashIm;
            (EleJamData +idz)->y += SinPhase[idy]*ElePosCashRe + CosPhase[idy]*ElePosCashIm;
        }
    }
    else if(2 == AziEleFalg)		//和差差（比幅）
    {
        double SumTranGain,AziTranGain,EleTranGain;
        SumTranGain = TransLoss;
        AziTranGain = TransLoss*AziGain[0];
        EleTranGain = TransLoss*EleGain[0];
        for(idy = 0,idz = DelayID;idy<TrNum;idy++,idz++)
        {
            (JamData + idz)->x   += SumTranGain*(CosPhase[idy]*CosDop - SinPhase[idy]*SinDop);
            (JamData + idz)->y   += SumTranGain*(SinPhase[idy]*CosDop + CosPhase[idy]*SinDop);
            (AziJamData +idz)->x += AziTranGain*(CosPhase[idy]*CosDop - SinPhase[idy]*SinDop);
            (AziJamData +idz)->y += AziTranGain*(SinPhase[idy]*CosDop + CosPhase[idy]*SinDop);
            (EleJamData +idz)->x += EleTranGain*(CosPhase[idy]*CosDop - SinPhase[idy]*SinDop);
            (EleJamData +idz)->y += EleTranGain*(SinPhase[idy]*CosDop + CosPhase[idy]*SinDop);
        }
    }
}
//噪声生成
template <class T,class T2,class T3,class T4,class T5,class T6,class T7>
void ThreadClassGenJAM::NoiseModelGen(int NoiseSigLen, T MeanValue, T2 StdValue, T3 NoiseFc, T4 NoiseBr, T5 NoiseAmp, T6 Fs, T7 *dev_NoiseModel)
{
    //// NoiseSigLen：干扰信号长度
    //// MeanValue：高斯白噪声均值
    //// StdValue：高斯白噪声方差
    //// NoiseFc：干扰信号载频
    //// NoiseBr：干扰信号带宽
    //// NoiseAmp：干扰信号幅度
    //// Fs：采样率

    int idx, idy, idz;
    //高斯白噪声生成
    curandSetPseudoRandomGeneratorSeed(gen_curand,(time(NULL) + rand()));
    curandGenerateUniform(gen_curand,(float*)dev_RandomComplexf,NoiseSigLen*2);
//    double d1, d2;
//    T7 *GaussNoise = (fftw_complex*)fftw_malloc(sizeof(fftw_complex)*NoiseSigLen);
//    srand(time(NULL));
//    for (idx = 0; idx < NoiseSigLen; idx++)
//    {
//        d1 = double(rand()) / RAND_MAX;
//        d2 = double(rand()) / RAND_MAX;
//        GaussNoise[idx][0] = StdValue*sqrt(-2.0 * log(d1))*cos(2.0 * PI*d2) + MeanValue;
//        GaussNoise[idx][1] = StdValue*sqrt(-2.0 * log(d2))*sin(2.0 * PI*d1) + MeanValue;
//    }
    T7 *dev_GaussNoise = (T7 *)_MallocCUDA(sizeof(T7)*NoiseSigLen);
    dim3 ThreadsPerBlock(512,1);
    dim3 BlockNum((NoiseSigLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
	CUDA_Noise_Gauss(0, ThreadsPerBlock, BlockNum, NoiseSigLen, StdValue, MeanValue, dev_CosValue, dev_SinValue, dev_RandomComplexf, dev_GaussNoise);

    ///生成噪声干扰频响曲线，ifft转换到时域得到噪声干扰信号
    int NoiseFcID = round(NoiseFc / Fs*NoiseSigLen);        //载频采样位置
    int NoiseBrLen = round(NoiseBr / Fs*NoiseSigLen);       //带宽采样点数
    if (NoiseBrLen > NoiseSigLen)
        NoiseBrLen = NoiseSigLen;

    T7 *dev_NoiseSigF = (T7 *)_MallocCUDA(sizeof(T7)*NoiseSigLen);

//    fftw_complex *NoiseSigF = (fftw_complex*)fftw_malloc(sizeof(fftw_complex)*NoiseSigLen);  //噪声干扰频响曲线（内部是高斯白噪声）
//    memset(NoiseSigF, 0, sizeof(fftw_complex)*NoiseSigLen);
    int NoisePosStartID = (int(NoiseFcID - floor(NoiseBrLen / 2.0) + NoiseSigLen)) % NoiseSigLen;
    int NoisePosStopID = (int(NoiseFcID + floor(NoiseBrLen / 2.0) + NoiseSigLen)) % NoiseSigLen;
    if (NoisePosStartID <= NoisePosStopID)
    {
//        for (idy = NoisePosStartID; idy <= NoisePosStopID; idy++)
//        {
//            NoiseSigF[idy][0] = GaussNoise[idy][0] / NoiseBrLen*NoiseSigLen;
//            NoiseSigF[idy][1] = GaussNoise[idy][1] / NoiseBrLen*NoiseSigLen;
//        }
        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum(((NoisePosStopID - NoisePosStartID + 1) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        int dot = (NoisePosStopID - NoisePosStartID + 1);
		CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, dot, dev_GaussNoise, dev_NoiseSigF, 1.f / NoiseBrLen*NoiseSigLen);
    }
    else
    {
//        for (idy = NoisePosStartID; idy < NoiseSigLen; idy++)
//        {
//            NoiseSigF[idy][0] = GaussNoise[idy][0] / NoiseBrLen*NoiseSigLen;
//            NoiseSigF[idy][1] = GaussNoise[idy][1] / NoiseBrLen*NoiseSigLen;
//        }
        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum(((NoiseSigLen - NoisePosStartID) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        int dot = (NoiseSigLen - NoisePosStartID);
		CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, dot, dev_GaussNoise, dev_NoiseSigF, 1.f / NoiseBrLen*NoiseSigLen);

//        for (idy = 0; idy <= NoisePosStopID; idy++)
//        {
//            NoiseSigF[idy][0] = GaussNoise[idy][0] / NoiseBrLen*NoiseSigLen;
//            NoiseSigF[idy][1] = GaussNoise[idy][1] / NoiseBrLen*NoiseSigLen;
//        }
        dim3 ThreadsPerBlock2(512,1);
        dim3 BlockNum2(((NoisePosStopID + 1) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        dot = (NoisePosStopID + 1);
		CUDA_ComplexMultCoef(0, ThreadsPerBlock2, BlockNum2, dot, dev_GaussNoise, dev_NoiseSigF, 1.f / NoiseBrLen*NoiseSigLen);
    }

    T7 *dev_NoiseSigIFFT = (T7 *)_MallocCUDA(sizeof(T7)*NoiseSigLen);
    memset(dev_NoiseSigIFFT, 0, sizeof(T7)*NoiseSigLen);

//    fftw_complex *NoiseSigIFFT = (fftw_complex*)fftw_malloc(sizeof(fftw_complex)*NoiseSigLen);  //噪声干扰频响曲线（内部是高斯白噪声）
//    memset(NoiseSigIFFT, 0, sizeof(fftw_complex)*NoiseSigLen);
//    IFFTComplex(NoiseSigLen, NoiseSigF, NoiseSigIFFT);			//ifft
    cufftHandle cufftPlan = CufftPlanCheck(NoiseSigLen);
    _CufftExecC2C(cufftPlan,dev_NoiseSigF,dev_NoiseSigIFFT,CUFFT_INVERSE);

//    for (idz = 0; idz < NoiseSigLen; idz++)
//    {
//        NoiseModel[idz][0] = NoiseAmp * NoiseSigIFFT[idz][0];
//        NoiseModel[idz][1] = NoiseAmp * NoiseSigIFFT[idz][1];
//    }

	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, NoiseSigLen, dev_NoiseSigIFFT, dev_NoiseModel, 1.f / NoiseSigLen*NoiseAmp);
}
//压制干扰生成
template <class T,class T2>
void ThreadClassGenJAM::BlanketJamGen(JAM_PARAM JamParam, double Fs, int JamSigLen, int PRFID, int PRTLen, T *SweepFCur, T2 *BlanketJamSig)
{
    int idx, idy, idz;
    idx = PRFID;
    int PulseTrLen = round(JamParam.DutyCycle*PRTLen);

    T2 *dev_JamConSig       = (T2 *)_MallocCUDA(sizeof(T2)*PRTLen);
    T2 *dev_JamSweepSig     = (T2 *)_MallocCUDA(sizeof(T2)*PRTLen);
    T2 *dev_JamPulseSig     = (T2 *)_MallocCUDA(sizeof(T2)*PulseTrLen);
    T2 *dev_JamCombSig      = (T2 *)_MallocCUDA(sizeof(T2)*PRTLen);

    if (JamParam.BalJamType == Block)		//连续噪声
    {
        NoiseModelGen(PRTLen, 0, JamParam.AmpVar, JamParam.DeltaFc, JamParam.Br, 1, Fs, dev_JamConSig);

        cudaMemcpy(&BlanketJamSig[idx*PRTLen],dev_JamConSig,sizeof(T2)*PRTLen,cudaMemcpyDeviceToHost);
//        for (idy = 0; idy < PRTLen; idy++)
//        {
//            if (idx*PRTLen + idy < JamSigLen)
//            {
//                BlanketJamSig[idx*PRTLen + idy].x = dev_JamConSig[idy][0];
//                BlanketJamSig[idx*PRTLen + idy].y = dev_JamConSig[idy][1];
//            }
//            else
//                return;
//        }
    }//if (JamParam.BalJamType == Block)

    else if (JamParam.BalJamType == PulseJam)		//间断噪声
    {
        cudaMemset(dev_JamPulseSig, 0, sizeof(T2)*PulseTrLen);
        NoiseModelGen(PulseTrLen, 0, JamParam.AmpVar, JamParam.DeltaFc, JamParam.Br, 1, Fs, dev_JamPulseSig);

        cudaMemcpy(&BlanketJamSig[idx*PRTLen],dev_JamPulseSig,sizeof(T2)*JamSigLen,cudaMemcpyDeviceToHost);
//        for (idy = 0; idy < PulseTrLen; idy++)
//        {
//            if (idx*PRTLen + idy < JamSigLen)
//            {
//                BlanketJamSig[idx*PRTLen + idy].x = dev_JamPulseSig[idy][0];
//                BlanketJamSig[idx*PRTLen + idy].y = dev_JamPulseSig[idy][1];
//            }
//            else
//                return;
//        }
    }//else if (JamParam.BalJamType == PulseJam)

    else if (JamParam.BalJamType == Sweep)		//扫频噪声
    {
        cudaMemset(dev_JamSweepSig, 0, sizeof(T2)*PRTLen);
        NoiseModelGen(PRTLen, 0, JamParam.AmpVar, SweepFCur[idx], JamParam.Br, 1, Fs, dev_JamSweepSig);
        cudaMemcpy(&BlanketJamSig[idx*PRTLen],dev_JamPulseSig,sizeof(T2)*JamSigLen,cudaMemcpyDeviceToHost);
//        for (idy = 0; idy < PRTLen; idy++)
//        {
//            if (idx*PRTLen + idy < JamSigLen)
//            {
//                BlanketJamSig[idx*PRTLen + idy].x = dev_JamSweepSig[idy][0];
//                BlanketJamSig[idx*PRTLen + idy].y = dev_JamSweepSig[idy][1];
//            }
//            else
//                return;
//        }
    }//else if (JamParam.BalJamType == Sweep)

    else if (JamParam.BalJamType == CombSpec)		//梳状谱
    {
        T2 *dev_Temp = (T2 *)_MallocCUDA(sizeof(T2)*PRTLen);
        cudaMemset(dev_Temp,0,sizeof(T2)*PRTLen);
        for (idz = 0; idz < JamParam.CombFreqNum; idz++)
        {
            cudaMemset(dev_JamCombSig, 0, sizeof(T2)*PRTLen);
            NoiseModelGen(PRTLen, 0, JamParam.AmpVar, JamParam.CombFreqSequency[idz], JamParam.CombBrSequency[idz], JamParam.CombJamAmp[idz], Fs, dev_JamCombSig);

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((PRTLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
			CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, PRTLen, dev_JamCombSig, dev_Temp, dev_Temp);
//            for (idy = 0; idy < PRTLen; idy++)
//            {
//                BlanketJamSig[idx*PRTLen + idy].x += dev_JamCombSig[idy][0];
//                BlanketJamSig[idx*PRTLen + idy].y += dev_JamCombSig[idy][1];
//            }
        }
        cudaMemcpy(&BlanketJamSig[idx*PRTLen],dev_JamPulseSig,sizeof(T2)*JamSigLen,cudaMemcpyDeviceToHost);
    }//else if (JamParam.BalJamType == CombSpec)

}
//欺骗干扰生成
template<class T,class T2>
void ThreadClassGenJAM::DeceptionJamGen(JAM_PARAM JamParam, double Fs, double Tr, int JamSigLen, int PRFID, int PRTLen, double SimTime,
                                        double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    int TrLen = round(Tr*Fs);
    double RangeNew, PhaseValue;
    T2 DopPhase;
    int idx, idy, idz;
    int DelayID;

    idx = PRFID;

    if (SimTime >= JamParam.StartTime & SimTime <= JamParam.StopTime)
    {
        if (JamParam.PullOffType == 1)				//距离拖引
        {
            RangeNew = Range + JamParam.PullOffRan + JamParam.PullOffVel*SimTime + 0.5*JamParam.PullOffAcc*SimTime*SimTime;        //每个PRT时刻距离
            DelayID = round((2 * RangeNew / SPEEDLIGHT + SimTime)*Fs);

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
            double phase0 = -2*RangeNew / Lambda;
            double phase1Coef = (2 * (JamParam.PullOffVel + JamParam.PullOffAcc*SimTime) / Lambda)/ Fs;
			CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
                            dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);

//            for (idy = 0; idy < TrLen; idy++)
//            {
//                if (DelayID + idy < JamSigLen)
//                {
//                    PhaseValue = -4 * PI*RangeNew / Lambda + 2 * PI*(2 * (JamParam.PullOffVel + JamParam.PullOffAcc*SimTime) / Lambda)*idy / Fs;       //每个PRT时刻多普勒
//                    DopPhase.re = cos(PhaseValue);
//                    DopPhase.im = sin(PhaseValue);
//                    DeceptionJamSig[DelayID + idy].re = 1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.re);
//                    DeceptionJamSig[DelayID + idy].im = 1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.im);
//                }
//            }
        }//if (JamParam.PullOffType == 1)

        else if (JamParam.PullOffType == 2)				//速度拖引
        {
            RangeNew = Range + (JamParam.DeltaDop + JamParam.DeltaRate*SimTime)*Lambda / 2 * SimTime;		//每个PRT时刻距离
            DelayID = round((2 * RangeNew / SPEEDLIGHT + SimTime)*Fs);

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
            double phase0 = -2*RangeNew / Lambda;
            double phase1Coef = (JamParam.DeltaDop + JamParam.DeltaRate*SimTime)/ Fs;
			CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
                            dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);
//            for (idy = 0; idy < TrLen; idy++)
//            {
//                if (DelayID + idy < JamSigLen)
//                {
//                    PhaseValue = -4 * PI*RangeNew / Lambda + 2 * PI*(JamParam.DeltaDop + JamParam.DeltaRate*SimTime)*idy / Fs;       //每个PRT时刻多普勒
//                    DopPhase.re = cos(PhaseValue);
//                    DopPhase.im = sin(PhaseValue);
//                    DeceptionJamSig[DelayID + idy].re = 1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.im);
//                    DeceptionJamSig[DelayID + idy].im = 1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.re);
//                }
//            }
        }//else if (JamParam.PullOffType == 2)

        else if (JamParam.PullOffType == 3)				//距离-速度联合拖引
        {
            for (idz = 0; idz < JamParam.MulTarNum; idz++)
            {
                RangeNew = Range + JamParam.MulPullOffRan[idz] + JamParam.MulPullOffVel[idz] * SimTime + 0.5*JamParam.MulPullOffAcc[idz] * SimTime*SimTime
                    + (JamParam.MulDeltaDop[idz] + JamParam.MulDeltaRate[idz] * SimTime)*Lambda / 2 * SimTime;		//每个PRT时刻距离
                DelayID = round((2 * RangeNew / SPEEDLIGHT + SimTime)*Fs);

                dim3 ThreadsPerBlock(512,1);
                dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                double phase0 = -2*RangeNew / Lambda;
                double phase1Coef = (2 * (JamParam.MulPullOffVel[idz] + JamParam.MulPullOffAcc[idz] * SimTime) / Lambda) / Fs
                                    + (JamParam.MulDeltaDop[idz] + JamParam.MulDeltaRate[idz] * SimTime)/ Fs;       //每个PRT时刻多普勒
				CUDA_JAM_PullAdd(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
                                dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);
//                for (idy = 0; idy < TrLen; idy++)
//                {
//                    if (DelayID + idy < JamSigLen)
//                    {
//                        PhaseValue = -4 * PI*RangeNew / Lambda + 2 * PI*(2 * (JamParam.MulPullOffVel[idz] + JamParam.MulPullOffAcc[idz] * SimTime) / Lambda)*idy / Fs
//                            + 2 * PI*(JamParam.MulDeltaDop[idz] + JamParam.MulDeltaRate[idz] * SimTime)*idy / Fs;       //每个PRT时刻多普勒
//                        DopPhase.re = cos(PhaseValue);
//                        DopPhase.im = sin(PhaseValue);
//                        DeceptionJamSig[DelayID + idy].re = DeceptionJamSig[DelayID + idy].re + 1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.im);
//                        DeceptionJamSig[DelayID + idy].im = DeceptionJamSig[DelayID + idy].im + 1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.re);
//                    }
//                }
            }
        }//else if (JamParam.PullOffType == 3)

        else if (JamParam.PullOffType == 4)				//频移干扰
        {
            DelayID = round((2 * Range / SPEEDLIGHT + SimTime)*Fs);

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
            double phase0 = -2*Range / Lambda;
            double phase1Coef = JamParam.FreqShift/ Fs;
			CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
                            dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);
//            for (idy = 0; idy < TrLen; idy++)
//            {
//                if (DelayID + idy < JamSigLen)
//                {
//                    PhaseValue = -4 * PI*Range / Lambda + 2 * PI*JamParam.FreqShift*idy / Fs;       //每个PRT时刻多普勒
//                    DopPhase.re = cos(PhaseValue);
//                    DopPhase.im = sin(PhaseValue);
//                    DeceptionJamSig[DelayID + idy].re = 1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.im);
//                    DeceptionJamSig[DelayID + idy].im = 1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.re);
//                }
//            }
        }//else if (JamParam.PullOffType == 4)

        else if (JamParam.PullOffType == 5)			//密集复制+重复转发干扰
        {
            if (JamParam.CopyType == 1)					//密集复制干扰
            {
                DelayID = round((2 * Range / SPEEDLIGHT + SimTime + JamParam.FrontDelay)*Fs);
                int DelaySpace = round(JamParam.FrontSpace*Fs);       //密集复制信号之间的间隔
                int	FrontLen = round(JamParam.FrontTp*Fs);       //复制信号切片的长度

                dim3 ThreadsPerBlock(512,1);
                dim3 BlockNum((FrontLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);

                PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
                DopPhase.x = cos(PhaseValue);
                DopPhase.y = sin(PhaseValue);
                for (idz = 0; idz < JamParam.FrontCopyTimes; idz++)
                {
					CUDA_JAM_PullAddCopy(0, ThreadsPerBlock, BlockNum, FrontLen, JamSigLen, DelayID + DelaySpace*idz, dev_SourceSignal,
                                         DopPhase.x, DopPhase.y,dev_DeceptionJamSig);
//                    for (idy = 0; idy < FrontLen; idy++)
//                    {
//                        if (DelayID + DelaySpace*idz + idy < JamSigLen)
//                        {
//                            DeceptionJamSig[DelayID + DelaySpace*idz + idy].re = DeceptionJamSig[DelayID + DelaySpace*idz + idy].re +
//                                1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.im);
//                            DeceptionJamSig[DelayID + DelaySpace*idz + idy].im = DeceptionJamSig[DelayID + DelaySpace*idz + idy].im +
//                                1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.re);
//                        }
//                    }
                }
            }//else if (JamParam.CopyType == 1)

            else if (JamParam.CopyType == 2)					//重复转发干扰
            {
                DelayID = round((2 * Range / SPEEDLIGHT + SimTime + JamParam.FrontDelay)*Fs);
                int DelaySpace = round((JamParam.CopySpace + JamParam.FrontTp)*Fs);       //转发信号之间的间隔
                int	FrontLen = round(JamParam.FrontTp*Fs);       //复制信号切片的长度
                PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
                DopPhase.x = cos(PhaseValue);
                DopPhase.y = sin(PhaseValue);

                dim3 ThreadsPerBlock(512,1);
                dim3 BlockNum((FrontLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                for (idz = 0; idz < JamParam.CopyTimes; idz++)
                {
					CUDA_JAM_PullAddCopy(0, ThreadsPerBlock, BlockNum, FrontLen, JamSigLen, DelayID + DelaySpace*idz, dev_SourceSignal,
                                         DopPhase.x, DopPhase.y,dev_DeceptionJamSig);
//                    for (idy = 0; idy < FrontLen; idy++)
//                    {
//                        if (DelayID + DelaySpace*idz + idy < JamSigLen)
//                        {
//                            DeceptionJamSig[DelayID + DelaySpace*idz + idy].re = DeceptionJamSig[DelayID + DelaySpace*idz + idy].re +
//                                1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.im);
//                            DeceptionJamSig[DelayID + DelaySpace*idz + idy].im = DeceptionJamSig[DelayID + DelaySpace*idz + idy].im +
//                                1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.re);
//                        }
//                    }
                }
            }//else if (JamParam.CopyType == 2)

            else if (JamParam.CopyType == 3)					//先密集复制再重复转发干扰
            {
                DelayID = round((2 * Range / SPEEDLIGHT + SimTime + JamParam.FrontDelay)*Fs);
                int DelaySpace = round(JamParam.FrontSpace*Fs);       //复制信号之间的间隔
                int	FrontLen = round(JamParam.FrontTp*Fs);       //复制信号切片的长度
                PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
                DopPhase.x = cos(PhaseValue);
                DopPhase.y = sin(PhaseValue);
                T2 *dev_JamCopyTemp = (T2*)_MallocCUDA(sizeof(T2)*PRTLen);
                cudaMemset(dev_JamCopyTemp, 0, sizeof(T2)*PRTLen);

                dim3 ThreadsPerBlock(512,1);
                dim3 BlockNum((FrontLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                for (idz = 0; idz < JamParam.FrontCopyTimes; idz++)
                {
					CUDA_JAM_PullAddCopy(0, ThreadsPerBlock, BlockNum, FrontLen, PRTLen, DelayID + DelaySpace*idz, dev_SourceSignal,
                                         DopPhase.x, DopPhase.y,dev_DeceptionJamSig);
                    cudaMemcpy(&dev_JamCopyTemp[DelaySpace*idz],&dev_DeceptionJamSig[DelayID + DelaySpace*idz],sizeof(T2)*PRTLen,cudaMemcpyDeviceToDevice);
//                    for (idy = 0; idy < FrontLen; idy++)
//                    {
//                        if (DelaySpace*idz + idy < PRTLen)
//                        {
//                            JamCopyTemp[DelaySpace*idz + idy].re = DeceptionJamSig[DelayID + DelaySpace*idz + idy].re +
//                                1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.im);
//                            JamCopyTemp[ DelaySpace*idz + idy].im = DeceptionJamSig[DelayID + DelaySpace*idz + idy].im +
//                                1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.re);
//                        }
//                    }
                }

                int CopyLen = round((JamParam.FrontTp + JamParam.FrontSpace*(JamParam.FrontCopyTimes - 1))*Fs);       //转发信号切片的长度
                int DelaySpaceCopy = round(JamParam.CopySpace*Fs) + CopyLen;       //转发信号之间的间隔
                BlockNum.x = ((JamSigLen - (DelayID + DelaySpaceCopy*idz )) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x;
                for (idz = 0; idz < JamParam.CopyTimes; idz++)
                {
					CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, JamSigLen - (DelayID + DelaySpaceCopy*idz), dev_JamCopyTemp,
                                    (T2*)&dev_DeceptionJamSig[DelayID + DelaySpaceCopy*idz], (T2*)&dev_DeceptionJamSig[DelayID + DelaySpaceCopy*idz]);
//                    for (idy = 0; idy < CopyLen; idy++)
//                    {
//                        if (DelayID + DelaySpaceCopy*idz + idy < JamSigLen)
//                        {
//                            DeceptionJamSig[DelayID + DelaySpaceCopy*idz + idy].re = DeceptionJamSig[DelayID + DelaySpaceCopy*idz + idy].re + JamCopyTemp[idy].re;
//                            DeceptionJamSig[DelayID + DelaySpaceCopy*idz + idy].im = DeceptionJamSig[DelayID + DelaySpaceCopy*idz + idy].im + JamCopyTemp[idy].im;
//                        }
//                    }
                }
            }//else if (JamParam.CopyType == 3)

            else if (JamParam.CopyType == 4)		//先重复转发再密集复制干扰
            {
                DelayID = round((2 * Range / SPEEDLIGHT + SimTime + JamParam.FrontDelay)*Fs);
                int DelaySpaceCopy = round((JamParam.CopySpace + JamParam.FrontTp)*Fs);       //转发信号之间的间隔
                int	FrontLen = round(JamParam.FrontTp*Fs);       //复制信号切片的长度
                PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
                DopPhase.x = cos(PhaseValue);
                DopPhase.y = sin(PhaseValue);
                T2 *dev_JamCopyTemp = (T2*)_MallocCUDA(sizeof(T2)*PRTLen);
                cudaMemset(dev_JamCopyTemp, 0, sizeof(T2)*PRTLen);

                dim3 ThreadsPerBlock(512,1);
                dim3 BlockNum((FrontLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                for (idz = 0; idz < JamParam.CopyTimes; idz++)
                {
					CUDA_JAM_PullAddCopy(0, ThreadsPerBlock, BlockNum, FrontLen, PRTLen, DelaySpaceCopy*idz, dev_SourceSignal,
                                         DopPhase.x, DopPhase.y,dev_JamCopyTemp);
//                    for (idy = 0; idy < FrontLen; idy++)
//                    {
//                        if (DelaySpaceCopy*idz + idy < PRTLen)
//                        {
//                            JamCopyTemp[DelaySpaceCopy*idz + idy].re = JamCopyTemp[DelaySpaceCopy*idz + idy].re +
//                                1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.im);
//                            JamCopyTemp[DelaySpaceCopy*idz + idy].im = JamCopyTemp[DelaySpaceCopy*idz + idy].im +
//                                1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.re);
//                        }
//                    }
                }

                int DelaySpace = round(JamParam.FrontSpace*Fs);       //复制信号之间的间隔
                int FrontLenNew = DelaySpace*JamParam.FrontCopyTimes;       //密集复制的信号的长度
                BlockNum.x = ((JamSigLen - (DelayID + DelaySpace*idz )) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x;
                for (idz = 0; idz < JamParam.FrontCopyTimes; idz++)
                {
					CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, JamSigLen - (DelayID + DelaySpace*idz), dev_JamCopyTemp,
                                    (T2*)&dev_DeceptionJamSig[DelayID + DelaySpace*idz], (T2*)&dev_DeceptionJamSig[DelayID + DelaySpace*idz]);
//                    for (idy = 0; idy < FrontLenNew; idy++)
//                    {
//                        if (DelayID + DelaySpace*idz + idy < JamSigLen)
//                        {
//                            DeceptionJamSig[DelayID + DelaySpace*idz + idy].re = DeceptionJamSig[DelayID + DelaySpace*idz + idy].re + JamCopyTemp[idy].re;
//                            DeceptionJamSig[DelayID + DelaySpace*idz + idy].im = DeceptionJamSig[DelayID + DelaySpace*idz + idy].im + JamCopyTemp[idy].im;
//                        }
//                    }
                }
            }//else if (JamParam.CopyType == 4)
        }//else if (JamParam.PullOffType == 5)

        else if (JamParam.PullOffType == 6)				//切片干扰
        {
            if (JamParam.CutType == 1)			//只切片
            {
                DelayID = round((2 * Range / SPEEDLIGHT + SimTime + JamParam.CutDelay + JamParam.CutEdge)*Fs);
                int CutPRTTimes = ceil((Tr - JamParam.CutEdge) / JamParam.CutPRT);       //脉冲切片采样次数
                int	CutLen = round(JamParam.CutTp*Fs);       //脉冲切片长度
                int	CutPRTLen = round(JamParam.CutPRT*Fs);       //切片周期长度
                int	CutEdgeLen = round(JamParam.CutEdge*Fs);       //切片前沿起始长度
                PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
                DopPhase.x = cos(PhaseValue);
                DopPhase.y = sin(PhaseValue);


                int Dot1 = JamSigLen - (DelayID + CutEdgeLen + CutPRTLen*idz);
                int Dot2 = TrLen -(CutEdgeLen + CutPRTLen*idz);
                int Dot = Dot1 > Dot2 ? Dot2:Dot1;
                dim3 ThreadsPerBlock(512,1);
                dim3 BlockNum((Dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                for (idz = 0; idz < CutPRTLen; idz++)
                {
					CUDA_JAM_PullAddCut(0, ThreadsPerBlock, BlockNum, Dot, (T*)&dev_SourceSignal[CutEdgeLen + CutPRTLen*idz],
                                         DopPhase.x, DopPhase.y,(T2*)&dev_DeceptionJamSig[DelayID + CutEdgeLen + CutPRTLen*idz]);
//                    for (idy = 0; idy < CutLen; idy++)
//                    {
//                        if (DelayID + CutEdgeLen + CutPRTLen*idz + idy < JamSigLen)
//                        {
//                            if (CutEdgeLen + CutPRTLen*idz + idy < TrLen)
//                            {
//                                DeceptionJamSig[DelayID + CutEdgeLen + CutPRTLen*idz + idy].re = 1.0*(SourceSignal[CutEdgeLen + CutPRTLen*idz + idy].re * DopPhase.re
//                                    - SourceSignal[CutEdgeLen + CutPRTLen*idz + idy].im * DopPhase.im);
//                                DeceptionJamSig[DelayID + CutEdgeLen + CutPRTLen*idz + idy].im = 1.0*(SourceSignal[CutEdgeLen + CutPRTLen*idz + idy].re * DopPhase.im
//                                    + SourceSignal[CutEdgeLen + CutPRTLen*idz + idy].im * DopPhase.re);
//                            }
//                        }
//                    }
                }
            }//if (JamParam.CutType == 1)

            else if (JamParam.CutType == 2)			//先切片再重复转发
            {
                DelayID = round((2 * Range / SPEEDLIGHT + SimTime + JamParam.CutDelay + JamParam.CutEdge)*Fs);
                int CutPRTTimes = ceil((Tr - JamParam.CutEdge) / JamParam.CutPRT);       //脉冲切片采样次数
                int	CutLen = round(JamParam.CutTp*Fs);       //脉冲切片长度
                int	CutPRTLen = round(JamParam.CutPRT*Fs);       //切片周期长度
                int	CutEdgeLen = round(JamParam.CutEdge*Fs);       //切片前沿起始长度
                PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
                DopPhase.x = cos(PhaseValue);
                DopPhase.y = sin(PhaseValue);

                T2 *dev_JamCopyTemp = (T2*)_MallocCUDA(sizeof(T2)*PRTLen);
                cudaMemset(dev_JamCopyTemp, 0, sizeof(T2)*PRTLen);
                int dot = TrLen - (CutEdgeLen + CutPRTLen*idz);

                dim3 ThreadsPerBlock(512,1);
                dim3 BlockNum((dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                for (idz = 0; idz < CutPRTLen; idz++)
                {
					CUDA_JAM_PullAddCut(0, ThreadsPerBlock, BlockNum, dot, (T*)&dev_SourceSignal[CutEdgeLen + CutPRTLen*idz],
                                         DopPhase.x, DopPhase.y,(T2*)&dev_JamCopyTemp[CutEdgeLen + CutPRTLen*idz]);
//                    for (idy = 0; idy < CutLen; idy++)
//                    {
//                        if (CutEdgeLen + CutPRTLen*idz + idy < TrLen)
//                        {
//                            JamCopyTemp[CutEdgeLen + CutPRTLen*idz + idy].re = 1.0*(SourceSignal[CutEdgeLen + CutPRTLen*idz + idy].re * DopPhase.re
//                                - SourceSignal[CutEdgeLen + CutPRTLen*idz + idy].im * DopPhase.im);
//                            JamCopyTemp[CutEdgeLen + CutPRTLen*idz + idy].im = 1.0*(SourceSignal[CutEdgeLen + CutPRTLen*idz + idy].re * DopPhase.im
//                                + SourceSignal[CutEdgeLen + CutPRTLen*idz + idy].im * DopPhase.re);
//                        }
//                    }
                }

                int DelaySpaceCopy = round((JamParam.CopySpace + Tr)*Fs);       //转发信号之间的间隔
                int	CopyLen = TrLen;       //转发信号切片的长度
                BlockNum.x = ((JamSigLen - (DelayID + DelaySpaceCopy*idz )) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x;
                for (idz = 0; idz < JamParam.CopyTimes; idz++)
                {
					CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, JamSigLen - (DelayID + DelaySpaceCopy*idz), dev_JamCopyTemp,
                                    (T2*)&dev_DeceptionJamSig[DelayID + DelaySpaceCopy*idz], (T2*)&dev_DeceptionJamSig[DelayID + DelaySpaceCopy*idz]);
//                    for (idy = 0; idy < CopyLen; idy++)
//                    {
//                        if (DelayID + DelaySpaceCopy*idz + idy < JamSigLen)
//                        {
//                            DeceptionJamSig[DelayID + DelaySpaceCopy*idz + idy].re = DeceptionJamSig[DelayID + DelaySpaceCopy*idz + idy].re + JamCopyTemp[idy].re;
//                            DeceptionJamSig[DelayID + DelaySpaceCopy*idz + idy].im = DeceptionJamSig[DelayID + DelaySpaceCopy*idz + idy].im + JamCopyTemp[idy].im;
//                        }
//                    }
                }
            }//else if (JamParam.CutType == 2)
        }//else if (JamParam.PullOffType == 6)

        else if (JamParam.PullOffType == 7)						// 多普勒闪烁干扰
        {
            DelayID = round((2 * Range / SPEEDLIGHT + SimTime)*Fs);
            int FreqDopJamPos = idx % JamParam.FreqDopNum;
            double DopFreqShift = JamParam.FreqDopJam[FreqDopJamPos];

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
            double phase0 = -2*Range / Lambda;
            double phase1Coef = DopFreqShift/ Fs;
			CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
                            dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);
//            for (idy = 0; idy < TrLen; idy++)
//            {
//                if (DelayID + idy < JamSigLen)
//                {
//                    PhaseValue = -4 * PI*Range / Lambda + 2 * PI*DopFreqShift*idy / Fs;       //每个PRT时刻多普勒
//                    DopPhase.re = cos(PhaseValue);
//                    DopPhase.im = sin(PhaseValue);
//                    DeceptionJamSig[DelayID + idy].re = 1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.im);
//                    DeceptionJamSig[DelayID + idy].im = 1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.re);
//                }
//            }
        }//else if (JamParam.PullOffType == 7)

        else if (JamParam.PullOffType == 8)						// 灵巧噪声干扰
        {
            DelayID = round((2 * Range / SPEEDLIGHT + SimTime)*Fs);       //每个PRT时刻多普勒
            PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
            DopPhase.x = cos(PhaseValue);
            DopPhase.y = sin(PhaseValue);
            T2 *dev_SmartNoisePhase = (T2*)_MallocCUDA(sizeof(T2)*PRTLen);

            T2 *dev_SmartNoise0 = (T2*)_MallocCUDA(sizeof(T2)*PRTLen);
            cudaMemset(dev_SmartNoise0, 0, sizeof(T2)*PRTLen);
            NoiseModelGen(PRTLen, 0, JamParam.SmartNoiseVar, 0, JamParam.SmartNoiseBr, 1, Fs, dev_SmartNoise0);
            int dot = JamSigLen - DelayID;
            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
			CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_SourceSignal, dev_SmartNoise0, dev_SmartNoisePhase, 1);

			CUDA_JAM_PullAddCut(0, ThreadsPerBlock, BlockNum, dot, dev_SmartNoisePhase, DopPhase.x, DopPhase.y, (T2*)&dev_DeceptionJamSig[DelayID]);

//            for (idy = 0; idy < PRTLen; idy++)
//            {
//                if (DelayID + idy < JamSigLen)
//                {
//                    SmartNoisePhase.re = SmartNoise0[idy][0] * SourceSignal[idy].re - SmartNoise0[idy][1] * SourceSignal[idy].im;
//                    SmartNoisePhase.im = SmartNoise0[idy][0] * SourceSignal[idy].im + SmartNoise0[idy][1] * SourceSignal[idy].re;

//                    DeceptionJamSig[DelayID + idy].re = 1.0*(SmartNoisePhase.re * DopPhase.re - SmartNoisePhase.im * DopPhase.im);
//                    DeceptionJamSig[DelayID + idy].im = 1.0*(SmartNoisePhase.re * DopPhase.im + SmartNoisePhase.im * DopPhase.re);
//                }
//            }
        }//else if (JamParam.PullOffType == 8)

        else if (JamParam.PullOffType == 9)						// 随机假目标干扰
        {
            for (idz = 0; idz < JamParam.RandMulTargetNum; idz++)
            {
                RangeNew = Range + JamParam.RandRangeSeq[idz];        //每个PRT时刻距离
                DelayID = round((2 * RangeNew / SPEEDLIGHT + SimTime)*Fs);       //每个PRT时刻多普勒

                dim3 ThreadsPerBlock(512,1);
                dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                double phase0 = -2*RangeNew / Lambda;
                double phase1Coef = JamParam.RandDopplerSeq[idz]/ Fs;
				CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
                                dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);
//                for (idy = 0; idy < TrLen; idy++)
//                {
//                    if (DelayID + idy < JamSigLen)
//                    {
//                        PhaseValue = -4 * PI*RangeNew / Lambda + 2 * PI*JamParam.RandDopplerSeq[idz] * idy / Fs;       //每个PRT时刻多普勒
//                        DopPhase.re = cos(PhaseValue);
//                        DopPhase.im = sin(PhaseValue);
//                        DeceptionJamSig[DelayID + idy].re = 1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.im);
//                        DeceptionJamSig[DelayID + idy].im = 1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.re);
//                    }
//                }
            }
        }//else if (JamParam.PullOffType == 9)

        else if (JamParam.PullOffType == 10)						// 多普勒噪声干扰
        {
            DelayID = round((2 * Range / SPEEDLIGHT + SimTime)*Fs);       //每个PRT时刻多普勒
            PhaseValue = -4 * PI*Range / Lambda + 2 * PI*JamParam.Kpm*sqrt(JamParam.PhaVar)*double(rand() / RAND_MAX);       //每个PRT时刻多普勒
            DopPhase.x = cos(PhaseValue);
            DopPhase.y = sin(PhaseValue);

            int dot = JamSigLen - DelayID;
            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
			CUDA_JAM_PullAddCut(0, ThreadsPerBlock, BlockNum, dot, dev_SourceSignal, DopPhase.x, DopPhase.y, (T2*)&dev_DeceptionJamSig[DelayID]);
//            for (idy = 0; idy < TrLen; idy++)
//            {
//                if (DelayID + idy < JamSigLen)
//                {
//                    DeceptionJamSig[DelayID + idy].re = 1.0*(SourceSignal[idy].re * DopPhase.re - SourceSignal[idy].im * DopPhase.im);
//                    DeceptionJamSig[DelayID + idy].im = 1.0*(SourceSignal[idy].re * DopPhase.im + SourceSignal[idy].im * DopPhase.re);
//                }
//            }
        }//else if (JamParam.PullOffType == 10)
    }
}
//******************计算有源干扰回波**********//
//参数说明：
//JamData：干扰信号，和通道
//AziJamData：方位差干扰信号
//EleJamData：俯仰差干扰信号
//ParamR：雷达发射信号相关信息
//Jammer：干扰信号相关信息
//TransLoss:计算出的通道传送损耗
//PRTTime：方位向时间，用于处理间断噪声干扰
//Range：站目距离
//AziGain：方位差通道加权
//EleGain：俯仰差通道加权
//AziEleFalg：单波束/和差差波束标志
//EchoLen：回波流信号总长度
void ThreadClassGenJAM::ActiveJamming(Complexf *JamData, Complexf *BaseSignal, RADAR *ParamR, JAM_PARAM Jammer, SimStruct *SimData,
                                      double PRTTime, double *Range, double *SweepFCur, int EchoLen, int PRFID)
{
    double Lambda = C/ParamR->Fc;	//干扰信号波长
	double DopPhase;
	double KrTime;
    int PRFNum = (int)(ParamR->Fs / ParamR->Prf);	        //一个PRF的点数(流信号)若为帧格式，则会传入Nr
	int idx,idy,idz = 0;
    //******************压制式干扰*******************//
    if (1 == Jammer.JamType)
    {
        BlanketJamGen(Jammer, ParamR->Fs, EchoLen, PRFID, PRFNum, SweepFCur, JamData);
    }// End of: if(1 == Jammer.JamType)	压制式干扰
    //***************End of:压制式干扰*******************//


    //*******************欺骗干扰************************//
    else if (2 == Jammer.JamType)
    {
        DeceptionJamGen(Jammer, ParamR->Fs, ParamR->Tp, EchoLen, PRFID, PRFNum, PRTTime, Range[0], Lambda, BaseSignal, JamData);
    }// End of: if(2 == Jammer.JamType)	欺骗干扰
    //***************End of:欺骗干扰*******************//
           
}
template <class T>
void _CufftExecC2C(cufftHandle plan, T *idata, T *odata, int direction)
{
	if (sizeof(T) > 8){
		cufftExecZ2Z(plan, (cuDoubleComplex*)idata, (cuDoubleComplex*)odata, direction);
	}
	else{
		cufftExecC2C(plan, (cufftComplex*)idata, (cufftComplex*)odata, direction);
	}
}
template <class T,class T2,class T3,class T4>
void ThreadClassGenJAM::GenChaffJam(SimStruct *SimData,JAM_PARAM *Jammer,RADAR *ParamR,T *RadarAPC,T2 *Matrix,
                                    ANTPARAM *AntParam,T *PRI, T3 *dev_BaseSignal,
                                    T4 *dev_SumJamDataPolar1, T4 *dev_AziJamDataPolar1, T4 *dev_EleJamDataPolar1, T4 *dev_ExJamDataPolar1)
{
    int RangeGateMode = 0;
    //**********发射信号生成***********//
    int TrNum  = (int)(ParamR->Fs*ParamR->Tp+0.5);		//一个脉宽的点数
    
    int Nr = ParamR->Nr;   //ParamR->Nr
	int Na = SimData->SaveFlie_Na;   //ParamR->Na
	int SaveNr = SimData->SaveFlie_Nr;
	double ChaffAmp;
	double Range;
	double TransLoss;
	int StartID,EndID;
	int ChaffNum = Jammer->ChaffNum;
	dim3 ThreadsPerBlock(512,1);
	dim3 BlockNum((ChaffNum + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
	dim3 BlockNum1((Nr + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
	double *dev_ChaffPos_ALL;//各箔条位置
	double Radar2Rcs[3];	//北天东坐标系下的弹目矢量
	double Radar2Target[3];	//波束指向坐标系下的弹目矢量
	double Ps_i[3];

    T3 *dev_Signal = (T3*)_MallocCUDA(Nr * sizeof(T3));
    cudaMemset(dev_Signal, 0, Nr*sizeof(T3));
    cudaMemcpy(dev_Signal, dev_BaseSignal, sizeof(T3)*TrNum, cudaMemcpyDeviceToDevice);

	Ps_i[0]  = RadarAPC[3*(ParamR->Na/2) + 0];
	Ps_i[1]  = RadarAPC[3*(ParamR->Na/2) + 1];
	Ps_i[2]  = RadarAPC[3*(ParamR->Na/2) + 2];
	Radar2Rcs[0] = Jammer->ChaffPos[0] - Ps_i[0];	//北
	Radar2Rcs[1] = Jammer->ChaffPos[1] - Ps_i[1];	//天
	Radar2Rcs[2] = Jammer->ChaffPos[2] - Ps_i[2];	//东
	Range = sqrt(Radar2Rcs[0]*Radar2Rcs[0] + Radar2Rcs[1]*Radar2Rcs[1] + Radar2Rcs[2]*Radar2Rcs[2]);

	Radar2Target[0] = Matrix[0]*Radar2Rcs[0] + Matrix[3]*Radar2Rcs[1] + Matrix[6]*Radar2Rcs[2];
	Radar2Target[1] = Matrix[1]*Radar2Rcs[0] + Matrix[4]*Radar2Rcs[1] + Matrix[7]*Radar2Rcs[2];
	Radar2Target[2] = Matrix[2]*Radar2Rcs[0] + Matrix[5]*Radar2Rcs[1] + Matrix[8]*Radar2Rcs[2];

	//---------------------天线加权计算------------------------
	float  AntFactor,AziAntFactor,EleAntFactor;		//箔条天线加权（统一用质心的）
	double AziAngle    = atan(Radar2Target[2]/Radar2Target[0]);		// 方位角
	double PitchAngle  = asin(Radar2Target[1]/Range);					// 俯仰角

	if(Radar2Target[0] <= 0)	//背瓣
	{
		AziAngle = PI + AziAngle;
		PitchAngle = -PI - PitchAngle;
	}
	double DeltaAzi		= AntParam->AntAziBound/AntParam->AntFuncAziNum;
	double DeltaPitch	= AntParam->AntEleBound/AntParam->AntFuncEleNum;
	int AziID			= (int)(AziAngle/DeltaAzi	  + AntParam->AntFuncAziNum/2.0 );
	int PitchID			= (int)(PitchAngle/DeltaPitch + AntParam->AntFuncEleNum/2.0);
	int AziBeamLen		= AntParam->AntFuncAziNum;
	int PitchBeamLen	= AntParam->AntFuncEleNum;
	int AziOverlapLen	= (int)(AntParam->AziOverlap/DeltaAzi/2 + 0.5);		//方位向半重叠波束长度
	int EleOverlapLen	= (int)(AntParam->EleOverlap/DeltaPitch/2 + 0.5);		//俯仰向半重叠波束长度
	int AziStartID,AziEndID,EleStartID,EleEndID;
	AziStartID		= int(-AziOverlapLen + AziBeamLen/2.0);
	AziEndID		= int(AziOverlapLen  + AziBeamLen/2.0);
	EleStartID		= int(-EleOverlapLen + PitchBeamLen/2.0);
	EleEndID		= int(EleOverlapLen  + PitchBeamLen/2.0);
	int AntMod	 = SimData->AziEleFalg;     
    if(0 == AntMod)			//单波束
    {
        if(AziID>=0 && AziID<AziBeamLen && PitchID>=0 && PitchID<PitchBeamLen)
            AntFactor = (float)AntParam->SumAntennaFunction[PitchID + (int)AntParam->AntFuncEleNum*AziID];
        else
            AntFactor = (float)(AntParam->SumAntennaFunction[0]);
    }
    else if(1 == AntMod)	//和差差(比相)
    {
        if(AziID>=0 && AziID<AziBeamLen && PitchID>=0 && PitchID<PitchBeamLen)
        {
            AntFactor	 = (float)AntParam->SumAntennaFunction[PitchID + (int)AntParam->AntFuncEleNum*AziID];
            AziAntFactor = (float)(2.0*PI*ParamR->Fc / SPEEDLIGHT*AntParam->AziSpace*sin(AziAngle));
            EleAntFactor = (float)(2.0*PI*ParamR->Fc / SPEEDLIGHT*AntParam->EleSpace*sin(PitchAngle));
        }
        else
        {
            AntFactor = (float)(AntParam->SumAntennaFunction[0]);
            AziAntFactor = (float)(2.0*PI*ParamR->Fc / SPEEDLIGHT*AntParam->AziSpace*sin(AziAngle));
            EleAntFactor = (float)(2.0*PI*ParamR->Fc / SPEEDLIGHT*AntParam->EleSpace*sin(PitchAngle));
        }
    }
    else if(2 == AntMod)	//和差差(比幅)
    {
        if (AziID >= 0 && AziID < AziBeamLen && PitchID >= 0 && PitchID < PitchBeamLen)
        {
            AntFactor = (float)AntParam->SumAntennaFunction[PitchID + (int)AntParam->AntFuncEleNum*AziID];
            AziAntFactor = (float)AntParam->AziSubAntennaFunction[PitchID + (int)AntParam->AntFuncEleNum*AziID];
            EleAntFactor = (float)AntParam->PitSubAntennaFunction[PitchID + (int)AntParam->AntFuncEleNum*AziID];
        }
        else
        {
            AntFactor = (float)(AntParam->SumAntennaFunction[0]);
            AziAntFactor = (float)(AntParam->AziSubAntennaFunction[0]);
            EleAntFactor = (float)(AntParam->PitSubAntennaFunction[0]);
        }
    }
    
    double *dev_param;//GPU计算时需要的双精度参数，显存
    int *dev_elseparam;//GPU计算时需要的双精度参数，显存
    dev_param = (double*)_MallocCUDA(15*sizeof(double));
    dev_elseparam = (int*)_MallocCUDA(10*sizeof(int));

    double param[15];
    param[0] = SimData->Rmin;
	param[1] = 2.0*ParamR->Fs / C;	//2.0*fs/C
	param[2] = -2.0 / (C / ParamR->Fc);		//C/Fc
	param[3] = (C / ParamR->Fc)*(C / ParamR->Fc);//lambda^2
	param[4] = 1.0/(64 * PI*PI*PI);
    param[5] = Jammer->AveRcs;				//箔条平均RCS
    param[6] = Jammer->DiffuseRan;
    param[7] = Jammer->ChaffPos[0];
    param[8] = Jammer->ChaffPos[1];
    param[9] = Jammer->ChaffPos[2];
    param[10] = ParamR->Amp;
    //param[11] = AziAntFactor;
    //param[12] = EleAntFactor;
    param[13] = ParamR->RadomeLoss;			//天线罩损耗
    cudaMemcpy(dev_param,param,15*sizeof(double),cudaMemcpyHostToDevice);
    
    int else_param[10];
    else_param[0] = Jammer->ChaffNum;
    else_param[1] = ParamR->Nr;
    else_param[2] = AziBeamLen;
    else_param[3] = PitchBeamLen;
    else_param[4] = SimData->AziEleFalg;
    else_param[5] = SimData->TransLossFlag;
    
    int RangeGateNum;
    int LastPos;        //可变距离门最后ID
    int GateSampleNum;
    if(RangeGateMode == 1)//可变距离门
    {
        //计算可变距离门个数
        RangeGateNum = int(SimData->RangeGateRegion/SimData->RangeGateWidth + 1);
        if(fabs(SimData->RangeGateRegion/SimData->RangeGateWidth - int(SimData->RangeGateRegion/SimData->RangeGateWidth)) < MINEQUERR)
            RangeGateNum = int(SimData->RangeGateRegion/SimData->RangeGateWidth + MINEQUERR);  //当两个double型数刚好可以整除时
        //else_param[6] = RangeGateNum;
        LastPos = int(SimData->RangeGateRegion*ParamR->Fs + 0.5);
        //else_param[7] = LastPos;
        //else_param[8] = SimData->SarchNum;
        GateSampleNum = int(SimData->RangeGateWidth*ParamR->Fs + 0.5);
        //else_param[9] = GateSampleNum;
    }
    
    cudaMemcpy(dev_elseparam,else_param,10*sizeof(double),cudaMemcpyHostToDevice);
    dev_ChaffPos_ALL = (double*)_MallocCUDA(sizeof(double)*ChaffNum*3);
                
    double stddev = Jammer->DiffuseRan;

    curandSetPseudoRandomGeneratorSeed(gen_curand,(time(NULL) + rand()));
    curandGenerateNormalDouble(gen_curand, dev_ChaffPos_ALL, (size_t)(ChaffNum * 3), 0.0, stddev);  //在DiffuseRan范围内随机生成箔条坐标
    double *dev_Ps;
    dev_Ps = (double*)_MallocCUDA(sizeof(double)*3);
    cudaMemset(dev_Ps,0,sizeof(double)*3);
    cudaMemcpy(dev_Ps,Ps_i,sizeof(double)*3,cudaMemcpyHostToDevice);

	T3 *echo_all = (T3*)_Malloc(SaveNr*Na*sizeof(T3));			//产生总的回波，内存
	memset(echo_all, 0, sizeof(T3)*SaveNr*Na);
	T3 *aziecho_all = (T3*)_Malloc(SaveNr*Na*sizeof(T3));		//产生总的方位向回波，内存
	memset(aziecho_all, 0, sizeof(T3)*SaveNr*Na);
	T3 *eleecho_all = (T3*)_Malloc(SaveNr*Na*sizeof(T3));		//产生总的俯仰向回波，内存
	memset(eleecho_all, 0, sizeof(T3)*SaveNr*Na);

	T3 *hsys = (T3*)_Malloc(Nr*sizeof(T3));					//每一帧回波缓存，内存
	memset(hsys, 0, sizeof(T3)*Nr);
	T3 *dev_hsys, *dev_Azihsys, *dev_Elehsys;				//产生系统响应函数，显存
    dev_hsys    = (T3*)_MallocCUDA(Nr*sizeof(T3));	
    dev_Azihsys = (T3*)_MallocCUDA(Nr*sizeof(T3));
    dev_Elehsys = (T3*)_MallocCUDA(Nr*sizeof(T3));
	cudaMemset(dev_hsys, 0, sizeof(T3)*Nr);
	cudaMemset(dev_Azihsys, 0, sizeof(T3)*Nr);
	cudaMemset(dev_Elehsys, 0, sizeof(T3)*Nr);
                
    int i;
    double PRTTime = 0;	//当前时刻
	T3 *dev_Echo;
	dev_Echo = (T3*)_MallocCUDA(Nr*sizeof(T3));
	cudaMemset(dev_Echo, 0, sizeof(T3)*Nr);
    T3 *dev_EchoFinal       = (T3*)_MallocCUDA(sizeof(T3)*SaveNr*Na);
    T3 *dev_EchoAzieFinal   = (T3*)_MallocCUDA(sizeof(T3)*SaveNr*Na);
    T3 *dev_EchoEleFinal    = (T3*)_MallocCUDA(sizeof(T3)*SaveNr*Na);
    
	cudaEvent_t e_start, e_stop;
	cudaEventCreate(&e_start);
	cudaEventCreate(&e_stop);
	cudaEventRecord(e_start, 0);

    cufftHandle cufftPlan = CufftPlanCheck(Nr);
    _CufftExecC2C(cufftPlan, dev_Signal, dev_Signal, CUFFT_FORWARD);
    int idm;		//距离门计数
    int GateStart = 0;	//不同距离门起始ID 
    for(i = 0;i<Na;i++)
    {
        Ps_i[0]  = RadarAPC[3*i + 0];
        Ps_i[1]  = RadarAPC[3*i + 1];
        Ps_i[2]  = RadarAPC[3*i + 2];
        cudaMemcpy(dev_Ps,Ps_i,sizeof(double)*3,cudaMemcpyHostToDevice);
        param[7] = Jammer->ChaffPos[0] + PRTTime*Jammer->ChaffVel[0];
        param[8] = Jammer->ChaffPos[1] + PRTTime*Jammer->ChaffVel[1];
        param[9] = Jammer->ChaffPos[2] + PRTTime*Jammer->ChaffVel[2];
        PRTTime = PRTTime + PRI[i];
        cudaMemcpy(dev_param,param,15*sizeof(double),cudaMemcpyHostToDevice);
        if(RangeGateMode == 1)//可变距离门
        {
            if(i == 0)
                idm = 1;
            else if(i != 0 && i%SimData->SarchNum == 0 && idm < RangeGateNum )
                idm = idm + 1;		//下一个距离门
            else if(i != 0 && i%SimData->SarchNum == 0 && idm == RangeGateNum)
                idm = 1;
            //else_param[10] = idm;
            if(idm<RangeGateNum)
                GateStart = (idm-1)*GateSampleNum;
            if(idm==RangeGateNum)
                GateStart = LastPos - GateSampleNum;	
        }

		cudaMemset(dev_hsys, 0, sizeof(T3)*Nr);
		cudaMemset(dev_Azihsys, 0, sizeof(T3)*Nr);
		cudaMemset(dev_Elehsys, 0, sizeof(T3)*Nr);

		CUDA_cal_hsys_all_kernel_Chaff(0, ThreadsPerBlock, BlockNum, dev_Ps, dev_ChaffPos_ALL, dev_ChaffPos_ALL + ChaffNum, dev_ChaffPos_ALL + 2 * ChaffNum, dev_param, dev_elseparam,
			AntFactor, AziAntFactor, EleAntFactor, dev_CosValue, dev_SinValue, dev_hsys, dev_Azihsys, dev_Elehsys);
        
        cufftHandle cufftPlan = CufftPlanCheck(Nr);
        _CufftExecC2C(cufftPlan, (T3*)dev_hsys, (T3*)dev_hsys, CUFFT_FORWARD);
		CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, (T3*)dev_Signal, (T3*)dev_hsys, (T3*)dev_Echo, Nr);
        _CufftExecC2C(cufftPlan, dev_Echo, &dev_EchoFinal[i*SaveNr], CUFFT_INVERSE);

//		cudaMemcpy(hsys, dev_Echo, sizeof(T3)*Nr, cudaMemcpyDeviceToHost);
//		memcpy(&echo_all[i*SaveNr], hsys + GateStart, sizeof(T3)*SaveNr);
        if(1 == SimData->AziEleFalg)
        {
            _CufftExecC2C(cufftPlan, (T3*)dev_Azihsys, (T3*)dev_Azihsys, CUFFT_FORWARD);
			CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, (T3*)dev_Signal, (T3*)dev_Azihsys, (T3*)dev_Echo, Nr);
            _CufftExecC2C(cufftPlan, dev_Echo, &dev_EchoAzieFinal[i*SaveNr], CUFFT_INVERSE);
//			cudaMemcpy(hsys, dev_Echo, sizeof(T3)*Nr, cudaMemcpyDeviceToHost);
//			memcpy(&aziecho_all[i*SaveNr], hsys + GateStart, sizeof(T3)*SaveNr);
            
            _CufftExecC2C(cufftPlan, (T3*)dev_Elehsys, (T3*)dev_Elehsys, CUFFT_FORWARD);
			CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, (T3*)dev_Signal, (T3*)dev_Elehsys, (T3*)dev_Echo, Nr);
            _CufftExecC2C(cufftPlan, dev_Echo, &dev_EchoEleFinal[i*SaveNr], CUFFT_INVERSE);
//			cudaMemcpy(hsys, dev_Echo, sizeof(T3)*Nr, cudaMemcpyDeviceToHost);
//			memcpy(&eleecho_all[i*SaveNr], hsys + GateStart, sizeof(T3)*SaveNr);
        }
        else if(2 == SimData->AziEleFalg)
        {
            _CufftExecC2C(cufftPlan, (T3*)dev_Azihsys, (T3*)dev_Azihsys, CUFFT_FORWARD);
			CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, (T3*)dev_Signal, (T3*)dev_Azihsys, (T3*)dev_Echo, Nr);
            _CufftExecC2C(cufftPlan, dev_Echo, &dev_EchoAzieFinal[i*SaveNr], CUFFT_INVERSE);
//			cudaMemcpy(hsys, dev_Echo, sizeof(T3)*Nr, cudaMemcpyDeviceToHost);
//			memcpy(&aziecho_all[i*SaveNr], hsys + GateStart, sizeof(T3)*SaveNr);

            _CufftExecC2C(cufftPlan, (T3*)dev_Elehsys, (T3*)dev_Elehsys, CUFFT_FORWARD);
			CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, (T3*)dev_Signal, (T3*)dev_Elehsys, (T3*)dev_Echo, Nr);
            _CufftExecC2C(cufftPlan, dev_Echo, &dev_EchoEleFinal[i*SaveNr], CUFFT_INVERSE);
//			cudaMemcpy(hsys, dev_Echo, sizeof(T3)*Nr, cudaMemcpyDeviceToHost);
//			memcpy(&eleecho_all[i*SaveNr], hsys + GateStart, sizeof(T3)*SaveNr);
        }
    }

	cudaEventRecord(e_stop, 0);
	cudaEventSynchronize(e_stop);
	float elapsedTime = 0, elapsedTime2 = 0;
	cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
	qDebug() << "============  ThreadClassGenJAM Cost Time is  " << (elapsedTime) << " ms";

	//char *pppp = (char*)_Malloc(Nr*sizeof(T3));
	//QFile *pQFile = new QFile;
	//pQFile->setFileName("E:\\YN\\yn_0521\\PR2301_063\\5_from_yening\\tte.bin");
	//pQFile->open(QFile::WriteOnly);
	//cudaMemcpy(pppp, dev_Echo, Nr*sizeof(T3), cudaMemcpyDeviceToHost);
	//pQFile->write((char*)pppp, Nr*sizeof(T3));
	//pQFile->close();
	//delete pQFile;

    //信号输出
    ThreadsPerBlock.x   = 512;
    ThreadsPerBlock.y   = 1;
    BlockNum.x          = (SaveNr*Na + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x;
    BlockNum.y          = 1;
    CUDA_ComplexAdd(0,ThreadsPerBlock, BlockNum, SaveNr*Na, dev_EchoFinal, dev_SumJamDataPolar1, dev_SumJamDataPolar1);
    CUDA_ComplexAdd(0,ThreadsPerBlock, BlockNum, SaveNr*Na, dev_EchoFinal, dev_ExJamDataPolar1, dev_ExJamDataPolar1);
//	for(int idx = 0;idx<Na;idx++)		//可以合并，此部分感觉没有用，该CUDA中Na与Nr都是与流信号相对应
//	{
//		for(int idy = 0;idy<SaveNr;idy++)
//		{
//			SumJamDataPolar1[idx*SaveNr + idy].x += echo_all[idx*SaveNr + idy].x;
//			SumJamDataPolar1[idx*SaveNr + idy].y += echo_all[idx*SaveNr + idy].y;
//			SumJamDataPolar2[idx*SaveNr + idy].x += echo_all[idx*SaveNr + idy].x;
//			SumJamDataPolar2[idx*SaveNr + idy].y += echo_all[idx*SaveNr + idy].y;
//			ExJamDataPolar1[idx*SaveNr + idy].x += echo_all[idx*SaveNr + idy].x;
//			ExJamDataPolar1[idx*SaveNr + idy].y += echo_all[idx*SaveNr + idy].y;
//			ExJamDataPolar2[idx*SaveNr + idy].x += echo_all[idx*SaveNr + idy].x;
//			ExJamDataPolar2[idx*SaveNr + idy].y += echo_all[idx*SaveNr + idy].y;
//		}
//	}
	if(1 == SimData->AziEleFalg || 2 == SimData->AziEleFalg)
	{
        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum((SaveNr*Na + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        CUDA_ComplexAdd(0,ThreadsPerBlock, BlockNum, SaveNr*Na, dev_EchoAzieFinal, dev_AziJamDataPolar1, dev_AziJamDataPolar1);
        CUDA_ComplexAdd(0,ThreadsPerBlock, BlockNum, SaveNr*Na, dev_EchoEleFinal, dev_EleJamDataPolar1, dev_EleJamDataPolar1);
//		for(int idx = 0;idx<Na;idx++)
//		{
//			for(int idy = 0;idy<SaveNr;idy++)
//			{
//				AziJamDataPolar1[idx*SaveNr + idy].x += aziecho_all[idx*SaveNr + idy].x;
//				AziJamDataPolar1[idx*SaveNr + idy].y += aziecho_all[idx*SaveNr + idy].y;
//				AziJamDataPolar2[idx*SaveNr + idy].x += aziecho_all[idx*SaveNr + idy].x;
//				AziJamDataPolar2[idx*SaveNr + idy].y += aziecho_all[idx*SaveNr + idy].y;
//				EleJamDataPolar1[idx*SaveNr + idy].x += eleecho_all[idx*SaveNr + idy].x;
//				EleJamDataPolar1[idx*SaveNr + idy].y += eleecho_all[idx*SaveNr + idy].y;
//				EleJamDataPolar2[idx*SaveNr + idy].x += eleecho_all[idx*SaveNr + idy].x;
//				EleJamDataPolar2[idx*SaveNr + idy].y += eleecho_all[idx*SaveNr + idy].y;
//			}
//		}
	}
    
}
//************干扰信号生成************//
//功能：干扰信号生成主程序，可生成有源，无源干扰
//参数说明：
//Target[TARGET_NUM]：   干扰机平台位置速度加速度信息         
//Jammer[TARGET_NUM]：   干扰大类相关参数，包括有源，无源
//ParamR：               雷达发射信号相关参数
//AntParam：             相控阵信息，包括阵元与子阵以及天线加权
//SimData：              仿真结构体,用于主控和波束类型判断
//RadarAPC：             雷达平台坐标，用于计算站目距离(修改，修改成PLATFORM *RadarPos)
//JamData：              干扰信号数据，和通道
//AziJamData：           方位差干扰信号
template <class T,class T2>
void ThreadClassGenJAM::GenJAM(PLATFORM *Target[TARGET_NUM], JAM_PARAM Jammer[TARGET_NUM], RADAR *ParamR, ANTPARAM *AntParam, SimStruct *SimData, PLATFORM *RadarPos, T *BaseSignal,
								T2 *SumJamDataPolar1, T2 *AziJamDataPolar1, T2 *EleJamDataPolar1, T2 *ExJamDataPolar1)
{
    T2 *dev_SumJamDataPolar1 = (T2*)_MallocCUDA(sizeof(T2)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    T2 *dev_ExJamDataPolar1 = (T2*)_MallocCUDA(sizeof(T2)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    T2 *dev_AziJamDataPolar1 = (T2*)_MallocCUDA(sizeof(T2)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    T2 *dev_EleJamDataPolar1 = (T2*)_MallocCUDA(sizeof(T2)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);

    T *dev_BaseSignal = (T*)_MallocCUDA(sizeof(T)*ParamR->Nr);
    cudaMemcpy(dev_BaseSignal,BaseSignal,sizeof(T)*ParamR->Nr,cudaMemcpyHostToDevice);

    int idx,idy,idz,idn;
	double *APC[TARGET_NUM];
	double *PRI[TARGET_NUM];
	double *RadarAPC[TARGET_NUM];							//用于存放每一个干扰所对应的雷达位置
	int SaveFlieLen =  SimData->SaveFlie_Na*SimData->SaveFlie_Nr;		//帧信号信号长度
	int SaveFlowFlieLen = ParamR->Na*ParamR->Nr;						//流信号信号长度
	double PRTTime;
	int Na = 0;
	double PRF;
	//**********PRI序列和APC序列生成***********//
	for(idx =0;idx < SimData->JamNum;idx++)    
	{
		if(1 == Jammer[idx].JamType && PulseJam == Jammer[idx].BalJamType)	//间断噪声干扰（同步+杂乱）
		{
			Na = (int)(ParamR->Na/ParamR->Prf*Jammer[idx].PRF);				// 换算成间断噪声干扰模式下等时间内的Na
			//Na = (int)(ParamR->Na*ParamR->Prf/Jammer[idx].PRF);
			PRF = Jammer[idx].PRF;
			PRI[idx] = (double*)_Malloc(sizeof(double)*Na);			
			APC[idx] = (double*)_Malloc(sizeof(double)*Na*3);		//干扰机APC
			RadarAPC[idx] = (double*)_Malloc(sizeof(double)*Na*3);	//雷达APC
			if( RanPulse == Jammer[idx].PulJamType)	//杂乱脉冲
			{
				for(idy= 0;idy < Na;idy = idy++)
				{
					*(PRI[idx]+idy) = 1/PRF + 0.2/PRF*((double)rand()/RAND_MAX - 0.5)*2;
				}
			}
			else if( SynPulse == Jammer[idx].PulJamType)	//同步脉冲
			{
				for(idy= 0;idy < Na;idy = idy++)
				{
					*(PRI[idx]+idy) = 1/PRF;
				}
			}
			
			PRIGenAPC(Na,PRI[idx],Target[idx],AntParam,APC[idx]);		//根据PRI生成各时刻干扰机位置
			PRIGenAPC(Na,PRI[idx],RadarPos,AntParam,RadarAPC[idx]);		//根据PRI生成各时刻雷达位置
		}
		else if(0 == Jammer[idx].JamType)			//无源干扰
		{
			APC[idx] = (double*)_Malloc(sizeof(double)*ParamR->Na*3);
			PRI[idx] = (double*)_Malloc(sizeof(double)*ParamR->Na);
			RadarAPC[idx] = (double*)_Malloc(sizeof(double)*ParamR->Na*3);
			memset(APC[idx],0,sizeof(double)*ParamR->Na*3);
			GenPRI(ParamR,PRI[idx]);  
			PRIGenAPC(ParamR->Na,PRI[idx],RadarPos,AntParam,RadarAPC[idx]);
			PRIGenAPC(ParamR->Na,PRI[idx],Target[idx],AntParam,APC[idx]);
		}
		else
		{
			APC[idx] = (double*)_Malloc(sizeof(double)*ParamR->Na*3);
			PRI[idx] = (double*)_Malloc(sizeof(double)*ParamR->Na);
			RadarAPC[idx] = (double*)_Malloc(sizeof(double)*ParamR->Na*3);
			memset(APC[idx],0,sizeof(double)*ParamR->Na*3);
			GenAPC(ParamR->Na,ParamR->Prf,Target[idx],APC[idx]);		//根据平台初始时刻位置速度生成各时刻干扰机位置
			GenAPC(ParamR->Na,ParamR->Prf,RadarPos,RadarAPC[idx]);		//根据平台初始时刻位置速度生成各时刻雷达位置
			for(idy= 0;idy < ParamR->Na;idy = idy++)					// 除开间断噪声外干扰信号的PRI序列生成（等PRF）
			{
				*(PRI[idx]+idy) = 1/ParamR->Prf;
			}
		}
	}
	//**********End of: PRI序列和APC序列生成***********//
    
    ThreeDimension TargetPos;	//目标位置
    double TranGain,AziTranGain, PitTranGain;			//传送功率损耗
	double DopPhase = 0;		//-4.0*PI*R/Lambda;
	int DelayID		= 0;		//延时
	int MinID		= 0;		//子阵内延时的起始ID
	int MaxID		= 0;		//子阵内延时的终止ID
	double AntGain[1];			//天线加权
	double AziGain[1];          //方位差通道天线加权
	double EleGain[1];          //俯仰差通道天线加权
	double Range[1];			//弹目距    
	EleGain[0] = 0; AziGain[0] = 0; AntGain[0] = 0; Range[0] = 0;
	
	int PRTNum = (int)(ParamR->Fs/ParamR->Prf+0.5f);	    //一个prf的点数
	int TrNum  = (int)(ParamR->Fs*ParamR->Tp+0.5f);		//一个脉宽的点数

	double Param[11];     // 用于CalAntWeight
	Param[0] = AntParam->AntAziBound/AntParam->AntFuncAziNum;
	Param[1] = AntParam->AntEleBound/AntParam->AntFuncEleNum;
	Param[2] = AntParam->AntFuncAziNum;
	Param[3] = AntParam->AntFuncEleNum;
	//if(ArryElement == AntParam->SubAntType)
	//{
	//	Param[4] = (AntParam->ArrayNumRow   -1)/2.0*(C/ParamR->Fc/2);	//差波束阵元间距-行
	//	Param[5] = (AntParam->ArrayNumColumn-1)/2.0*(C/ParamR->Fc/2);	//差波束阵元间距-列
	//}
	//else
	//{
	//	Param[4] = (AntParam->SubArrayNumRow - 1)	/2.0*AntParam->SubArraySpcaeRow;	//差波束阵元间距-行
	//	Param[5] = (AntParam->SubArrayNumColumn - 1)/2.0*AntParam->SubArraySpcaeRow;	//差波束阵元间距-列
	//}
	Param[4] = AntParam->AziSpace;
	Param[5] = AntParam->EleSpace;
	Param[6] = ParamR->Fc;	//波长
	Param[7] = AntParam->AziSlope;		//方位方向差斜率(比幅)
	Param[8] = AntParam->EleSlope;		//俯仰方向差斜率(比幅)
	Param[9] = AntParam->AziOverlap;	//方位方向重叠波束（比幅）
	Param[10] = AntParam->EleOverlap;	//俯仰方向重叠波束（比幅）

	//************无源干扰信号生成*************//
	RCS			Rcs[TARGET_NUM];
	int			PassJamNum = 0;              //无源干扰个数
	PLATFORM	*JamPlat[TARGET_NUM];
	double		*JamAPC[TARGET_NUM];				//无源干扰时干扰APC
	double		*PassJamRadarAPC[TARGET_NUM];	//无源干扰时雷达APC
	double		*JamPRI[TARGET_NUM];
	for(idy = 0;idy<SimData->JamNum;idy++)
	{
		if(0 == Jammer[idy].JamType)		
		{
			Rcs[PassJamNum].RcsType = 0;
			if(1 == Jammer[idy].PassiveType)			//方形反射器
			{
				Rcs[PassJamNum].Sigma0 = 0.70*PI*Jammer[idy].SideLen*Jammer[idy].SideLen*Jammer[idy].SideLen*Jammer[idy].SideLen/ParamR->Lambda/ParamR->Lambda;
			}
			else if(2 == Jammer[idy].PassiveType)	    //圆形反射器
			{
				Rcs[PassJamNum].Sigma0 = 0.47*PI*Jammer[idy].SideLen*Jammer[idy].SideLen*Jammer[idy].SideLen*Jammer[idy].SideLen/ParamR->Lambda/ParamR->Lambda;
			}
			else if(3 == Jammer[idy].PassiveType)	    //三角反射器
			{
				Rcs[PassJamNum].Sigma0 = 0.17*PI*Jammer[idy].SideLen*Jammer[idy].SideLen*Jammer[idy].SideLen*Jammer[idy].SideLen/ParamR->Lambda/ParamR->Lambda;
			}
			//else //if(4 == Jammer[idy].PassiveType)	//龙伯透镜
			//{
			//	Rcs[PassJamNum].Sigma0 = 2.00*PI*Jammer[idy].SideLen*Jammer[idy].SideLen*Jammer[idy].SideLen*Jammer[idy].SideLen/ParamR->Lambda/ParamR->Lambda;
			//}
			else if(0 == Jammer[idy].PassiveType)		//箔条（单独处理）
				continue;
			JamAPC[PassJamNum]			= APC[idy];
			JamPlat[PassJamNum]			= Target[idy];
			JamPRI[PassJamNum]			= PRI[idy];
			PassJamRadarAPC[PassJamNum] = RadarAPC[idy];
			PassJamNum++;
		}
	}
	if(PassJamNum >= 1) // 生成无源干扰(帧信号)
	{
        PassiveJamming(JamPlat, Rcs, ParamR, AntParam, SimData, PassJamRadarAPC, JamAPC, JamPRI, PassJamNum, dev_BaseSignal,
                    dev_SumJamDataPolar1,  dev_AziJamDataPolar1,  dev_EleJamDataPolar1,  dev_ExJamDataPolar1);
	}
	//************End of：无源干扰信号生成*************//
    
    //************有源干扰信号生成*************//
	
	//申请流信号空间
    Complexf *dev_JamDataFlow,*dev_AziJamDataFlow,*dev_EleJamDataFlow;
    dev_JamDataFlow		= (Complexf*)_MallocCUDA(sizeof(Complexf)*ParamR->Nr*ParamR->Na);
    dev_AziJamDataFlow	= (Complexf*)_MallocCUDA(sizeof(Complexf)*ParamR->Nr*ParamR->Na);
    dev_EleJamDataFlow	= (Complexf*)_MallocCUDA(sizeof(Complexf)*ParamR->Nr*ParamR->Na);
    cudaMemset(dev_JamDataFlow,   0,sizeof(Complexf)*ParamR->Nr*ParamR->Na);
    cudaMemset(dev_AziJamDataFlow,0,sizeof(Complexf)*ParamR->Nr*ParamR->Na);
    cudaMemset(dev_EleJamDataFlow,0,sizeof(Complexf)*ParamR->Nr*ParamR->Na);
	int Flow2SteamFlag;
    double *SweepFCur = NULL;

	for(idy = 0;idy<SimData->JamNum;idy++)
	{
		PRTTime = 0;      //用于获取下一个PRT时刻时间 PRTTime = PRTTime + *(PRI[idy] +idn) idy是JamNum,idn是Na

		if(0 == Jammer[idy].JamType)   // 若为无源干扰或者系统噪声则直接跳过该部分
			continue;
		
		if(1 == Jammer[idy].JamType && PulseJam == Jammer[idy].BalJamType)
		{
			Na = (int)(ParamR->Na/ParamR->Prf*Jammer[idy].PRF);
			PRF = Jammer[idy].PRF;
			Flow2SteamFlag = 1;
		}
		else
		{
			Na = ParamR->Na;
			PRF = ParamR->Prf;
			Flow2SteamFlag = 1;
		}
        if (Jammer[idy].BalJamType == Sweep)
        {
            int SweepCyclePRTNum = ceil(Jammer[idy].SweepCycleTime * ParamR->Prf);        //扫描周期内PRT个数
            int SweepPRTNum = ceil(Jammer[idy].JamTime * ParamR->Prf);       //扫描时间内PRT个数
            double SweepVel = (Jammer[idy].SweepFmax - Jammer[idy].SweepFmin) / SweepCyclePRTNum;
            SweepFCur = (double *)_Malloc(sizeof(double)*SweepPRTNum);
            SweepFreGen(Jammer[idy].SweepType, Jammer[idy].SweepFmin, Jammer[idy].SweepFmax, SweepVel, SweepCyclePRTNum, SweepPRTNum, SweepFCur);
        }
		for(idn = 0;idn < Na;idn++)
		{
            TargetPos.x = *(APC[idy] + idn * 3 + 0) - *(RadarAPC[idy] + idn * 3 + 0);     // 计算每一个PRF时刻的干扰机与雷达位置矢量
            TargetPos.y = *(APC[idy] + idn * 3 + 1) - *(RadarAPC[idy] + idn * 3 + 1);
            TargetPos.z = *(APC[idy] + idn * 3 + 2) - *(RadarAPC[idy] + idn * 3 + 2);

            CalAntWeight(TargetPos, AntParam->G_Matrix, AntParam->SumAntennaFunction, AntParam->AziSubAntennaFunction, AntParam->PitSubAntennaFunction, Param, AntGain, AziGain, EleGain, Range, SimData->AziEleFalg);

            if (SimData->TransLossFlag == 1)	   //功率量化
            {
                if (2 == (Jammer + idy)->JamType)		//欺骗式干扰
                {
                    TranGain = ParamR->Amp*ParamR->RadomeLoss*pow(10.0, Jammer[idy].Gain / 10.0)*AntGain[0] * ParamR->Lambda*ParamR->Lambda
                        / (64 * PI*PI*PI) / (Range[0] * Range[0] * Range[0] * Range[0]);//(Pt*Loss*Gj*G*lambda^2*sigma/((4pi)^3*R^4))
                    AziTranGain = ParamR->Amp*ParamR->RadomeLoss*pow(10.0, Jammer[idy].Gain / 10.0)*AziGain[0] * ParamR->Lambda*ParamR->Lambda
                        / (64 * PI*PI*PI) / (Range[0] * Range[0] * Range[0] * Range[0]);
                    PitTranGain = ParamR->Amp*ParamR->RadomeLoss*pow(10.0, Jammer[idy].Gain / 10.0)*EleGain[0] * ParamR->Lambda*ParamR->Lambda
                        / (64 * PI*PI*PI) / (Range[0] * Range[0] * Range[0] * Range[0]);
                }
                else
                {					//压制式干扰
                    TranGain = Jammer[idy].JamPower*pow(10.0, Jammer[idy].Gain / 10.0)*AntGain[0] * ParamR->Lambda*ParamR->Lambda
                        / (16 * PI*PI) / (Range[0] * Range[0]);                     //(Pjt*Gj*G*lambda^2/((4pi)^2*R^2))
                    AziTranGain = Jammer[idy].JamPower*pow(10.0, Jammer[idy].Gain / 10.0)*AziGain[0] * ParamR->Lambda*ParamR->Lambda
                        / (16 * PI*PI) / (Range[0] * Range[0]);
                    PitTranGain = Jammer[idy].JamPower*pow(10.0, Jammer[idy].Gain / 10.0)*EleGain[0] * ParamR->Lambda*ParamR->Lambda
                        / (16 * PI*PI) / (Range[0] * Range[0]);
                }
            }
            else	//电压量化
            {
                if (2 == (Jammer + idy)->JamType)		//欺骗式干扰
                {
                    TranGain = sqrt((ParamR->Amp*ParamR->RadomeLoss*ParamR->Lambda*ParamR->Lambda
                        / (64 * PI*PI*PI) / (Range[0] * Range[0] * Range[0] * Range[0])) * 50 * pow(10.0, Jammer[idy].Gain / 10.0)*AntGain[0]);
                    AziTranGain = sqrt((ParamR->Amp*ParamR->RadomeLoss*ParamR->Lambda*ParamR->Lambda
                        / (64 * PI*PI*PI) / (Range[0] * Range[0] * Range[0] * Range[0])) * 50 * pow(10.0, Jammer[idy].Gain / 10.0)*AziGain[0]);
                    PitTranGain = sqrt((ParamR->Amp*ParamR->RadomeLoss*ParamR->Lambda*ParamR->Lambda
                        / (64 * PI*PI*PI) / (Range[0] * Range[0] * Range[0] * Range[0])) * 50 * pow(10.0, Jammer[idy].Gain / 10.0)*EleGain[0]);
                }
                else
                {
                    TranGain = sqrt((Jammer[idy].JamPower*ParamR->Lambda*ParamR->Lambda
                        / (16 * PI*PI) / (Range[0] * Range[0])) * 50 * pow(10.0, Jammer[idy].Gain / 10.0)*AntGain[0]);
                    AziTranGain = sqrt((Jammer[idy].JamPower*ParamR->Lambda*ParamR->Lambda
                        / (16 * PI*PI) / (Range[0] * Range[0])) * 50 * pow(10.0, Jammer[idy].Gain / 10.0)*AziGain[0]);
                    PitTranGain = sqrt((Jammer[idy].JamPower*ParamR->Lambda*ParamR->Lambda
                        / (16 * PI*PI) / (Range[0] * Range[0])) * 50 * pow(10.0, Jammer[idy].Gain / 10.0)*EleGain[0]);
                }
            }

            DelayID = (int)((Range[0] /SPEEDLIGHT+ PRTTime)*ParamR->Fs + 0.5);	//每个PRF时刻的回波延时
            if (DelayID + ParamR->Prf / ParamR->Fs > SaveFlowFlieLen)
                continue;

            ActiveJamming(dev_JamDataFlow, dev_BaseSignal, ParamR, Jammer[idy], SimData, PRTTime, Range, SweepFCur, SaveFlowFlieLen, idn);

            if (0 == SimData->AziEleFalg)				//单波束
            {
                dim3 ThreadsPerBlock(512,1);
                dim3 BlockNum((PRTNum + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
				CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, PRTNum, dev_JamDataFlow, dev_JamDataFlow, TranGain);
//                for (idy = 0; idy < PRTNum; idy++)
//                {
//                    (JamDataFlow + idy)->re = TranGain*(JamDataFlow + idy)->re;
//                    (JamDataFlow + idy)->im = TranGain*(JamDataFlow + idy)->im;
//                }
            }
            else if (2 == SimData->AziEleFalg)		//和差差（比幅）
            {
                dim3 ThreadsPerBlock(512,1);
                dim3 BlockNum((PRTNum + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                CUDA_ComplexMultCoef(0,ThreadsPerBlock,BlockNum, PRTNum, dev_JamDataFlow, dev_JamDataFlow, TranGain);
                CUDA_ComplexMultCoef(0,ThreadsPerBlock,BlockNum, PRTNum, dev_JamDataFlow, dev_AziJamDataFlow, AziTranGain);
                CUDA_ComplexMultCoef(0,ThreadsPerBlock,BlockNum, PRTNum, dev_JamDataFlow, dev_EleJamDataFlow, PitTranGain);
//                for (idy = 0; idy < PRTNum; idy++)
//                {
//                    (JamDataFlow + idy)->re = TranGain*(JamDataFlow + idy)->re;
//                    (JamDataFlow + idy)->im = TranGain*(JamDataFlow + idy)->im;
//                    (AziJamDataFlow + idy)->re = AziTranGain*(JamDataFlow + idy)->re;
//                    (AziJamDataFlow + idy)->im = AziTranGain*(JamDataFlow + idy)->im;
//                    (EleJamDataFlow + idy)->re = PitTranGain*(JamDataFlow + idy)->re;
//                    (EleJamDataFlow + idy)->im = PitTranGain*(JamDataFlow + idy)->im;
//                }
            }

            PRTTime = PRTTime + *(PRI[idy] + idn);
		}//End of :for(idn = 0;idn < Na;idn++)  遍历每个方位向时刻
	}//End of: for(idy = 0;idy<SimData->JamNum;idy++)  遍历每个干扰源
	//************End of:有源干扰信号生成(流信号)*************//
    
    //************箔条无源干扰信号生成(帧信号)*************//
	for(idy = 0;idy<SimData->JamNum;idy++)
	{
		if(0 == Jammer[idy].JamType)		
		{
			Rcs[PassJamNum].RcsType = 0;
			if(0 == Jammer[idy].PassiveType)
			{
                GenChaffJam(SimData, &(Jammer[idy]), ParamR, RadarAPC[idy], AntParam->G_Matrix, AntParam, PRI[idy], dev_BaseSignal,
                            dev_SumJamDataPolar1,  dev_AziJamDataPolar1, dev_EleJamDataPolar1, dev_ExJamDataPolar1);	//固定距离门帧信号
			}
		}
	}
    
    //*****************流信号转帧信号输出********************//
    if(Flow2SteamFlag == 1)
    {
        double *RadarPRI = (double*)_Malloc(sizeof(double)*ParamR->Na);	//雷达发射信号真实重复周期
        memset(RadarPRI,0,sizeof(double)*ParamR->Na);
        GenPRI(ParamR,RadarPRI);		//生成变PRT序列
        PRTTime = 0;
        int RangeGateNum;		//可变距离门选通中距离门个数
        int GateSampleNum;		//可变距离门内采样点数
        int LastPos;			//可变距离门Region内结束ID位置
        int GateStart;			//第i个距离门开始位置
        RangeGateNum = int(SimData->RangeGateRegion/SimData->RangeGateWidth + 1);
        if(fabs(SimData->RangeGateRegion/SimData->RangeGateWidth - int(SimData->RangeGateRegion/SimData->RangeGateWidth)) < MINEQUERR)
            RangeGateNum = int(SimData->RangeGateRegion/SimData->RangeGateWidth + MINEQUERR);  //当两个double型数刚好可以整除时
        GateSampleNum = int(SimData->RangeGateWidth*ParamR->Fs + 0.5);   //即可变距离门选通的SimData->SaveFlie_Nr
        LastPos = int(SimData->RangeGateRegion*ParamR->Fs + 0.5);
        int idm = 1;		//距离门计数
        int StartID;		//每帧信号起始ID

        for(idn = 0;idn < SimData->SaveFlie_Na;idn++)
        {
            if(SimData->RangeGateMode == 1)//可变距离门
            {
                if(idn == 0)
                    idm = 1;
                else if(idn != 0 && idn%SimData->SarchNum == 0 && idm < RangeGateNum )
                    idm = idm + 1;		//下一个距离门
                else if(idn != 0 && idn%SimData->SarchNum == 0 && idm == RangeGateNum)
                    idm = 1;	
    
                if(idm<RangeGateNum)
                    GateStart = (idm-1)*GateSampleNum;
                if(idm==RangeGateNum)
                    GateStart = LastPos - GateSampleNum +1;
                StartID = int((2*SimData->Rmin/C + PRTTime)*ParamR->Fs + GateStart + 0.5);		//帧信号开始ID
            }
            else {
                StartID = int((2*SimData->Rmin/C + PRTTime)*ParamR->Fs+0.5);					//帧信号开始ID
            }
           
            if(0 == SimData->AziEleFalg)	//单波束
            {
                if (StartID >= 0)    //默认不跨重
                {
                    dim3 ThreadsPerBlock(512,1);
                    dim3 BlockNum((SimData->SaveFlie_Nr + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                    CUDA_ComplexAtomicAdd(0,ThreadsPerBlock, BlockNum, SimData->SaveFlie_Nr, (Complexf*)&dev_JamDataFlow[StartID], (T2*)&dev_SumJamDataPolar1[idn*SimData->SaveFlie_Nr]);
                    CUDA_ComplexAtomicAdd(0,ThreadsPerBlock, BlockNum, SimData->SaveFlie_Nr, (Complexf*)&dev_JamDataFlow[StartID], (T2*)&dev_ExJamDataPolar1[idn*SimData->SaveFlie_Nr]);
//                    for(idz = 0;idz<SimData->SaveFlie_Nr;idz++)
//                    {
//                        (SumJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->x += (JamDataFlow + StartID + idz)->x;
//                        (SumJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->y += (JamDataFlow + StartID + idz)->y;
//                        (SumJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->x += (JamDataFlow + StartID + idz)->x;
//                        (SumJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->y += (JamDataFlow + StartID + idz)->y;
//                        (ExJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->x += (JamDataFlow + StartID + idz)->x;
//                        (ExJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->y += (JamDataFlow + StartID + idz)->y;
//                        (ExJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->x += (JamDataFlow + StartID + idz)->x;
//                        (ExJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->y += (JamDataFlow + StartID + idz)->y;
//                    }
                }
            }
            else
            {
                if (StartID >= 0)    //默认不跨重
                {
                    dim3 ThreadsPerBlock(512,1);
                    dim3 BlockNum((SimData->SaveFlie_Nr + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
                    CUDA_ComplexAtomicAdd(0,ThreadsPerBlock, BlockNum, SimData->SaveFlie_Nr, (Complexf*)&dev_JamDataFlow[StartID], (T2*)&dev_SumJamDataPolar1[idn*SimData->SaveFlie_Nr]);
                    CUDA_ComplexAtomicAdd(0,ThreadsPerBlock, BlockNum, SimData->SaveFlie_Nr, (Complexf*)&dev_JamDataFlow[StartID], (T2*)&dev_ExJamDataPolar1[idn*SimData->SaveFlie_Nr]);

                    CUDA_ComplexAtomicAdd(0,ThreadsPerBlock, BlockNum, SimData->SaveFlie_Nr, (Complexf*)&dev_AziJamDataFlow[StartID], (T2*)&dev_AziJamDataPolar1[idn*SimData->SaveFlie_Nr]);
                    CUDA_ComplexAtomicAdd(0,ThreadsPerBlock, BlockNum, SimData->SaveFlie_Nr, (Complexf*)&dev_EleJamDataFlow[StartID], (T2*)&dev_EleJamDataPolar1[idn*SimData->SaveFlie_Nr]);
//                    for(idz = 0;idz<SimData->SaveFlie_Nr;idz++)
//                    {
//                        (SumJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->x += (JamDataFlow + StartID + idz)->x;
//                        (SumJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->y += (JamDataFlow + StartID + idz)->y;
//                        (SumJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->x += (JamDataFlow + StartID + idz)->x;
//                        (SumJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->y += (JamDataFlow + StartID + idz)->y;
//                        (ExJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->x += (JamDataFlow + StartID + idz)->x;
//                        (ExJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->y += (JamDataFlow + StartID + idz)->y;
//                        (ExJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->x += (JamDataFlow + StartID + idz)->x;
//                        (ExJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->y += (JamDataFlow + StartID + idz)->y;
//                        (AziJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->x += (AziJamDataFlow + StartID + idz)->x;
//                        (AziJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->y += (AziJamDataFlow + StartID + idz)->y;
//                        (AziJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->x += (AziJamDataFlow + StartID + idz)->x;
//                        (AziJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->y += (AziJamDataFlow + StartID + idz)->y;
//                        (EleJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->x += (EleJamDataFlow + StartID + idz)->x;
//                        (EleJamDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->y += (EleJamDataFlow + StartID + idz)->y;
//                        (EleJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->x += (EleJamDataFlow + StartID + idz)->x;
//                        (EleJamDataPolar2 + idn*SimData->SaveFlie_Nr + idz)->y += (EleJamDataFlow + StartID + idz)->y;
//                    }
                }
            }
            PRTTime = PRTTime + *(RadarPRI +idn);
        }
    }//End of:if(Flow2SteamFlag == 1)
    //Device2Host
    cudaMemcpy(SumJamDataPolar1,dev_SumJamDataPolar1,sizeof(T2)*SimData->SaveFlie_Nr*Na,cudaMemcpyDeviceToHost);
    cudaMemcpy(ExJamDataPolar1,dev_ExJamDataPolar1,sizeof(T2)*SimData->SaveFlie_Nr*Na,cudaMemcpyDeviceToHost);

    cudaMemcpy(AziJamDataPolar1,dev_AziJamDataPolar1,sizeof(T2)*SimData->SaveFlie_Nr*Na,cudaMemcpyDeviceToHost);
    cudaMemcpy(EleJamDataPolar1,dev_EleJamDataPolar1,sizeof(T2)*SimData->SaveFlie_Nr*Na,cudaMemcpyDeviceToHost);
    
}
//干扰槽函数
void ThreadClassGenJAM::slotGenJAM(void *p)
{
	_Release_Malloc();		//初始化CPU伪内存池
	_Release_MallocCUDA();	//初始化GPU伪内存池

    stJamEchoPara *pStJamEchoPara = (stJamEchoPara*)p;
    
    PLATFORM *Target    = pStJamEchoPara->Target;		//目标位置	 舰船运动参数
    JAM_PARAM *Jammer   = pStJamEchoPara->Jammer;		//干扰源参数
	RADAR *ParamR		= (RADAR*)&pStJamEchoPara->ParamR;
	ANTPARAM *AntParam	= (ANTPARAM*)&pStJamEchoPara->AntParam;
	SimStruct *SimData	= (SimStruct*)&pStJamEchoPara->SimData;
	PLATFORM *RadarPos	= (PLATFORM*)&pStJamEchoPara->RadarPos;		//雷达位置   弹头最多8个
    
    //发射信号生成
	Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) * ParamR->Nr);
	memset(BaseSignal, 0, sizeof(Complexf) * ParamR->Nr);
	int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数
	ThreadClassSendWaveGen::Instance()->TransSignalSim(ParamR, SimData, TrNum, BaseSignal);

    stEchoData *pStEchoData = _MallocMemObj(stEchoData);
	Complexf *SumJamDataPolar1 = pStEchoData->SumEchoDataPolar1;
	Complexf *AziJamDataPolar1 = pStEchoData->AziEchoDataPolar1;
	Complexf *EleJamDataPolar1 = pStEchoData->EleEchoDataPolar1;
	Complexf *ExJamDataPolar1 = pStEchoData->ExEchoDataPolar1;
    //干扰生成
    GenJAM(&Target, Jammer, ParamR, AntParam, SimData, RadarPos, BaseSignal, 
            SumJamDataPolar1, AziJamDataPolar1, EleJamDataPolar1, ExJamDataPolar1);

	pStEchoData->SumEchoData1Dot = SimData->SaveFlie_Nr;
	pStEchoData->ExEchoData1Dot = 0;
	if (SimData->AziEleFalg > 0){
		pStEchoData->AziEchoData1Dot = SimData->SaveFlie_Nr;
		pStEchoData->EleEchoData1Dot = SimData->SaveFlie_Nr;
	}
	pStEchoData->Nr_Save = SimData->SaveFlie_Nr;
	pStEchoData->Na_Save = ParamR->Na;
	//生成的回波发送到回波整合线程进行数据格式转换
	emit sendJamDataFormat(pStEchoData);
	
	_ReleaseMemObj(stJamEchoPara, pStJamEchoPara);
}

