﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Form Files">
      <UniqueIdentifier>{99349809-55BA-4b9d-BF79-8FDBB0286EB3}</UniqueIdentifier>
      <Extensions>ui</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Form Files">
      <UniqueIdentifier>{99349809-55BA-4b9d-BF79-8FDBB0286EB3}</UniqueIdentifier>
      <Extensions>ui</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="cuda">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-cuda</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="cuda_d">
      <UniqueIdentifier>{E0D8C965-CC5F-43d7-AD63-FAEF0BBC0F85}-cuda_d</UniqueIdentifier>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Generated Files\Debug">
      <UniqueIdentifier>{ab0ddd7e-e279-4d7f-96ea-f13cadd20c65}</UniqueIdentifier>
      <Extensions>cpp;moc</Extensions>
      <SourceControlFiles>False</SourceControlFiles>
    </Filter>
    <Filter Include="Generated Files\Release">
      <UniqueIdentifier>{ba73f82d-812d-4b19-86b9-78cb779b03a7}</UniqueIdentifier>
      <Extensions>cpp;moc</Extensions>
      <SourceControlFiles>False</SourceControlFiles>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\Class\ClassClutterRcsRead.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Class\ClassCoorDinate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Class\ClassEchoWaveFd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Class\ClassRcsRead.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\GLWidget\GLWidgetInstanceBWFreq.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\GLWidget\GLWidgetWave.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\MainWindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\inc\MemPool\MyMalloc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\inc\MemPool\MyMallocCUDA.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassAntennaGen.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassBlanketJamGen.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassChaffEcho.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassClutter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassDeceptionJamGen.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassGPUCalcute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassGenJAM.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassJFEcho.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassSarEcho.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassSarEchoClutter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadClassSendWaveGen.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadEchoDataFormat.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadEchoDataSave.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadResourceScheduling.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadSocket.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadSocketTCP.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\Thread\ThreadSocketTCPExtern.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\public\WhiteCalGloable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\WidgetShow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_GLWidgetInstanceBWFreq.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_GLWidgetInstanceBWFreq.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_GLWidgetWave.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_GLWidgetWave.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_MainWindow.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_MainWindow.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassAntennaGen.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassAntennaGen.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassBase.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassBase.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassBlanketJamGen.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassBlanketJamGen.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassChaffEcho.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassChaffEcho.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassClutter.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassClutter.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassDeceptionJamGen.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassDeceptionJamGen.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassGPUCalcute.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassGPUCalcute.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassGenJAM.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassGenJAM.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassJFEcho.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassJFEcho.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassSarEcho.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassSarEcho.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassSarEchoClutter.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassSarEchoClutter.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassSendWaveGen.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassSendWaveGen.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadEchoDataFormat.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadEchoDataFormat.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadEchoDataSave.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadEchoDataSave.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadResourceScheduling.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadResourceScheduling.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadSocket.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadSocket.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadSocketTCP.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadSocketTCP.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadSocketTCPExtern.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadSocketTCPExtern.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
    <ClCompile Include="Debug\moc_WidgetShow.cpp">
      <Filter>Generated Files\Debug</Filter>
    </ClCompile>
    <ClCompile Include="Release\moc_WidgetShow.cpp">
      <Filter>Generated Files\Release</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\inc\Class\ClassClutterRcsRead.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\inc\Class\ClassCoorDinate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\inc\Class\ClassEchoWaveFd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\inc\Class\ClassRcsRead.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <CustomBuild Include="..\inc\GLWidget\GLWidgetInstanceBWFreq.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\GLWidget\GLWidgetWave.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <ClInclude Include="..\..\..\cuda\inc\KernelAntenna.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\cuda\inc\KernelChaff.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\cuda\inc\KernelCoorConver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\cuda\inc\KernelCultter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\cuda\inc\KernelJAM.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\cuda\inc\KernelPublic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\cuda\inc\KernelSar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\cuda\inc\KernelSpline.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\cuda\inc\KernelTarHsys.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\cuda\inc\KernelTarHsysFd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <CustomBuild Include="..\inc\MainWindow.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <ClInclude Include="..\inc\MemPool\MyMalloc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\inc\MemPool\MyMallocCUDA.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\inc\RedGloable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <CustomBuild Include="..\inc\Thread\ThreadClassAntennaGen.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassBase.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassBlanketJamGen.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassChaffEcho.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassClutter.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassDeceptionJamGen.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassGPUCalcute.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassGenJAM.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassJFEcho.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassSarEcho.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassSarEchoClutter.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <ClInclude Include="..\inc\Thread\ThreadClassSarGenImag.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <CustomBuild Include="..\inc\Thread\ThreadClassSendWaveGen.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadEchoDataFormat.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadEchoDataSave.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadResourceScheduling.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadSocket.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadSocketTCP.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadSocketTCPExtern.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <ClInclude Include="..\..\..\inc\WhiteCalGlobel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <CustomBuild Include="..\inc\WidgetShow.h">
      <Filter>Header Files</Filter>
    </CustomBuild>
    <ClInclude Include="..\..\..\inc\GLWidget\_GLWidgetBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\inc\MemPool\_stumempool.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\inc\Extern\float2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\inc\globalExternalStruct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\inc\globalJamStruct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\inc\globalRadarStruct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\inc\Extern\wMemoryOperation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ui_MainWindow.h">
      <Filter>Generated Files</Filter>
    </ClInclude>
    <ClInclude Include="ui_WidgetShow.h">
      <Filter>Generated Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="debug\moc_predefs.h.cbt">
      <Filter>Generated Files</Filter>
    </CustomBuild>
    <CustomBuild Include="release\moc_predefs.h.cbt">
      <Filter>Generated Files</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\ui\MainWindow.ui">
      <Filter>Form Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\ui\WidgetShow.ui">
      <Filter>Form Files</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelAntenna.cu" />
    <CustomBuild Include="..\..\..\cuda\src\KernelChaff.cu" />
    <CustomBuild Include="..\..\..\cuda\src\KernelCoorConver.cu" />
    <CustomBuild Include="..\..\..\cuda\src\KernelCultter.cu" />
    <CustomBuild Include="..\..\..\cuda\src\KernelJAM.cu" />
    <CustomBuild Include="..\..\..\cuda\src\KernelPublic.cu" />
    <CustomBuild Include="..\..\..\cuda\src\KernelSar.cu" />
    <CustomBuild Include="..\..\..\cuda\src\KernelSpline.cu" />
    <CustomBuild Include="..\..\..\cuda\src\KernelTarHsys.cu" />
    <CustomBuild Include="..\..\..\cuda\src\KernelTarHsysFd.cu" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\..\..\cuda\src\KernelAntenna.cu">
      <Filter>cuda</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelChaff.cu">
      <Filter>cuda</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelCoorConver.cu">
      <Filter>cuda</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelCultter.cu">
      <Filter>cuda</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelJAM.cu">
      <Filter>cuda</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelPublic.cu">
      <Filter>cuda</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelSar.cu">
      <Filter>cuda</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelSpline.cu">
      <Filter>cuda</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelTarHsys.cu">
      <Filter>cuda</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelTarHsysFd.cu">
      <Filter>cuda</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\..\..\cuda\src\KernelAntenna.cu">
      <Filter>cuda_d</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelChaff.cu">
      <Filter>cuda_d</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelCoorConver.cu">
      <Filter>cuda_d</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelCultter.cu">
      <Filter>cuda_d</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelJAM.cu">
      <Filter>cuda_d</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelPublic.cu">
      <Filter>cuda_d</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelSar.cu">
      <Filter>cuda_d</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelSpline.cu">
      <Filter>cuda_d</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelTarHsys.cu">
      <Filter>cuda_d</Filter>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelTarHsysFd.cu">
      <Filter>cuda_d</Filter>
    </CustomBuild>
  </ItemGroup>
</Project>