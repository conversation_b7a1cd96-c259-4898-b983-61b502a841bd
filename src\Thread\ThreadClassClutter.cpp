/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassClutter.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassClutter.cpp $
 *         $Author: yening $
 *         $Revision: 160 $
 *         $Date: 2025-02-05 17:40:22 $
*******************************************************************************************/
#include "ThreadClassClutter.h"
#include<QThread>
#include<math.h>

#include <QFile>
#include <QDebug>

#include <curand.h>

#include<cuda_runtime_api.h>
#include "KernelCultter.h"
#include "KernelPublic.h"
#include "ThreadClassSendWaveGen.h"
//#include "../Spline/Spline.h"

ThreadClassClutter::ThreadClassClutter(int buffer_size,char *d_Buffer,int DevID,QObject *parent) : QObject(parent)
{
    _DefineMemPoolCUDA(stEchoData, 2);
    //分配CPU伪内存池空间
    InitMyMalloc(64*1024*1024);

	//三角函数查表
	double CosValueSample = 1.0 / 4096;
	CosValueLen = 4100;
	m_CosValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_CosValue, 0, sizeof(float)*CosValueLen);
	m_SinValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_SinValue, 0, sizeof(float)*CosValueLen);
	for (int idd = 0; idd < CosValueLen; idd++)
	{
		if (CosValueSample*idd <= 1)
		{
			m_CosValue[idd] = cos(idd*CosValueSample * 2 * PI);
			m_SinValue[idd] = sin(idd*CosValueSample * 2 * PI);
		}
	}
   

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassClutter::~ThreadClassClutter()
{

}
//初始化GPU设备工作参数
bool ThreadClassClutter::InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer)
{
	BufferInit_Cur = 0;
	BufferInit_SizeTotal = (long long)32 * 1024 * 1024;
	if (d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
		m_DevID = DevID;
		d_BufferInit = d_Buffer;
		d_BufferFather = d_Buffer + BufferInit_SizeTotal;
		//分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, buffer_size);
	}
	else{//作为独立的线程使用，单独使用一个GPU卡
		m_DevID = -1;
		d_BufferFather = nullptr;
		//分配GPU伪内存池空间
		cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
		cudaMalloc((void**)&d_Buffer, buffer_size);
		d_BufferFather = d_Buffer;
		InitMyMallocCUDA(d_BufferFather, buffer_size);
	}
	//三角函数查表
	long long Bytes = (CosValueLen*sizeof(float) + 16) / 16 * 16;
	dev_CosValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_CosValue, m_CosValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
	dev_SinValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_SinValue, m_SinValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;

	Bytes = 5000.f * 5000.f * sizeof(float);
	dev_AziSubAntennaFunction = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	dev_SumAntennaFunction = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	dev_PitSubAntennaFunction = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;

	pSumAntennaFunction = (float*)malloc(sizeof(float) * 5000 * 5000);
	memset(pSumAntennaFunction, 0, sizeof(float) * 5000 * 5000);
	pAziSubAntennaFunction = (float*)malloc(sizeof(float) * 5000 * 5000);
	memset(pAziSubAntennaFunction, 0, sizeof(float) * 5000 * 5000);
	pPitSubAntennaFunction = (float*)malloc(sizeof(float) * 5000 * 5000);
	memset(pPitSubAntennaFunction, 0, sizeof(float) * 5000 * 5000);
	pSimData = (SimStruct*)malloc(sizeof(SimStruct));
	memset(pSumAntennaFunction, 0, sizeof(SimStruct));

    return true;
}
//初始化cuda参数
bool ThreadClassClutter::InitCUDAPara()
{
    int    BATCH = 1;
    //cufftPlan1d(&plan,32768,CUFFT_C2C,BATCH);
	cufftPlan1d(&plan, 16384, CUFFT_C2C, BATCH);

	cufftPlan1d(&plan_1k, 1024, CUFFT_C2C, BATCH);
	cufftPlan1d(&plan_2k, 2*1024, CUFFT_C2C, BATCH);
	cufftPlan1d(&plan_4k, 4 * 1024, CUFFT_C2C, BATCH);
	cufftPlan1d(&plan_8k, 8 * 1024, CUFFT_C2C, BATCH);
	cufftPlan1d(&plan_16k, 16 * 1024, CUFFT_C2C, BATCH);
	cufftPlan1d(&plan_32k, 32 * 1024, CUFFT_C2C, BATCH);
	cufftPlan1d(&plan_64k, 64 * 1024, CUFFT_C2C, BATCH);
	cufftPlan1d(&plan_128k, 128 * 1024, CUFFT_C2C, BATCH);
	cufftPlan1d(&plan_256k, 256 * 1024, CUFFT_C2C, BATCH);
	cufftPlan1d(&plan_512k, 512 * 1024, CUFFT_C2C, BATCH);

	//随机数句柄产生
	curandCreateGenerator(&gen_curand, CURAND_RNG_PSEUDO_DEFAULT);

    return true;
}
cufftHandle ThreadClassClutter::CufftPlanCheck(int fftDot)
{
	switch (fftDot)
	{
	case 1024:return plan_1k;
	case 2*1024:return plan_2k;
	case 4*1024:return plan_4k;
	case 8*1024:return plan_8k;
	case 16*1024:return plan_16k;
	case 32*1024:return plan_32k;
	case 64 * 1024:return plan_64k;
	case 128 * 1024:return plan_128k;
	case 256 * 1024:return plan_256k;
	case 512 * 1024:return plan_512k;
	default:return -1;
	}
	return -1;
}

void SaveFile(int Bytes,char *pBuffer,char *filePath,bool DEV_Flag)
{
	char *pppp = (char*)malloc(Bytes);
	QFile *pQFile = new QFile;
	pQFile->setFileName(filePath); 
	pQFile->open(QFile::WriteOnly);
	if (DEV_Flag == 1){
		cudaMemcpy(pppp, pBuffer, Bytes, cudaMemcpyDeviceToHost);
		pQFile->write((char*)pppp, Bytes);
	}
	else{
		pQFile->write((char*)pBuffer, Bytes);
	}
	
	pQFile->close();
	delete pQFile;
	free(pppp);
}
void SaveFile(int Bytes, char *pBuffer, QString str, bool DEV_Flag)
{
	char *pppp = (char*)malloc(Bytes);
	QFile *pQFile = new QFile;
	pQFile->setFileName(str);
	pQFile->open(QFile::WriteOnly);
	if (DEV_Flag == 1){
		cudaMemcpy(pppp, pBuffer, Bytes, cudaMemcpyDeviceToHost);
		pQFile->write((char*)pppp, Bytes);
	}
	else{
		pQFile->write((char*)pBuffer, Bytes);
	}

	pQFile->close();
	delete pQFile;
	free(pppp);
}
//杂波生成顶层函数
template <class T, class T2, class T3, class T4>
void ThreadClassClutter::ClutterGen(SimStruct *SimData, RADAR *G_Radar, PLATFORM *RadarPos, ANTPARAM *AntParam, T *RadarAPC, T2 *PRI, int flag, T3 *BaseSignal,
									T4 *SumCluDataPolar1, T4 *AziCluDataPolar1, T4 *EleCluDataPolar1, T4 *ExCluDataPolar1)
{
	if (flag == 0) //flag=0统计型杂波，=1实时型杂波
	{
		StatisticalClutter(SimData, G_Radar, RadarPos, AntParam, BaseSignal,
							SumCluDataPolar1, AziCluDataPolar1, EleCluDataPolar1, ExCluDataPolar1);
	}
	else if (flag == 1)
	{
		RealCultter(SimData, G_Radar, RadarPos, AntParam, RadarAPC, PRI, BaseSignal,
			SumCluDataPolar1, AziCluDataPolar1, EleCluDataPolar1, ExCluDataPolar1);
	}
}

//K分布杂波生成
template < class T, class T2, class T3, class T4, class T5, class T6, class T7 >
void ThreadClassClutter::KDisClutterGen(int Num, T *dev_Pf, T2 mu, T3 Shape, T4 Scale, T5 viu, T6 *dev_Cos_val, T6 *dev_Sin_val, double Range, double *param, T7 *dev_KDisData, T7 *dev_KDisData_azi, T7 *dev_KDisData_pit)
{
	cudaError_t Err = cudaGetLastError();
	//相关系数计算
	T7 *dev_Freq_filter = (T7 *)_MallocCUDA(sizeof(T7)*Num);
	T7 *dev_Coef_filter = (T7 *)_MallocCUDA(sizeof(T7)*Num);
	cudaMemset(dev_Freq_filter, 0, sizeof(T7)*Num);
	cudaMemset(dev_Freq_filter, 0, sizeof(T7)*Num);
	
	dim3 ThreadsPerBlock(512,1);
	dim3 BlockNum((Num + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
	CUDA_CoefFilterGen(0, ThreadsPerBlock, BlockNum, Num, 1.0 / Num, (Num - 1) / 2.0, dev_Pf, dev_Cos_val, dev_Sin_val, dev_Freq_filter);
	cufftHandle cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_Freq_filter, dev_Coef_filter, CUFFT_INVERSE);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_Coef_filter, dev_Coef_filter, 1.0 / Num);

	Err = cudaGetLastError();

	double *dev_Coef_real = (double *)_MallocCUDA(sizeof(double)*Num);
	CUDA_ComplexAbs(0, ThreadsPerBlock, BlockNum, Num, 1, dev_Coef_filter, dev_Coef_real);

	Err = cudaGetLastError();
	int theta = (viu + 1) * 2;
	double coef1, coef2;
	if (viu <= 50)
	{
		coef1 = tgamma(viu + 3 / 2)*tgamma(3 / 2)*tgamma(viu + 1);
		coef2 = abs(coef1 * coef1 / (viu + 1 - coef1 * coef1));
	}
	else
		coef2 = 1;

	double aValue = -1 / 2.0;
	double bValue = -1 / 2.0;
	double cValue = viu + 1;

	double xDelta = 0.001;
	int xNum = (1 - 0) / xDelta + 1;
	double *dev_xValue = (double *)_MallocCUDA(sizeof(double)*xNum);
	double *dev_yValue = (double *)_MallocCUDA(sizeof(double)*xNum);
	cudaMemset(dev_xValue, 0, sizeof(double) * xNum);
	cudaMemset(dev_yValue, 0, sizeof(double) * xNum);
	CUDA_xValueGen(0, ThreadsPerBlock, BlockNum, xNum, xDelta, dev_xValue);
	Err = cudaGetLastError();

	////RhoFilterGen(Shape, aValue, bValue, cValue, coef1, 0, xNum, 0, 0, xValue, yValue);
	CUDA_RhoFilterGen(0, ThreadsPerBlock, BlockNum, Shape, aValue, bValue, cValue, coef1, 0, xNum, 0, 0, dev_xValue, dev_yValue);

	double *xValue = (double*)_Malloc(sizeof(double)*xNum);
	double *yValue = (double*)_Malloc(sizeof(double)*xNum);
	cudaMemcpy(yValue, dev_yValue, sizeof(double)*xNum, cudaMemcpyDeviceToHost);
	double ymin = yValue[0];
	double ymax = yValue[0];
	for (int i = 0; i < xNum; i++)//求最值后续再用cuda替代
	{
		if (yValue[i] < ymin)
			ymin = yValue[i];
		if (yValue[i] > ymax)
			ymax = yValue[i];
	}
	double kk = 1.0 / (ymax - ymin);
	cudaMemcpy(dev_yValue,yValue, sizeof(double)*xNum, cudaMemcpyHostToDevice);
	CUDA_yValue_KK(0, ThreadsPerBlock, BlockNum, xNum, kk, ymin, dev_yValue);

	Err = cudaGetLastError();
	double *dev_Rhoijd = (double *)_MallocCUDA(sizeof(double)*Num);
	double *dev_GivenX = (double*)_MallocCUDA(sizeof(double)*Num);
	double *dev_GivenY = (double*)_MallocCUDA(sizeof(double)*Num);
	double *dev_PartialDerivative = (double*)_MallocCUDA(sizeof(double)*Num);
	cudaMemcpy(xValue, dev_xValue, sizeof(double)*xNum, cudaMemcpyDeviceToHost);
	cudaMemcpy(yValue, dev_yValue, sizeof(double)*xNum, cudaMemcpyDeviceToHost);

//	Spline sp(yValue, xValue, xNum, GivenSecondOrder);
//	cudaMemcpy(dev_GivenX, sp.GivenX, sizeof(double)*sp.GetGivenNum(), cudaMemcpyHostToDevice);
//	cudaMemcpy(dev_GivenY, sp.GivenY, sizeof(double)*sp.GetGivenNum(), cudaMemcpyHostToDevice);
//	cudaMemcpy(dev_PartialDerivative, sp.PartialDerivative, sizeof(double)*sp.GetGivenNum(), cudaMemcpyHostToDevice);
//	sp.MultiPointInterp(Num, dev_GivenX, dev_GivenY, dev_PartialDerivative, dev_Coef_real, dev_Rhoijd);//求x的插值结果y

	Err = cudaGetLastError();
	//实转复
	T7 *dev_Rhoij = (T7*)_MallocCUDA(sizeof(T7)*Num);
	CUDA_Real2Complex(0, ThreadsPerBlock, BlockNum, Num, dev_Rhoijd, dev_Rhoij);

	//数据滤波
	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_Rhoij, dev_Rhoij, CUFFT_FORWARD);

	//----------------------------随机噪声---------------------------//
	unsigned int *dev_random = (unsigned int*)_MallocCUDA(sizeof(unsigned int)*Num*2);
	cudaMemset(dev_random, 0, sizeof(unsigned int)*Num*2);
	curandStatus_t ret = curandSetPseudoRandomGeneratorSeed(gen_curand, (time(NULL) + rand()));
	curandGenerate(gen_curand, dev_random, Num * 2);
	//--------------------------------------------------------------//

	//产生K分布杂波
	double Sigma = 0.5;
	T7 *dev_xt_temp = (T7*)_MallocCUDA(sizeof(T7)*Num);
	CUDA_xt_temp(0, ThreadsPerBlock, BlockNum, Num, dev_CosValue, dev_SinValue, dev_random, dev_xt_temp);

	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_xt_temp, dev_xt_temp, CUFFT_FORWARD);

	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_temp, dev_Freq_filter, dev_xt_temp, 1);

	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_xt_temp, dev_xt_temp, CUFFT_INVERSE);

	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_temp, dev_xt_temp, 1.0 / Num);
	Err = cudaGetLastError();
	T7 *dev_xt_I0 = (T7 *)_MallocCUDA(sizeof(T7)*Num);
	T7 *dev_xt_1 = (T7 *)_MallocCUDA(sizeof(T7)*Num);
	cudaMemset(dev_xt_1, 0, sizeof(T7) * Num);

	QString str;
	for (int idx = 0; idx < theta; idx++)//theta
	{
		cudaMemset(dev_xt_I0, 0, sizeof(T7) * Num);
		curandSetPseudoRandomGeneratorSeed(gen_curand, (time(NULL) + rand() + idx*1e5));
		curandGenerate(gen_curand, dev_random, Num * 2);
		cudaError_t Err1 = cudaGetLastError();
		CUDA_GenGuassNoise(0, ThreadsPerBlock, BlockNum, Num, Sigma, dev_CosValue, dev_SinValue, dev_random, dev_xt_I0);

		cudaError_t Err12 = cudaGetLastError();
		CUDA_Complex2Zero(0, ThreadsPerBlock, BlockNum, Num, 2, dev_xt_I0);

		cudaError_t Err13 = cudaGetLastError();
		cufftPlan = CufftPlanCheck(Num);
		_CufftExecC2C(cufftPlan, dev_xt_I0, dev_xt_I0, CUFFT_FORWARD);
		cudaError_t Err14 = cudaGetLastError();

		CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_I0, dev_Rhoij, dev_xt_I0, 1);

		cudaError_t Err15 = cudaGetLastError();
		cufftPlan = CufftPlanCheck(Num);
		_CufftExecC2C(cufftPlan, dev_xt_I0, dev_xt_I0, CUFFT_INVERSE);
		cudaError_t Err16 = cudaGetLastError();

		CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_I0, dev_xt_I0, 1.0 / Num);

		cudaError_t Err17 = cudaGetLastError();
		CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_I0, dev_xt_I0, dev_xt_I0, 1);
		cudaError_t Err18 = cudaGetLastError();

		CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, Num, dev_xt_1, dev_xt_I0, dev_xt_1);
		Err = cudaGetLastError();
	}

	curandSetPseudoRandomGeneratorSeed(gen_curand, (time(NULL) + 46444484 + rand()));
	curandGenerate(gen_curand, dev_random, Num * 2);

	T7 *dev_xt_I = (T7 *)_MallocCUDA(sizeof(T7)*Num);
	T7 *dev_xt_Q = (T7 *)_MallocCUDA(sizeof(T7)*Num);
	CUDA_GenGuassNoise(0, ThreadsPerBlock, BlockNum, Num, 1, dev_CosValue, dev_SinValue, dev_random, dev_xt_I);
	cudaMemset(dev_xt_Q, 0, sizeof(T7)*Num);
	CUDA_ComplexImg2Real(0, ThreadsPerBlock, BlockNum, Num, dev_xt_I, dev_xt_Q);
	CUDA_Complex2Zero(0, ThreadsPerBlock, BlockNum, Num, 2, dev_xt_I);

	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_xt_I, dev_xt_I, CUFFT_FORWARD);
	_CufftExecC2C(cufftPlan, dev_xt_Q, dev_xt_Q, CUFFT_FORWARD);
	
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_I, dev_Rhoij, dev_xt_I, 1);
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_Q, dev_Rhoij, dev_xt_Q, 1);

	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_xt_I, dev_xt_I, CUFFT_INVERSE);
	_CufftExecC2C(cufftPlan, dev_xt_Q, dev_xt_Q, CUFFT_INVERSE);
	Err = cudaGetLastError();
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_I, dev_xt_I, 1.0 / Num);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_Q, dev_xt_Q, 1.0 / Num);
	
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_I, dev_xt_I, dev_xt_I, 1);
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_Q, dev_xt_Q, dev_xt_Q, 1);

	//产生杂波数据
	T7 *dev_xt_data = (T7 *)_MallocCUDA(sizeof(T7)*Num);
	T7 *dev_xt_data_sum = (T7 *)_MallocCUDA(sizeof(T7)*Num);
	T7 *dev_xt_data_azi = (T7 *)_MallocCUDA(sizeof(T7)*Num);
	T7 *dev_xt_data_pit = (T7 *)_MallocCUDA(sizeof(T7)*Num);
	CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, Num, dev_xt_I, dev_xt_Q, dev_xt_data);
	Err = cudaGetLastError();
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_data, dev_xt_1, dev_xt_data, 1);
	CUDA_ComplexSqrt(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data);
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_data, dev_xt_temp, dev_xt_data, 1);

	T *dev_Std = (T *)_MallocCUDA(sizeof(T7)*Num);
	CUDA_StdCal1(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_Std);

	double sum1 = 0, sum2 = 0;
	T *pStd = (T*)_Malloc(sizeof(T7)*Num);
	cudaMemcpy(pStd, dev_Std, sizeof(T7)*Num, cudaMemcpyDeviceToHost);
	for (int i = 0; i < Num; i++)sum1 += pStd[i];
	T MeanValue = sum1 / Num;
	T *dev_Var = (T *)_MallocCUDA(sizeof(T7)*Num);
	CUDA_VarCal(0, ThreadsPerBlock, BlockNum, Num, sum1 / Num, dev_Std, dev_Var);

	cudaMemcpy(pStd, dev_Var, sizeof(T7)*Num, cudaMemcpyDeviceToHost);
	for (int i = 0; i < Num; i++)sum2 += pStd[i];
	T VarValue = sum2 / (Num - 1);
	T StdValue = sqrt(VarValue);

	double ratio = mu / MeanValue;

	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data, ratio);

	//天线方向图
	float GainLossLbdPI3_64_ref = param[0];
	float SumGain = param[1];
	float AziGain = param[2];
	float PitGain = param[3];
	float sigma = 1;
	float sigma_i = 1 * sigma;
	float sigma_q = 0 * sigma;

	float tempCoef1 = GainLossLbdPI3_64_ref*sigma / (Range * Range * Range * Range);
	float tempCoef = SumGain*sqrt(tempCoef1);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data_sum, tempCoef);
	//dev_xt_data_sum = dev_xt_data*tempCoef;
	cudaMemcpy(dev_KDisData, dev_xt_data_sum, sizeof(T7)*Num, cudaMemcpyDeviceToDevice);

	float tempCoef_azi = AziGain*sqrt(tempCoef1);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data_azi, tempCoef_azi);
	//dev_xt_data_azi = dev_xt_data*tempCoef_azi;
	cudaMemcpy(dev_KDisData_azi, dev_xt_data_azi, sizeof(T7)*Num, cudaMemcpyDeviceToDevice);

	float tempCoef_pit = PitGain*sqrt(tempCoef1);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data_pit, tempCoef_pit);
	//dev_xt_data_pit = dev_xt_data*tempCoef_pit;
	cudaMemcpy(dev_KDisData_pit, dev_xt_data_pit, sizeof(T7)*Num, cudaMemcpyDeviceToDevice);
}

//威布尔杂波生成
template <class T, class T2, class T3, class T4, class T5>
void ThreadClassClutter::WeibullClutterGen(int Num, T *dev_Pf, T2 mu, T3 Shape, T4 Scale, double Range, double *param, T5 *WeibullData, T5 *WeibullData_azi, T5 *WeibullData_pit)
{
	QString str;
	cudaError_t Err = cudaGetLastError();
	//相关系数计算
	T5 *dev_Freq_filter = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_Coef_filter = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	cudaMemset(dev_Freq_filter, 0, sizeof(T5)*Num);
	cudaMemset(dev_Freq_filter, 0, sizeof(T5)*Num);

	dim3 ThreadsPerBlock(512, 1);
	dim3 BlockNum((Num + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
	CUDA_CoefFilterGen(0, ThreadsPerBlock, BlockNum, Num, 1.0 / Num, (Num - 1) / 2.0, dev_Pf, dev_CosValue, dev_SinValue, dev_Freq_filter);
	cufftHandle cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_Freq_filter, dev_Coef_filter, CUFFT_INVERSE);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_Coef_filter, dev_Coef_filter, 1.0 / Num);
	Err = cudaGetLastError();

	double *dev_Coef_real = (double *)_MallocCUDA(sizeof(double)*Num);
	CUDA_ComplexAbs(0, ThreadsPerBlock, BlockNum, Num, 1, dev_Coef_filter, dev_Coef_real);
	Err = cudaGetLastError();


	double coef1 = (tgamma(1 + 1 / Shape)) *(tgamma(1 + 1 / Shape)) /
		(tgamma(1.0 + 2.0 / Shape) + (tgamma(1 + 1 / Shape)) *(tgamma(1 + 1 / Shape)));
	double aValue = -1 / Shape;
	double bValue = -1 / Shape;
	double cValue = 1;
	double xDelta = 0.001;
	int xNum = (1 - 0) / xDelta + 1;
	double *dev_xValue = (double *)_MallocCUDA(sizeof(double)*xNum);
	double *dev_yValue = (double *)_MallocCUDA(sizeof(double)*xNum);
	cudaMemset(dev_xValue, 0, sizeof(double) * xNum);
	cudaMemset(dev_yValue, 0, sizeof(double) * xNum);
	CUDA_xValueGen(0, ThreadsPerBlock, BlockNum, xNum, xDelta, dev_xValue);
	Err = cudaGetLastError();

	////RhoFilterGen(Shape, aValue, bValue, cValue, coef1, 0, xNum, 0, 0, xValue, yValue);
	CUDA_RhoFilterGen(0, ThreadsPerBlock, BlockNum, Shape, aValue, bValue, cValue, coef1, 0, xNum, 0, 0, dev_xValue, dev_yValue);

	double *xValue = (double*)_Malloc(sizeof(double)*xNum);
	double *yValue = (double*)_Malloc(sizeof(double)*xNum);
	cudaMemcpy(yValue, dev_yValue, sizeof(double)*xNum, cudaMemcpyDeviceToHost);
	double ymin = yValue[0];
	double ymax = yValue[0];
	for (int i = 0; i < xNum; i++)//求最值后续再用cuda替代
	{
		if (yValue[i] < ymin)
			ymin = yValue[i];
		if (yValue[i] > ymax)
			ymax = yValue[i];
	}
	double kk = 1.0 / (ymax - ymin);
	cudaMemcpy(dev_yValue, yValue, sizeof(double)*xNum, cudaMemcpyHostToDevice);
	CUDA_yValue_KK(0, ThreadsPerBlock, BlockNum, xNum, kk, ymin, dev_yValue);

	double *dev_Rhoijd = (double *)_MallocCUDA(sizeof(double)*Num);
	double *dev_GivenX = (double*)_MallocCUDA(sizeof(double)*Num);
	double *dev_GivenY = (double*)_MallocCUDA(sizeof(double)*Num);
	double *dev_PartialDerivative = (double*)_MallocCUDA(sizeof(double)*Num);
	cudaMemcpy(xValue, dev_xValue, sizeof(double)*xNum, cudaMemcpyDeviceToHost);
	cudaMemcpy(yValue, dev_yValue, sizeof(double)*xNum, cudaMemcpyDeviceToHost);

//	Spline sp(yValue, xValue, xNum, GivenSecondOrder);
//	cudaMemcpy(dev_GivenX, sp.GivenX, sizeof(double)*sp.GetGivenNum(), cudaMemcpyHostToDevice);
//	cudaMemcpy(dev_GivenY, sp.GivenY, sizeof(double)*sp.GetGivenNum(), cudaMemcpyHostToDevice);
//	cudaMemcpy(dev_PartialDerivative, sp.PartialDerivative, sizeof(double)*sp.GetGivenNum(), cudaMemcpyHostToDevice);
//	sp.MultiPointInterp(Num, dev_GivenX, dev_GivenY, dev_PartialDerivative, dev_Coef_real, dev_Rhoijd);//求x的插值结果y

	Err = cudaGetLastError();

	//实转复
	T5 *dev_Rhoij = (T5*)_MallocCUDA(sizeof(T5)*Num);
	CUDA_Real2Complex(0, ThreadsPerBlock, BlockNum, Num, dev_Rhoijd, dev_Rhoij);

	//数据滤波
	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_Rhoij, dev_Rhoij, CUFFT_FORWARD);


	//产生威布尔分布杂波
	//----------------------------随机噪声---------------------------//
	unsigned int *dev_random = (unsigned int*)_MallocCUDA(sizeof(unsigned int)*Num * 2);
	cudaMemset(dev_random, 0, sizeof(unsigned int)*Num * 2);
	curandSetPseudoRandomGeneratorSeed(gen_curand, (time(NULL) + 46444484 + rand()));
	curandGenerate(gen_curand, dev_random, Num * 2);

	T5 *dev_xt_I = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_xt_Q = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	CUDA_GenGuassNoise(0, ThreadsPerBlock, BlockNum, Num, 1, dev_CosValue, dev_SinValue, dev_random, dev_xt_I);
	cudaMemset(dev_xt_Q, 0, sizeof(T5)*Num);
	CUDA_ComplexImg2Real(0, ThreadsPerBlock, BlockNum, Num, dev_xt_I, dev_xt_Q);
	CUDA_Complex2Zero(0, ThreadsPerBlock, BlockNum, Num, 2, dev_xt_I);

	T5 *dev_xt_temp = (T5*)_MallocCUDA(sizeof(T5)*Num);
	CUDA_xt_temp(0, ThreadsPerBlock, BlockNum, Num, dev_CosValue, dev_SinValue, dev_random, dev_xt_temp);

	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_xt_I, dev_xt_I, CUFFT_FORWARD);
	_CufftExecC2C(cufftPlan, dev_xt_Q, dev_xt_Q, CUFFT_FORWARD);
	_CufftExecC2C(cufftPlan, dev_xt_temp, dev_xt_temp, CUFFT_FORWARD);

	CUDA_ComplexMutiple(0,ThreadsPerBlock, BlockNum, dev_xt_I, dev_Rhoij, dev_xt_I, 1);
	CUDA_ComplexMutiple(0,ThreadsPerBlock, BlockNum, dev_xt_Q, dev_Rhoij, dev_xt_Q, 1);
	CUDA_ComplexMutiple(0,ThreadsPerBlock, BlockNum, dev_xt_temp, dev_Rhoij, dev_xt_temp, 1);

	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_xt_I, dev_xt_I, CUFFT_INVERSE);
	_CufftExecC2C(cufftPlan, dev_xt_Q, dev_xt_Q, CUFFT_INVERSE);
	_CufftExecC2C(cufftPlan, dev_xt_temp, dev_xt_temp, CUFFT_INVERSE);

	double Just_coef = sqrt(pow(Scale, Shape) / 2);
	CUDA_ComplexMultCoef(0,ThreadsPerBlock, BlockNum, Num, dev_xt_I, dev_xt_I, 1.0 / Num*Just_coef);
	CUDA_ComplexMultCoef(0,ThreadsPerBlock, BlockNum, Num, dev_xt_Q, dev_xt_Q, 1.0 / Num*Just_coef);
	CUDA_ComplexMultCoef(0,ThreadsPerBlock, BlockNum, Num, dev_xt_temp, dev_xt_temp, 1.0 / Num);

	CUDA_ComplexMutiple(0,ThreadsPerBlock, BlockNum, dev_xt_I, dev_xt_I, dev_xt_I, 1);
	CUDA_ComplexMutiple(0,ThreadsPerBlock, BlockNum, dev_xt_Q, dev_xt_Q, dev_xt_Q, 1);
	//产生杂波数据
	T5 *dev_xt_data = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_xt_data_sum = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_xt_data_azi = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_xt_data_pit = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, Num, dev_xt_I, dev_xt_Q, dev_xt_data);
	Err = cudaGetLastError();

	//ComplexPow(Coef_filter, Num, (1.0 / Shape));
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_data, dev_xt_temp, dev_xt_data, 1);
	
	T *dev_Std = (T *)_MallocCUDA(sizeof(T)*Num);
	CUDA_StdCal1(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_Std);

	double sum1 = 0, sum2 = 0;
	T *pStd = (T*)_Malloc(sizeof(T)*Num);
	cudaMemcpy(pStd, dev_Std, sizeof(T)*Num, cudaMemcpyDeviceToHost);
	for (int i = 0; i < Num; i++)sum1 += pStd[i];
	T MeanValue = sum1 / Num;
	T *dev_Var = (T *)_MallocCUDA(sizeof(T)*Num);
	CUDA_VarCal(0, ThreadsPerBlock, BlockNum, Num, sum1 / Num, dev_Std, dev_Var);

	cudaMemcpy(pStd, dev_Var, sizeof(T)*Num, cudaMemcpyDeviceToHost);
	for (int i = 0; i < Num; i++)sum2 += pStd[i];
	T VarValue = sum2 / (Num - 1);
	T StdValue = sqrt(VarValue);

	double ratio = mu / MeanValue;
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data, ratio);

	//天线方向图
	float GainLossLbdPI3_64_ref = param[0];
	float SumGain = param[1];
	float AziGain = param[2];
	float PitGain = param[3];
	float sigma = 1;
	float sigma_i = 1 * sigma;
	float sigma_q = 0 * sigma;

	float tempCoef1 = GainLossLbdPI3_64_ref*sigma / (Range * Range * Range * Range);
	float tempCoef = SumGain*sqrt(tempCoef1);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data_sum, tempCoef);
	cudaMemcpy(WeibullData, dev_xt_data_sum, sizeof(T5)*Num, cudaMemcpyDeviceToDevice);

	float tempCoef_azi = AziGain*sqrt(tempCoef1);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data_azi, tempCoef_azi);
	cudaMemcpy(WeibullData_azi, dev_xt_data_azi, sizeof(T5)*Num, cudaMemcpyDeviceToDevice);

	float tempCoef_pit = PitGain*sqrt(tempCoef1);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data_pit, tempCoef_pit);
	cudaMemcpy(WeibullData_pit, dev_xt_data_pit, sizeof(T5)*Num, cudaMemcpyDeviceToDevice);


	//cudaMemcpy(WeibullData, dev_xt_data, sizeof(T5)*Num, cudaMemcpyDeviceToHost);

}

//对数正态杂波生成
template <class T,class T2,class T3,class T5>
void ThreadClassClutter::LogNormClutterGen(int Num, T *dev_Pf, T2 mu, T3 sigma_m, T5 *LogNormData)
{
	QString str;
	cudaError_t Err = cudaGetLastError();
	//相关系数计算
	T5 *dev_Freq_filter = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_Coef_filter = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	cudaMemset(dev_Freq_filter, 0, sizeof(T5)*Num);
	cudaMemset(dev_Freq_filter, 0, sizeof(T5)*Num);

	dim3 ThreadsPerBlock(512, 1);
	dim3 BlockNum((Num + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
	CUDA_CoefFilterGen(0, ThreadsPerBlock, BlockNum, Num, 1.0 / Num, (Num - 1) / 2.0, dev_Pf, dev_CosValue, dev_SinValue, dev_Freq_filter);
	cufftHandle cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_Freq_filter, dev_Coef_filter, CUFFT_INVERSE);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_Coef_filter, dev_Coef_filter, 1.0 / Num);
	Err = cudaGetLastError();

	CUDA_ComplexLog(0, ThreadsPerBlock, BlockNum, Num, sigma_m*sigma_m, 1.f / (sigma_m*sigma_m), dev_Coef_filter, dev_Coef_filter);
	Err = cudaGetLastError();
	//数据滤波
	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_Coef_filter, dev_Coef_filter, CUFFT_FORWARD);

	//产生对数正态分布杂波
	//----------------------------随机噪声---------------------------//
	unsigned int *dev_random = (unsigned int*)_MallocCUDA(sizeof(unsigned int)*Num * 2);
	cudaMemset(dev_random, 0, sizeof(unsigned int)*Num * 2);
	curandSetPseudoRandomGeneratorSeed(gen_curand, (time(NULL) + 46444484 + rand()));
	curandGenerate(gen_curand, dev_random, Num * 2);

	T5 *dev_xt_I = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_xt_Q = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	CUDA_GenGuassNoise(0, ThreadsPerBlock, BlockNum, Num, 1, dev_CosValue, dev_SinValue, dev_random, dev_xt_I);
	CUDA_Complex2Zero(0, ThreadsPerBlock, BlockNum, Num, 2, dev_xt_I);

	T5 *dev_xt_temp = (T5*)_MallocCUDA(sizeof(T5)*Num);
	CUDA_xt_temp(0, ThreadsPerBlock, BlockNum, Num, dev_CosValue, dev_SinValue, dev_random, dev_xt_temp);

	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_xt_I, dev_xt_I, CUFFT_FORWARD);
	_CufftExecC2C(cufftPlan, dev_xt_temp, dev_xt_temp, CUFFT_FORWARD);

	CUDA_ComplexMutiple(0,ThreadsPerBlock, BlockNum, dev_xt_I, dev_Coef_filter, dev_xt_I, 1);
	CUDA_ComplexMutiple(0,ThreadsPerBlock, BlockNum, dev_xt_temp, dev_Coef_filter, dev_xt_temp, 1);

	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_xt_I, dev_xt_I, CUFFT_INVERSE);
	_CufftExecC2C(cufftPlan, dev_xt_temp, dev_xt_temp, CUFFT_INVERSE);
	//产生杂波数据
	CUDA_ComplexMultCoefExp(0, ThreadsPerBlock, BlockNum, Num, 1.0 / Num, sigma_m, mu, dev_xt_I, dev_xt_I);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_temp, dev_xt_temp, 1.0 / Num);

	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_I, dev_xt_temp, dev_xt_I, 1);

	T5 *dev_xt_data = dev_xt_I;
	T *dev_Std = (T *)_MallocCUDA(sizeof(T)*Num);
	CUDA_StdCal1(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_Std);

	double sum1 = 0, sum2 = 0;
	T *pStd = (T*)_Malloc(sizeof(T)*Num);
	cudaMemcpy(pStd, dev_Std, sizeof(T)*Num, cudaMemcpyDeviceToHost);
	for (int i = 0; i < Num; i++)sum1 += pStd[i];
	T MeanValue = sum1 / Num;
	T *dev_Var = (T *)_MallocCUDA(sizeof(T)*Num);
	CUDA_VarCal(0, ThreadsPerBlock, BlockNum, Num, sum1 / Num, dev_Std, dev_Var);

	cudaMemcpy(pStd, dev_Var, sizeof(T)*Num, cudaMemcpyDeviceToHost);
	for (int i = 0; i < Num; i++)sum2 += pStd[i];
	T VarValue = sum2 / (Num - 1);
	T StdValue = sqrt(VarValue);

	double ratio = mu / MeanValue;

	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data, ratio);

	cudaMemcpy(LogNormData, dev_xt_data, sizeof(T5)*Num, cudaMemcpyDeviceToHost);

}

//瑞利杂波生成
template <class T,class T2,class T5>
void ThreadClassClutter::RelayClutterGen(int Num, T *dev_Pf, T2 mu, double Range, double *param, T5 *RelayData, T5 *RelayData_azi, T5 *RelayData_pit)
{

	QString str;
	cudaError_t Err = cudaGetLastError();
	//相关系数计算
	T5 *dev_Freq_filter = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_Coef_filter = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	cudaMemset(dev_Freq_filter, 0, sizeof(T5)*Num);
	cudaMemset(dev_Freq_filter, 0, sizeof(T5)*Num);

	dim3 ThreadsPerBlock(512, 1);
	dim3 BlockNum((Num + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
	CUDA_CoefFilterGen(0, ThreadsPerBlock, BlockNum, Num, 1.0 / Num, (Num - 1) / 2.0, dev_Pf, dev_CosValue, dev_SinValue, dev_Freq_filter);
	cufftHandle cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_Freq_filter, dev_Coef_filter, CUFFT_INVERSE);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_Coef_filter, dev_Coef_filter, 1.0 / Num);
	Err = cudaGetLastError();
	//虚部置0
	CUDA_Complex2Zero(0, ThreadsPerBlock, BlockNum, Num, 2, dev_Coef_filter);

	//数据滤波
	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_Coef_filter, dev_Coef_filter, CUFFT_FORWARD);

	//产生瑞利分布杂波
	//----------------------------随机噪声---------------------------//
	unsigned int *dev_random = (unsigned int*)_MallocCUDA(sizeof(unsigned int)*Num * 2);
	cudaMemset(dev_random, 0, sizeof(unsigned int)*Num * 2);
	curandSetPseudoRandomGeneratorSeed(gen_curand, (time(NULL) + 46444484 + rand()));
	curandGenerate(gen_curand, dev_random, Num * 2);

	T5 *dev_xt_I = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_xt_Q = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	CUDA_GenGuassNoise(0, ThreadsPerBlock, BlockNum, Num, 1, dev_CosValue, dev_SinValue, dev_random, dev_xt_I);
	cudaMemset(dev_xt_Q, 0, sizeof(T5)*Num);
	CUDA_ComplexImg2Real(0, ThreadsPerBlock, BlockNum, Num, dev_xt_I, dev_xt_Q);
	CUDA_Complex2Zero(0, ThreadsPerBlock, BlockNum, Num, 2, dev_xt_I);

	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_xt_I, dev_xt_I, CUFFT_FORWARD);
	_CufftExecC2C(cufftPlan, dev_xt_Q, dev_xt_Q, CUFFT_FORWARD);

	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_I, dev_Coef_filter, dev_xt_I, 1);
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_xt_Q, dev_Coef_filter, dev_xt_Q, 1);

	cufftPlan = CufftPlanCheck(Num);
	_CufftExecC2C(cufftPlan, dev_xt_I, dev_xt_I, CUFFT_INVERSE);
	_CufftExecC2C(cufftPlan, dev_xt_Q, dev_xt_Q, CUFFT_INVERSE);



	//产生杂波数据
	T5 *dev_xt_data = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_xt_data_sum = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_xt_data_azi = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	T5 *dev_xt_data_pit = (T5 *)_MallocCUDA(sizeof(T5)*Num);
	CUDA_Complex2Combin(0, ThreadsPerBlock, BlockNum, Num, dev_xt_I, dev_xt_Q, dev_xt_data);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data, 1.0 / Num);

	T *dev_Std = (T *)_MallocCUDA(sizeof(T)*Num);
	CUDA_StdCal1(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_Std);

	double sum1 = 0, sum2 = 0;
	T *pStd = (T*)_Malloc(sizeof(T)*Num);
	cudaMemcpy(pStd, dev_Std, sizeof(T)*Num, cudaMemcpyDeviceToHost);
	for (int i = 0; i < Num; i++)sum1 += pStd[i];
	T MeanValue = sum1 / Num;
	T *dev_Var = (T *)_MallocCUDA(sizeof(T)*Num);
	CUDA_VarCal(0, ThreadsPerBlock, BlockNum, Num, sum1 / Num, dev_Std, dev_Var);

	cudaMemcpy(pStd, dev_Var, sizeof(T)*Num, cudaMemcpyDeviceToHost);
	for (int i = 0; i < Num; i++)sum2 += pStd[i];
	T VarValue = sum2 / (Num - 1);
	T StdValue = sqrt(VarValue);

	double ratio = mu / MeanValue;
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, dev_xt_data, ratio);

	//天线方向图
	float GainLossLbdPI3_64_ref = param[0];
	float SumGain = param[1];
	float AziGain = param[2];
	float PitGain = param[3];
	float sigma = 1;
	//float sigma_i = 1 * sigma;
	//float sigma_q = 0 * sigma;

	float tempCoef1 = GainLossLbdPI3_64_ref*sigma / (Range * Range * Range * Range);
	float tempCoef = SumGain*sqrt(tempCoef1);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, RelayData, tempCoef);

	float tempCoef_azi = AziGain*sqrt(tempCoef1);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, RelayData_azi, tempCoef_azi);

	float tempCoef_pit = PitGain*sqrt(tempCoef1);
	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, Num, dev_xt_data, RelayData_pit, tempCoef_pit);

}

//*****统计模型杂波*******//
template <class T,class T2>
void ThreadClassClutter::StatisticalClutter(SimStruct *SimData, RADAR *G_Radar, PLATFORM *RadarPos, ANTPARAM *AntParam, T2 *BaseSignal,
											T *SumCluDataPolar1, T *AziCluDataPolar1, T *EleCluDataPolar1, T *ExCluDataPolar1)
{
	int idx, idy;
	int	FFTLen = 2048;
	//生成功率谱，1为高斯谱，2为立方谱，3为指数谱
	double *dev_Pf = (double *)_MallocCUDA(sizeof(double)*FFTLen);
	T *ClutterData = (T *)_Malloc(sizeof(T)*FFTLen);
	T *ClutterData_azi = (T *)_Malloc(sizeof(T)*FFTLen);
	T *ClutterData_pit = (T *)_Malloc(sizeof(T)*FFTLen);
	double	*Ps_i = (double*)_Malloc(sizeof(double)*3);
	memset(Ps_i, 0, sizeof(double) * 3);
	double *param = (double*)_Malloc(sizeof(double) * 8);
	param[0] = G_Radar->Amp*G_Radar->RadomeLoss*(C_ / G_Radar->Fc)*(C_ / G_Radar->Fc) / (64.0 * PI*PI*PI);
	param[1] = pSumAntennaFunction[2420 * 4801 + 2421];
	param[2] = pAziSubAntennaFunction[2420 * 4801 + 2421];
	param[3] = pPitSubAntennaFunction[2420 * 4801 + 2421];
	double Range;

	//cudaEvent_t e_start, e_stop;
	//cudaEventCreate(&e_start);
	//cudaEventCreate(&e_stop);
	//cudaEventRecord(e_start, 0);

	for (idx = 0; idx < G_Radar->Na; idx++)
	{
	cudaMemset(dev_Pf, 0, sizeof(double)*FFTLen);
	memset(ClutterData, 0, sizeof(T)*FFTLen);
	////SpectrunGen(SimData->Fs, SimData->f3dB, SimData->SaveFlie_Nr / 2, SimData->spectrum_way, Pf);
	dim3 ThreadsPerBlock(512, 1);
	dim3 BlockNum((FFTLen + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
	CUDA_SpectrunGen(0, ThreadsPerBlock, BlockNum, pSimData->Fs, pSimData->f3dB, FFTLen / 2, 1.0 / (pSimData->f3dB * FFTLen / 2), pSimData->spectrum_way, dev_Pf);

	Ps_i[1] = RadarPos[idx].SkyPos;//天
	Range = abs(Ps_i[1] / sin(AntParam->PitAngle*PI / 180));//距离

	cudaError_t Err = cudaGetLastError();
	T *dev_ClutterData = (T *)_MallocCUDA(sizeof(T)*FFTLen);
	T *dev_ClutterData_azi = (T *)_MallocCUDA(sizeof(T)*FFTLen);
	T *dev_ClutterData_pit = (T *)_MallocCUDA(sizeof(T)*FFTLen);

	if (1 == pSimData->StatClutterMod)	//仿真K分布海杂波
	{
		KDisClutterGen(FFTLen, dev_Pf, pSimData->mu, pSimData->Shape, pSimData->Scale, pSimData->viu, dev_CosValue, dev_SinValue, Range, param, dev_ClutterData, dev_ClutterData_azi, dev_ClutterData_pit);
	}
	else if (2 == pSimData->StatClutterMod)	//仿真韦伯分布海杂波
	{
		//WeibullClutterGen(SimData->SaveFlie_Nr, dev_Pf, SimData->mu, SimData->Shape, SimData->Scale, dev_ClutterData);
		WeibullClutterGen(FFTLen, dev_Pf, pSimData->mu, pSimData->Shape, pSimData->Scale, Range, param, dev_ClutterData, dev_ClutterData_azi, dev_ClutterData_pit);
	}
	else if (3 == pSimData->StatClutterMod)	//仿真对数正态分布海杂波
	{
		LogNormClutterGen(FFTLen, dev_Pf, pSimData->mu, pSimData->sigma_m, dev_ClutterData);
	}
	else if (4 == pSimData->StatClutterMod)	//瑞丽分布分布海杂波
	{
		RelayClutterGen(FFTLen, dev_Pf, pSimData->mu, Range, param, dev_ClutterData, dev_ClutterData_azi, dev_ClutterData_pit);
	}

	//计算卷积
	T *dev_PosCash;
	T *dev_PosCash_azi;
	T *dev_PosCash_pit;
	T *dev_Signal;
	T *dev_Echo;
	T *dev_Echo_azi;
	T *dev_Echo_pit;
	dev_Signal = (T*)_MallocCUDA(FFTLen * sizeof(T));
	dev_PosCash = (T*)_MallocCUDA(FFTLen * sizeof(T));
	dev_PosCash_azi = (T*)_MallocCUDA(FFTLen * sizeof(T));
	dev_PosCash_pit = (T*)_MallocCUDA(FFTLen * sizeof(T));
	dev_Echo = (T*)_MallocCUDA(FFTLen * sizeof(T));
	dev_Echo_azi = (T*)_MallocCUDA(FFTLen * sizeof(T));
	dev_Echo_pit = (T*)_MallocCUDA(FFTLen * sizeof(T));
	cudaMemset(dev_Signal, 0, sizeof(T)*FFTLen);
	cudaMemset(dev_PosCash, 0, sizeof(T)*FFTLen);
	cudaMemcpy(dev_Signal, BaseSignal, (int)(G_Radar->Fs*G_Radar->Tp + 0.5f)*sizeof(T), cudaMemcpyHostToDevice);
	cudaMemcpy(dev_PosCash, dev_ClutterData, sizeof(T)*FFTLen, cudaMemcpyDeviceToDevice);
	cudaMemcpy(dev_PosCash_azi, dev_ClutterData_azi, sizeof(T)*FFTLen, cudaMemcpyDeviceToDevice);
	cudaMemcpy(dev_PosCash_pit, dev_ClutterData_pit, sizeof(T)*FFTLen, cudaMemcpyDeviceToDevice);

	cufftHandle cufftPlan = CufftPlanCheck(FFTLen);
	_CufftExecC2C(cufftPlan, dev_Signal, dev_Signal, CUFFT_FORWARD);
	cudaError_t cudaStatus = cudaGetLastError();
	if (Err != cudaSuccess){
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err;
	}
	_CufftExecC2C(cufftPlan, dev_PosCash, dev_PosCash, CUFFT_FORWARD);
	_CufftExecC2C(cufftPlan, dev_PosCash_azi, dev_PosCash_azi, CUFFT_FORWARD);
	_CufftExecC2C(cufftPlan, dev_PosCash_pit, dev_PosCash_pit, CUFFT_FORWARD);
	cudaStatus = cudaGetLastError();
	if (Err != cudaSuccess){
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err;
	}

	ThreadsPerBlock.x = 512;
	ThreadsPerBlock.y = 1;
	BlockNum.x = (FFTLen + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x;
	BlockNum.y = 1;
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_Signal, dev_PosCash, dev_Echo, FFTLen);
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_Signal, dev_PosCash_azi, dev_Echo_azi, FFTLen);
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_Signal, dev_PosCash_pit, dev_Echo_pit, FFTLen);

	cudaStatus = cudaGetLastError();
	if (Err != cudaSuccess){
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err;
	}

	cudaStatus = cudaGetLastError();
	_CufftExecC2C(cufftPlan, dev_Echo, dev_Echo, CUFFT_INVERSE);
	_CufftExecC2C(cufftPlan, dev_Echo_azi, dev_Echo_azi, CUFFT_INVERSE);
	_CufftExecC2C(cufftPlan, dev_Echo_pit, dev_Echo_pit, CUFFT_INVERSE);
	cudaStatus = cudaGetLastError();
	if (Err != cudaSuccess){
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err;
	}


	//*************显存拷贝到内存***************//
	cudaMemcpy(ClutterData, dev_Echo, FFTLen*sizeof(T), cudaMemcpyDeviceToHost);
	cudaMemcpy(ClutterData_azi, dev_Echo_azi, FFTLen*sizeof(T), cudaMemcpyDeviceToHost);
	cudaMemcpy(ClutterData_pit, dev_Echo_pit, FFTLen*sizeof(T), cudaMemcpyDeviceToHost);
	QString str21 = QString("..\\..\\..\\data\\ClutterEcho01");
	SaveFile(sizeof(T)*SimData->SaveFlie_Nr, (char*)ClutterData, str21, 0);
	QString str31 = QString("..\\..\\..\\data\\ClutterEcho11");
	SaveFile(sizeof(T)*SimData->SaveFlie_Nr, (char*)ClutterData_azi, str31, 0);
	QString str41 = QString("..\\..\\..\\data\\ClutterEcho21");
	SaveFile(sizeof(T)*SimData->SaveFlie_Nr, (char*)ClutterData_pit, str41, 0);

		for (idy = 0; idy < SimData->SaveFlie_Nr; idy++)
		{
			SumCluDataPolar1[idx*SimData->SaveFlie_Nr + idy].x += ClutterData[idy].x;
			SumCluDataPolar1[idx*SimData->SaveFlie_Nr + idy].y += ClutterData[idy].y;
			ExCluDataPolar1[idx*SimData->SaveFlie_Nr + idy].x += ClutterData[idy].x;
			ExCluDataPolar1[idx*SimData->SaveFlie_Nr + idy].y += ClutterData[idy].y;
		}
		if (SimData->AziEleFalg == 1 || SimData->AziEleFalg == 2)
		{
			for (idy = 0; idy < SimData->SaveFlie_Nr; idy++)
			{
				AziCluDataPolar1[idx*SimData->SaveFlie_Nr + idy].x += ClutterData_azi[idy].x;
				AziCluDataPolar1[idx*SimData->SaveFlie_Nr + idy].y += ClutterData_azi[idy].y;
				EleCluDataPolar1[idx*SimData->SaveFlie_Nr + idy].x += ClutterData_pit[idy].x;
				EleCluDataPolar1[idx*SimData->SaveFlie_Nr + idy].y += ClutterData_pit[idy].y;
			}
		}
	}

	//cudaEventRecord(e_stop, 0);
	//cudaEventSynchronize(e_stop);
	//float elapsedTime = 0;
	//cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
	//qDebug() << "GPU:" << m_DevID << "============  ThreadClassClutter Cost Time is  " << (elapsedTime) << " ms";

	QString str2 = QString("..\\..\\..\\data\\ClutterEcho");
	SaveFile(sizeof(T)*G_Radar->Na*SimData->SaveFlie_Nr, (char*)SumCluDataPolar1, str2, 0);
	QString str3 = QString("..\\..\\..\\data\\ClutterEcho1");
	SaveFile(sizeof(T)*G_Radar->Na*SimData->SaveFlie_Nr, (char*)AziCluDataPolar1, str3, 0);
	QString str4 = QString("..\\..\\..\\data\\ClutterEcho2");
	SaveFile(sizeof(T)*G_Radar->Na*SimData->SaveFlie_Nr, (char*)EleCluDataPolar1, str4, 0);
    
    
    
}
//**********实时模型杂波*********//
template <class T,class T2,class T3,class T4>
void ThreadClassClutter::RealCultter(SimStruct *SimData, RADAR *G_Radar, PLATFORM *RadarPos, ANTPARAM *AntParam,T *RadarAPC, T2 *PRI,T3 *BaseSignal,
                                     T4 *SumCluDataPolar1, T4 *AziCluDataPolar1, T4 *EleCluDataPolar1, T4 *ExCluDataPolar1)
{
    
    T AngleBound[2];	//天线波束范围
	T AngSampSpace[2];		//天线采样步进

	SCENE	*G_Scene	= (SCENE*)_Malloc(sizeof(SCENE));
	CULPLAT *G_Plat		= (CULPLAT*)_Malloc(sizeof(CULPLAT));

	//*************************参数初始化**********************//
	//ParamIni(ParamR,G_Scene,G_Plat,RadarPos);		//初始化参数
	G_Scene->SceneType	= SimData->CultterType;		//海情等级
	G_Radar->Kr			= G_Radar->Br/G_Radar->Tp;			//调频斜率
	
	G_Scene->Na			= G_Radar->Na;
	G_Scene->Nr			= G_Radar->Nr;
	G_Scene->Pix_N		= 10;
	G_Scene->Pix_E		= 10;
	
	G_Plat->MaxRange	= SimData->Rmin + 10e3;			//最大探测斜距
	G_Plat->Pos_North	= RadarPos->NorthPos;
	G_Plat->Pos_Sky		= RadarPos->SkyPos;
	G_Plat->Pos_East	= RadarPos->EastPos;			//初始时刻雷达位置
	G_Plat->V_North		= RadarPos->NorthVel;			//初始时刻雷达速度
	G_Plat->V_Sky		= RadarPos->SkyVel;
	G_Plat->V_East		= RadarPos->EastVel;
	
	G_Plat->A_North		= RadarPos->NorthAcc;
	G_Plat->A_East		= RadarPos->EastAcc;
	G_Plat->A_Sky		= RadarPos->SkyAcc;

	G_Plat->V = sqrt(G_Plat->V_North*G_Plat->V_North + G_Plat->V_East*G_Plat->V_East + G_Plat->V_Sky*G_Plat->V_Sky);//初始速度的模
	G_Plat->A = sqrt(G_Plat->A_North*G_Plat->A_North + G_Plat->A_East*G_Plat->A_East + G_Plat->A_Sky*G_Plat->A_Sky);//加速度的模
	//*************************参数初始化**********************//
	int	CultterDot		= (2 * (G_Plat->MaxRange - SimData->Rmin) / C + G_Radar->Tp)*G_Radar->Fs;//BUFFER_LEN;	//FFT点数
	int CashLen			= CultterDot * 2 + (int)(G_Radar->Fs / G_Radar->Prf*G_Scene->Na + 1) * 2;		//杂波总长度
	T4 *CultterFlow = (T4*)_Malloc(sizeof(T4)*(CashLen));				//申请实时杂波流信号缓存	
	memset(CultterFlow,0,sizeof(Complex)*(CashLen));
	//Complex CultterFlow[1];

	AngleBound[0]	= AntParam->AntAziBound;
	AngleBound[1]	= AntParam->AntEleBound;
	AngSampSpace[0] = AntParam->AntAziBound/(AntParam->AntFuncAziNum - 1);
	AngSampSpace[1] = AntParam->AntEleBound/(AntParam->AntFuncEleNum - 1);
    GenCultter_GPU(SimData,G_Radar,G_Scene,G_Plat,BaseSignal,AngleBound,AngSampSpace,AntParam->G_Matrix,AntParam->SumAntennaFunction,CultterFlow,RadarAPC,PRI);

//	DoulbeArrayToDisk2((double*)CultterFlow,"Cultter2",CashLen);
//	*****************流信号转帧信号输出********************//
//	double *RadarPRI = (double*)malloc(sizeof(double)*ParamR->Na);	//雷达发射信号真实重复周期
//	memset(RadarPRI,0,sizeof(double)*ParamR->Na);
//	GenPRI(ParamR,RadarPRI);		//生成变PRT序列
	double PRTTime = 0;
	int RangeGateNum;		//可变距离门选通中距离门个数
	int GateSampleNum;		//可变距离门内采样点数
	int LastPos;			//可变距离门Region内结束ID位置
	int GateStart;			//第i个距离门开始位置
	RangeGateNum = int(SimData->RangeGateRegion/SimData->RangeGateWidth + 1);
	if(fabs(SimData->RangeGateRegion/SimData->RangeGateWidth - int(SimData->RangeGateRegion/SimData->RangeGateWidth)) < MINEQUERR)
		RangeGateNum = int(SimData->RangeGateRegion/SimData->RangeGateWidth + MINEQUERR);  //当两个double型数刚好可以整除时
	GateSampleNum = int(SimData->RangeGateWidth*G_Radar->Fs + 0.5);   //即可变距离门选通的SimData->SaveFlie_Nr
	LastPos = int(SimData->RangeGateRegion*G_Radar->Fs + 0.5);
	int idm = 1;		//距离门计数
	int StartID;		//每帧信号起始ID

	if(SimData->RangeGateMode == 0)		//固定距离门选通
	{
        for (int idn = 0; idn < SimData->SaveFlie_Na; idn++)
        {
            //int StartID = (int)(int((2*SimData->Rmin)*(ParamR->Fs/C)+0.5)%ParamR->Nr);					//帧信号开始ID
            StartID = int((2 * SimData->Rmin / C + PRTTime)*G_Radar->Fs + 0.5);					//帧信号开始ID
            if (StartID >= 0)    //默认不跨重
            {
                for (int idz = 0; idz < SimData->SaveFlie_Nr; idz++)
                {
                    (SumCluDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->x += (CultterFlow + StartID + idz)->x;
                    (SumCluDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->y += (CultterFlow + StartID + idz)->y;
                    (ExCluDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->x += (CultterFlow + StartID + idz)->x;
                    (ExCluDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->y += (CultterFlow + StartID + idz)->y;
                }
                if (SimData->AziEleFalg == 1 || SimData->AziEleFalg == 2)
                {
                    for (int idz = 0; idz < SimData->SaveFlie_Nr; idz++)
                    {
                        (AziCluDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->x += (CultterFlow + StartID + idz)->x;
                        (AziCluDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->y += (CultterFlow + StartID + idz)->y;
                        (EleCluDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->x += (CultterFlow + StartID + idz)->x;
                        (EleCluDataPolar1 + idn*SimData->SaveFlie_Nr + idz)->y += (CultterFlow + StartID + idz)->y;
                    }
                }
            }
            PRTTime = PRTTime + *(PRI + idn);
        }
	}
	else if(SimData->RangeGateMode == 1)	//可变距离门选通
	{
//		for(int idn = 0;idn < SimData->SaveFlie_Na;idn++)
//		{
//			if(idn == 0)
//				idm = 1;
//			else if(idn != 0 && idn%SimData->SarchNum == 0 && idm < RangeGateNum )
//				idm = idm + 1;		//下一个距离门
//			else if(idn != 0 && idn%SimData->SarchNum == 0 && idm == RangeGateNum)
//				idm = 1;	

//			if(idm<RangeGateNum)
//				GateStart = (idm-1)*GateSampleNum;
//			if(idm==RangeGateNum)
//				GateStart = LastPos - GateSampleNum +1;

//			StartID = int((2*SimData->Rmin/C + PRTTime)*G_Radar->Fs + GateStart + 0.5);					//帧信号开始ID
//			if (StartID >= 0)    //默认不跨重
//			{
//				for(int idz = 0;idz<SimData->SaveFlie_Nr;idz++)
//				{
//					(Cultter + idn*SimData->SaveFlie_Nr +idz)->x += (CultterFlow + StartID + idz)->x;
//					(Cultter + idn*SimData->SaveFlie_Nr +idz)->y += (CultterFlow + StartID + idz)->y;
//				}
//				if(SimData->AziEleFalg == 1 || SimData->AziEleFalg == 2)
//				{
//					for(int idz = 0;idz<SimData->SaveFlie_Nr;idz++)
//					{
//						(AziCultter + idn*SimData->SaveFlie_Nr +idz)->x += (CultterFlow + StartID + idz)->x;
//						(AziCultter + idn*SimData->SaveFlie_Nr +idz)->y += (CultterFlow + StartID + idz)->y;
//						(EleCultter + idn*SimData->SaveFlie_Nr +idz)->x += (CultterFlow + StartID + idz)->x;
//						(EleCultter + idn*SimData->SaveFlie_Nr +idz)->y += (CultterFlow + StartID + idz)->y;
//					}
//				}
//			}
//			PRTTime = PRTTime + *(PRI +idn);
//		}
	}
	//*****************End of:流信号转帧信号输出********************//
}
//生成杂波主程序--实时
template <class T, class T2, class T3, class T4, class T5, class T6>
void ThreadClassClutter::GenCultter_GPU(SimStruct *SimData,RADAR *G_Radar,SCENE *G_Scene,CULPLAT *G_Plat,T *BaseSignal,T2 AngleBound[2],
	T2 AngSampSpace[2], T3 *G_Matrix, T4 *AntennaPattern, T5 *Cultter, T6 *RadarAPC, T6 *PRI)
{
	cudaEvent_t e_start, e_stop;
	cudaEventCreate(&e_start);
	cudaEventCreate(&e_stop);
	cudaEventRecord(e_start, 0);

    double APC[3];			//雷达位置
	double APCMid[3];		//雷达中心时刻位置
	double Velocity[3];		//雷达速度
	double CurTime;			//当前慢时刻
	int StartID;			//各慢时刻对应的起始ID
	int	CultterDot	= (2 * (G_Plat->MaxRange - SimData->Rmin) / C + G_Radar->Tp)*G_Radar->Fs;//BUFFER_LEN;//FFT点数
	int CashLen		= CultterDot * 2 + (int)(G_Radar->Fs / G_Radar->Prf*G_Scene->Na + 1) * 2;	//杂波总长度
	//Complex	*Cultter = (Complex*)malloc(sizeof(Complex)*(CashLen));			//杂波缓存	
	//Cultter = (Complex*)malloc(sizeof(Complex)*(CashLen));					//杂波缓存	
	//memset(Cultter,0,sizeof(Complex)*(CashLen));

	double PRTTime = 0;
	APCMid[0] = RadarAPC[int(G_Radar->Na/2)*3];
	APCMid[1] = RadarAPC[int(G_Radar->Na/2)*3 + 1];
	APCMid[2] = RadarAPC[int(G_Radar->Na/2)*3 + 2];
	for(int idx=0;idx<G_Scene->Na;idx++)			//慢时刻
	{
		//**********更新雷达速度、位置，更新回波缓存起始ID********//
		CurTime		= (idx-G_Scene->Na/2)/G_Radar->Prf;
		APC[0]		= RadarAPC[idx*3] - APCMid[0];		//移动坐标系到海杂波坐标系，海杂波坐标系原点为中心时刻雷达星下点
		APC[1]		= RadarAPC[idx*3+1] ;
		APC[2]		= RadarAPC[idx*3+2] - APCMid[2];
		Velocity[0] = G_Plat->V_North + G_Plat->A_North*CurTime;
		Velocity[1] = G_Plat->V_Sky   + G_Plat->A_Sky*CurTime;
		Velocity[2] = G_Plat->V_East  + G_Plat->A_East*CurTime;
		StartID		= (int)(PRTTime*G_Radar->Fs);
		//**********更新雷达速度、位置，更新回波缓存起始ID********//
		_Release_MallocCUDA();	//初始化GPU伪内存池
		RangeAngle(SimData,G_Radar,G_Scene,G_Plat,BaseSignal,AngleBound,AngSampSpace,StartID,APC,Velocity,Cultter,plan,G_Matrix,AntennaPattern);	//生成回波
		PRTTime = PRTTime + *(PRI +idx);
	}

	cudaEventRecord(e_stop, 0);
	cudaEventSynchronize(e_stop);
	float elapsedTime = 0;
	cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
	qDebug() << "============  ThreadClassClutter Total Cost Time is  " << (elapsedTime) << " ms";
	//char *pppp = (char*)_Malloc(FFTLen*sizeof(Complex));
	//QFile *pQFile = new QFile;
	//pQFile->setFileName("E:\\YN\\yn_0521\\PR2301_063\\5_from_yening\\tte.bin");
	//pQFile->open(QFile::WriteOnly);
	////cudaMemcpy(pppp, dev_Echo, sizeof(Complex)*FFTLen, cudaMemcpyDeviceToHost);
	//pQFile->write((char*)Cultter, sizeof(Complex)*PRTTime*G_Radar->Fs*G_Scene->Na);
	//pQFile->close();
	//delete pQFile;
}
template <class T>
void _CufftExecC2C(cufftHandle plan, T *idata, T *odata, int direction)
{
	if (sizeof(T) > 8){
		cufftExecZ2Z(plan, (cuDoubleComplex*)idata, (cuDoubleComplex*)odata, direction);
	}
	else{
		cufftExecC2C(plan, (cufftComplex*)idata, (cufftComplex*)odata, direction);
	}
}
//生成杂波--实时
template <class T, class T2, class T3, class T4, class T5, class T6>
void ThreadClassClutter::RangeAngle(SimStruct *SimData, RADAR *G_Radar, SCENE *G_Scene, CULPLAT *G_Plat, T *BaseSignal, T2 AngleBound[2], T2 AngSampSpace[2],
									int StartID, T3 APC[3], T3 V[3], T4 *Cultter, cufftHandle plan, T5 *G_Matrix, T6 *AntennaPattern)
{
	cudaEvent_t e_start, e_stop;
	cudaEventCreate(&e_start);
	cudaEventCreate(&e_stop);
	cudaEventRecord(e_start, 0);

    int idm,idx,idz;
    cudaError_t cudaStatus;														//GPU状态
	int		CultterDot			= (2 * (G_Plat->MaxRange - SimData->Rmin) / C + G_Radar->Tp)*G_Radar->Fs;	//杂波长度增加了最小作用距离 20240425
	int		FFTLen = 16384;// 32768;
    int		AzimuGridNum	= (int)(AngleBound[0]/AngSampSpace[0]+0.5)+1;		//方位向点数(int)(2*AngleBound[0]/AngSampSpace[0]+0.5)+1
    int		PitchGridNum	= (int)(AngleBound[1]/AngSampSpace[1]+0.5)+1;		//俯仰向点数(int)(2*AngleBound[1]/AngSampSpace[1]+0.5)+1
    //double	*PosSigRe		= (double*)_Malloc(sizeof(double)*FFTLen);
    //double	*PosSigIm		= (double*)_Malloc(sizeof(double)*FFTLen);
    int		TrLen			= (int)(G_Radar->Tp*G_Radar->Fs+0.5);
    double Pha;
    //memset(PosSigRe,0,FFTLen*sizeof(double));
    //memset(PosSigIm,0,FFTLen*sizeof(double));
    
    //
    double  AlphaV = atan((V[2])/(V[0]));              	//方位角
    if( 0 == V[0] )
    {
        if(V[2] > 0)
            AlphaV = PI/2;
        else
            AlphaV = -PI/2;
    }
    if(V[0] < 0)   //向后飞
        AlphaV = PI+AlphaV;
	double	*Param		= (double*)_Malloc(64 * sizeof(double));
	int		*ElseParam	= (int*)_Malloc(10 * sizeof(int));
    double	Lambda		= C/G_Radar->Fc;				//波长
    Param[0] = G_Radar->Prf;							//PRF
	Param[1] = SimData->Rmin;							//最小斜距
    Param[2] = 1.0/(2*PI);								//1.0/(2*PI)  原值 G_Plat->MaxRange
    //Param[3] = C/G_Radar->Br/2;						//斜距间隔 C/2B
    //Param[3] = G_Radar->Tp/2*C;						//斜距间隔 C*(Tp/2)
    Param[3] = 5;										//斜距间隔 固定值 会影响等距离等多普勒划分的面积
    Param[4] = APC[1];									//雷达距地面高度，同时也是等距离环的Rmin
    Param[5] = sqrt(V[0]*V[0] + V[1]*V[1] + V[2]*V[2]);	//合速度
    Param[6] = -asin(V[1]/Param[5]);					//速度下倾角（水平面与速度矢量的夹角，向下为正）
    Param[7] = G_Radar->Prf/G_Scene->Na;				//频率间隔(从-PRF到+PRF)
    Param[8] = -2.0/Lambda;								//三角函数相位系数
    Param[9] = G_Scene->Pix_N;							//北向分辨率
    Param[10] = G_Scene->Pix_E;							//东向分辨率
    Param[11] = 2*Param[5]/Lambda;						//最大多普勒频率
	Param[12] = 1.0/G_Scene->Pix_N; 					//北向分辨率倒数
    Param[13] = APC[0]; 								//雷达坐标，北
    Param[14] = APC[1]; 								//雷达坐标，天
    Param[15] = APC[2]; 								//雷达坐标，东
    Param[16] = G_Radar->Fs; 							//采样率
	Param[17] = 1.f / AngSampSpace[0]; 					//方位向天线间隔（单位 弧度）
	Param[18] = 1.f / AngSampSpace[1]; 					//俯仰向天线间隔（单位 弧度）

    
    //double RanAngleSpace = Param[0]*Lambda/(2*G_Scene->Na*Param[5]);     // 每份方位角多少弧度(PRF*Lambda/(2*Na*V))
    // ThetaNum = (int)(2*PI/RanAngleSpace);			//角度分成多少份
    double RanAngleSpace = 1.0/180*PI;     // 每份方位角多少弧度1/PI*180.0;
    int ThetaNum = 359;			//角度分成多少份
    //int ThetaNum = (int)(2*PI/RanAngleSpace);			//角度分成多少份
    Param[19] = RanAngleSpace; 							//每份方位角多少弧度
    Param[20] = 0;										//等方位角起始角度
	Param[21] = G_Scene->Pix_E;							//东向分辨率倒数
    Param[22] = G_Radar->RadomeLoss;					//天线罩损耗

	ElseParam[0] = (int)(G_Plat->MaxRange / Param[3] + 0.5f);	//按最大距离划分等距离环
    ElseParam[1] = ThetaNum;									//多普勒单元数目
    ElseParam[2] = SimData->AziEleFalg;							//0则单波束，1则和差差波束
    ElseParam[3] = SimData->TransLossFlag;						//传输损耗计算标志
    ElseParam[4] = G_Scene->SceneType;							//场景类型（海清等级）
    ElseParam[5] = AzimuGridNum;								//天线方位向点数
    ElseParam[6] = PitchGridNum;								//天线俯仰向点数
	ElseParam[7] = CultterDot;

	//提前计算核函数参数
	float A			= 4e-7*powf(10, 0.6f*(G_Scene->SceneType + 1));
	float B			= PI / 2;
	float Beta0		= 2.44f*powf(G_Scene->SceneType + 1, 1.08f) / 57.29f;
	float He		= 0.025f + 0.046f*powf(G_Scene->SceneType, 1.72f);
	float ThetaC	= asin(Lambda / (4 * PI*He));
	float Sigma0Coef = A/ Lambda + sqrt(G_Radar->Fc) / 4.7f / tan(Beta0) / tan(Beta0);		//平均Sigma0

	Param[23] = A;			//A
	Param[24] = B;			//B
	Param[25] = Beta0;		//Beta0
	Param[26] = He;			//He
	Param[27] = ThetaC;		//ThetaC 
	Param[28] = 1.0/(tan(Beta0)*tan(Beta0));	//tan(Beta0)*tan(Beta0)
	Param[29] = 2.0*G_Radar->Fs / C;				// idCoef = (int)(2 * R1 / C*Fs + 0.5);
	Param[30] = G_Radar->Amp*Lambda*Lambda / (64 * PI*PI*PI);	// Loss 损耗（R^4添加进了matrix因子中）
	Param[31] = Sigma0Coef;
    
    //**************GPU变量初始化***************//
    dim3 ThreadsPerBlock(16,16);
    dim3 BlockNum((ElseParam[0] + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,(ElseParam[1] + ThreadsPerBlock.y - 1)/ThreadsPerBlock.y);
	T *EhcoCache = (T*)_Malloc(sizeof(T)*CultterDot);	//杂波的回波缓存（GPU上）

    double	*dev_Param;
    int		*dev_ElseParam;
    T6	*dev_Antenna;
    double	*dev_AntMatrix;
    double	*dev_CMatrix;
    T *dev_PosCash;
    T *dev_Signal;
    T *dev_Echo;
    dev_CMatrix		= (double*)_MallocCUDA(ElseParam[0]*ElseParam[1] * sizeof(double));
    dev_Param		= (double*)_MallocCUDA(32 * sizeof(double));
    dev_ElseParam	= (int*	  )_MallocCUDA(10 * sizeof(int));
	dev_Antenna		= (T6*)_MallocCUDA(AzimuGridNum*PitchGridNum * sizeof(T6));
    dev_AntMatrix	= (T5*)_MallocCUDA(9 * sizeof(T5));
    dev_Signal		= (T*)_MallocCUDA(FFTLen * sizeof(T));
    dev_PosCash		= (T*)_MallocCUDA(FFTLen * sizeof(T));
    dev_Echo		= (T*)_MallocCUDA(FFTLen * sizeof(T));
    
	cudaMemset(dev_Signal, 0, sizeof(T)*FFTLen);

    cudaMemcpy(dev_ElseParam,	ElseParam,		10*sizeof(int),					cudaMemcpyHostToDevice);
    cudaMemcpy(dev_Param,		Param,			32*sizeof(double),				cudaMemcpyHostToDevice);
	cudaMemcpy(dev_Antenna, AntennaPattern, AzimuGridNum*PitchGridNum*sizeof(T6), cudaMemcpyHostToDevice);
    cudaMemcpy(dev_AntMatrix,	G_Matrix,		9*sizeof(T5),				cudaMemcpyHostToDevice);
	cudaMemcpy(dev_Signal, BaseSignal,			(int)(G_Radar->Fs*G_Radar->Tp + 0.5f)*sizeof(T), cudaMemcpyHostToDevice);
	cudaStatus = cudaGetLastError();
	if (cudaStatus != cudaSuccess) {
		fprintf(stderr, "failed: %s\n", cudaGetErrorString(cudaStatus));
	}
    cudaMemset(dev_CMatrix,0,sizeof(double)*ElseParam[0]*ElseParam[1]);	
    cudaMemset(dev_PosCash,0,sizeof(T)*FFTLen);
    //**************GPU变量初始化***************//

    cudaStatus = cudaGetLastError();
    if (cudaStatus != cudaSuccess) {
        fprintf(stderr, "failed: %s\n", cudaGetErrorString(cudaStatus));
    }
    //double *CMatrix = (double *)malloc(sizeof(double)*FFTLen);
    float *dev_Random_Phase_ALL;
    //cudaMalloc((void**)&dev_Random_Phase_ALL,ElseParam[0]*ElseParam[1]*4*sizeof(float));
    dev_Random_Phase_ALL = (float*)_MallocCUDA(ElseParam[0]*ElseParam[1]*4*sizeof(float));

#if EXP_RAND_ON
    //***********生成随机相位和均匀分布RCS***********//
    curandGenerator_t gen;
    cudaMemset(dev_Random_Phase_ALL,0,4*sizeof(float)*ElseParam[0]*ElseParam[1]);	
    curandCreateGenerator(&gen,CURAND_RNG_PSEUDO_MRG32K3A);	
    curandSetPseudoRandomGeneratorSeed(gen,(time(NULL) + rand()));	
    curandGenerateUniform(gen,dev_Random_Phase_ALL,ElseParam[0]*ElseParam[1]*4);
    curandDestroyGenerator(gen);
    //***********生成随机相位和均匀分布RCS***********//
    cudaStatus = cudaGetLastError();
    if (cudaStatus != cudaSuccess) {
        fprintf(stderr, "failed: %s\n", cudaGetErrorString(cudaStatus));
    }
#else
    cudaMemset(dev_Random_Phase_ALL,0,sizeof(float)*4*ElseParam[0]*ElseParam[1]);
#endif
	
	if (cudaStatus != cudaSuccess) {
		fprintf(stderr, "failed: %s\n", cudaGetErrorString(cudaStatus));
	}
	CUDA_RangeAngle(0, ThreadsPerBlock, BlockNum, dev_Param, dev_ElseParam, dev_Antenna, dev_AntMatrix, dev_CosValue, dev_SinValue, dev_PosCash, dev_Random_Phase_ALL);
	cudaStatus = cudaGetLastError();
	if (cudaStatus != cudaSuccess) {
		fprintf(stderr, "failed: %s\n", cudaGetErrorString(cudaStatus));
	}
    
    //*************发射信号与RCS卷积***************//
	_CufftExecC2C(plan, dev_Signal, dev_Signal, CUFFT_FORWARD);
	cudaStatus = cudaGetLastError();
	if (cudaStatus != cudaSuccess) {
		fprintf(stderr, "failed: %s\n", cudaGetErrorString(cudaStatus));
	}
	_CufftExecC2C(plan, dev_PosCash, dev_PosCash, CUFFT_FORWARD);
	cudaStatus = cudaGetLastError();
	if (cudaStatus != cudaSuccess) {
		fprintf(stderr, "failed: %s\n", cudaGetErrorString(cudaStatus));
	}
    
    ThreadsPerBlock.x = 512;
    ThreadsPerBlock.y = 1;
    BlockNum.x = (FFTLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x;
    BlockNum.y = 1;
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_Signal, dev_PosCash, dev_Echo, 1);
	cudaStatus = cudaGetLastError();
	if (cudaStatus != cudaSuccess) {
		fprintf(stderr, "failed: %s\n", cudaGetErrorString(cudaStatus));
	}

	cudaStatus = cudaGetLastError();
	_CufftExecC2C(plan, dev_Echo, dev_Echo, CUFFT_INVERSE);
	cudaStatus = cudaGetLastError();
	if (cudaStatus != cudaSuccess) {
		fprintf(stderr, "failed: %s\n", cudaGetErrorString(cudaStatus));
	}
    //*************显存拷贝到内存***************//
    cudaMemcpy(EhcoCache,dev_Echo, FFTLen*sizeof(T), cudaMemcpyDeviceToHost);

	cudaEventRecord(e_stop, 0);
	cudaEventSynchronize(e_stop);
	float elapsedTime = 0, elapsedTime2 = 0;
	cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
	//qDebug() << "============ ThreadClassClutter CUDA Cost Time is  " << (elapsedTime2) << " ms";
	qDebug() << "============  ThreadClassClutter Cost Time is  " << (elapsedTime) << " ms";

	//char *pppp = (char*)_Malloc(FFTLen*sizeof(Complex));
	//QFile *pQFile = new QFile;
	//pQFile->setFileName("E:\\YN\\yn_0521\\PR2301_063\\5_from_yening\\tte.bin");
	//pQFile->open(QFile::WriteOnly);
	//cudaMemcpy(pppp, dev_Echo, sizeof(T)*FFTLen, cudaMemcpyDeviceToHost);
	//pQFile->write((char*)pppp, sizeof(T)*FFTLen);
	//pQFile->close();
	//delete pQFile;


    double RcsSquare = 1*(C/2/G_Radar->Br)*(C/2/G_Radar->Br);		//衰减因子
    //double RcsSquare = 1;		//衰减因子
	for (int idz = 0; idz<CultterDot; idz++)
    {
        Cultter[idz + StartID].x	+= EhcoCache[idz].x*RcsSquare;
        Cultter[idz + StartID].y	+= EhcoCache[idz].y*RcsSquare;
    }
    //*************显存拷贝到内存***************//

}
//杂波生成槽函数
void ThreadClassClutter::slotClutterGen(void *p)
{
	_Release_Malloc();		//初始化CPU伪内存池

    stClutterEchoPara *pStClutterEchoPara = (stClutterEchoPara*)p;
	RADAR *ParamR		= (RADAR*)&pStClutterEchoPara->ParamR;
	ANTPARAM *AntParam	= (ANTPARAM*)&pStClutterEchoPara->AntParam;
	SimStruct *SimData	= (SimStruct*)&pStClutterEchoPara->SimData;
	PLATFORM *RadarPos	= (PLATFORM*)&pStClutterEchoPara->RadarPos;		//雷达位置   弹头最多8个
    double *RadarAPC    = pStClutterEchoPara->RadarAPC;
    double *PRI         = pStClutterEchoPara->PRI;
    
    //发射信号生成
	Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) * ParamR->Nr);
	memset(BaseSignal, 0, sizeof(Complexf) * ParamR->Nr);
	int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数
	ThreadClassSendWaveGen::Instance()->TransSignalSim(ParamR, SimData, TrNum, BaseSignal);

    stEchoData *pStEchoData		= _MallocMemObj(stEchoData);
	Complexf *SumCluDataPolar1	= pStEchoData->SumEchoDataPolar1;
	Complexf *AziCluDataPolar1	= pStEchoData->AziEchoDataPolar1;
	Complexf *EleCluDataPolar1	= pStEchoData->EleEchoDataPolar1;
	Complexf *ExCluDataPolar1	= pStEchoData->ExEchoDataPolar1;
    //杂波生成
	int flag = 0;	//flag=0统计型杂波，=1实时型杂波
	ClutterGen(SimData, ParamR, RadarPos, AntParam, RadarAPC, PRI, flag, BaseSignal,
		SumCluDataPolar1, AziCluDataPolar1, EleCluDataPolar1, ExCluDataPolar1);

	pStEchoData->SumEchoData1Dot = SimData->SaveFlie_Nr;
	pStEchoData->ExEchoData1Dot = 0;
	if (SimData->AziEleFalg > 0){
		pStEchoData->AziEchoData1Dot = SimData->SaveFlie_Nr;
		pStEchoData->EleEchoData1Dot = SimData->SaveFlie_Nr;
	}
	pStEchoData->Nr_Save = SimData->SaveFlie_Nr;
	pStEchoData->Na_Save = ParamR->Na;
	//生成的回波发送到回波整合线程进行数据格式转换
	emit sendCluDataFormat(pStEchoData);

	if (pStClutterEchoPara->RadarAPC)_ReleaseMemObj(double, pStClutterEchoPara->RadarAPC);
	if (pStClutterEchoPara->SenceTarget)_ReleaseMemObj(double, pStClutterEchoPara->SenceTarget);
	if (pStClutterEchoPara->PRI)_ReleaseMemObj(double, pStClutterEchoPara->PRI);
	_ReleaseMemObj(stClutterEchoPara, pStClutterEchoPara);
}

//杂波生成
void ThreadClassClutter::GenClutter(void *p, stEchoData *pStEchoData)
{
	_Release_Malloc();		//初始化CPU伪内存池

	stClutterEchoPara *pStClutterEchoPara = (stClutterEchoPara*)p;
	RADAR *ParamR = (RADAR*)&pStClutterEchoPara->ParamR;
	ANTPARAM *AntParam = (ANTPARAM*)&pStClutterEchoPara->AntParam;
	SimStruct *SimData = (SimStruct*)&pStClutterEchoPara->SimData;
	PLATFORM *RadarPos = (PLATFORM*)&pStClutterEchoPara->RadarPos;		//雷达位置   弹头最多8个
	double *RadarAPC = pStClutterEchoPara->RadarAPC;
	double *PRI = pStClutterEchoPara->PRI;

	//发射信号生成
	Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) * ParamR->Nr);
	memset(BaseSignal, 0, sizeof(Complexf) * ParamR->Nr);
	int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数
	ThreadClassSendWaveGen::Instance()->TransSignalSim(ParamR, SimData, TrNum, BaseSignal);
	
	QString str5 = QString("..\\..\\..\\data\\BaseSignal");
	SaveFile(sizeof(Complexf) * ParamR->Nr, (char*)BaseSignal, str5, 0);

	Complexf *SumCluDataPolar1 = pStEchoData->SumEchoDataPolar1;
	Complexf *AziCluDataPolar1 = pStEchoData->AziEchoDataPolar1;
	Complexf *EleCluDataPolar1 = pStEchoData->EleEchoDataPolar1;
	Complexf *ExCluDataPolar1 = pStEchoData->ExEchoDataPolar1;
	//杂波生成
	int flag = 0;	//flag=0统计型杂波，=1实时型杂波
	ClutterGen(SimData, ParamR, RadarPos, AntParam, RadarAPC, PRI, flag, BaseSignal,
		SumCluDataPolar1, AziCluDataPolar1, EleCluDataPolar1, ExCluDataPolar1);

	pStEchoData->SumEchoData1Dot = SimData->SaveFlie_Nr;
	pStEchoData->ExEchoData1Dot = 0;
	if (SimData->AziEleFalg > 0){
		pStEchoData->AziEchoData1Dot = SimData->SaveFlie_Nr;
		pStEchoData->EleEchoData1Dot = SimData->SaveFlie_Nr;
	}
	pStEchoData->Nr_Save = SimData->SaveFlie_Nr;
	pStEchoData->Na_Save = ParamR->Na;
	//生成的回波发送到回波整合线程进行数据格式转换
	//emit sendCluDataFormat(pStEchoData);

	if (m_DevID == 0)
	{
		QFile *pQFile = new QFile;
		pQFile->setFileName("..\\..\\..\\data\\EchoDMC1");
		pQFile->open(QFile::WriteOnly);
		for (int i = 0; i < pStEchoData->Na_Save; i++)
		{
			pQFile->write((char*)&SumCluDataPolar1[i * pStEchoData->Nr_Save], sizeof(Complexf) * pStEchoData->Nr_Save);
			if (SimData->AziEleFalg > 0){
				pQFile->write((char*)&AziCluDataPolar1[i * pStEchoData->Nr_Save], sizeof(Complexf) * pStEchoData->Nr_Save);
				pQFile->write((char*)&EleCluDataPolar1[i * pStEchoData->Nr_Save], sizeof(Complexf) * pStEchoData->Nr_Save);
			}
		}
		pQFile->close();
		delete pQFile;
	}

	if (pStClutterEchoPara->RadarAPC)_ReleaseMemObj(double, pStClutterEchoPara->RadarAPC);
	if (pStClutterEchoPara->SenceTarget)_ReleaseMemObj(double, pStClutterEchoPara->SenceTarget);
	if (pStClutterEchoPara->PRI)_ReleaseMemObj(double, pStClutterEchoPara->PRI);
	_ReleaseMemObj(stClutterEchoPara, pStClutterEchoPara);
}

void ThreadClassClutter::slotSettingClutterSence(void *p,void *pc)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	ANTPARAM        *AntParam = (ANTPARAM*)p;
	SimStruct       *SimData = (SimStruct*)pc;
	//***********天线加权gpu***********//
	cudaMemcpy(dev_SumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
	cudaMemcpy(dev_AziSubAntennaFunction, AntParam->AziSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
	cudaMemcpy(dev_PitSubAntennaFunction, AntParam->PitSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
	cudaMemcpy(pSumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToHost);
	cudaMemcpy(pAziSubAntennaFunction, AntParam->AziSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToHost);
	cudaMemcpy(pPitSubAntennaFunction, AntParam->PitSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToHost);
	cudaMemcpy(pSimData, SimData, sizeof(SimStruct), cudaMemcpyHostToHost);
}


