﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A291A69C-4785-3CE5-8AEF-E7EF0C799804}</ProjectGuid>
    <RootNamespace>EchoInterfeCalcutePool</RootNamespace>
    <Keyword>Qt4VSv1.0</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <PlatformToolset>v140</PlatformToolset>
    <OutputDirectory>..\bin\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>Application</ConfigurationType>
    <IntermediateDirectory>release\</IntermediateDirectory>
    <PrimaryOutput>EchoInterfeCalcutePool</PrimaryOutput>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <PlatformToolset>v140</PlatformToolset>
    <OutputDirectory>..\bin\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>Application</ConfigurationType>
    <IntermediateDirectory>debug\</IntermediateDirectory>
    <PrimaryOutput>EchoInterfeCalcutePool</PrimaryOutput>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">EchoInterfeCalcutePool</TargetName>
    <IgnoreImportLibrary Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</IgnoreImportLibrary>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EchoInterfeCalcutePool</TargetName>
    <IgnoreImportLibrary Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</IgnoreImportLibrary>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>.;..\inc;../../../inc;../../../inc/GLWidget;../../../cuda/inc;../../../inc/MemPool;..\inc\Thread;..\inc\Class;..\inc\public;../inc/cuda;..\inc\GLWidget;../../../inc/Extern;C:\cuda\v10.0\include;C:\cuda\v10.0\common\inc;C:/cuda/v10.0;..\..\..\..\Dadar;..\..\EchoInterfeCalcutePool_Com;$(QTDIR)\include;$(QTDIR)\include\QtOpenGL;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtCore;release;.;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>release\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>None</DebugInformationFormat>
      <DisableSpecificWarnings>4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <Optimization>MaxSpeed</Optimization>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;WIN32;WIN64;QT_DEPRECATED_WARNINGS;_STUMEMPOOL_LIBRARY;_GLWidgetBase_LIBRARY;_ECHO_KERNEL_LIBRARY;QT_NO_DEBUG;QT_OPENGL_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
    <Link>
      <AdditionalDependencies>$(QTDIR)\lib\qtmain.lib;shell32.lib;opengl32.lib;cudart.lib;cublas.lib;cufft.lib;curand.lib;_stumempool.lib;GLWidgetBase.lib;ShareMemory.lib;$(QTDIR)\lib\Qt5OpenGL.lib;$(QTDIR)\lib\Qt5Widgets.lib;$(QTDIR)\lib\Qt5Gui.lib;$(QTDIR)\lib\Qt5Core.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(QTDIR)\lib;C:\utils\my_sql\my_sql\lib;C:\utils\postgresql\pgsql\lib;$(QTDIR)\lib;C:\cuda\v10.0\lib\x64;C:\CUDA\v10.0\lib\x64;D:\zhuhongjun\????\Dadar\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'" %(AdditionalOptions)</AdditionalOptions>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreImportLibrary>true</IgnoreImportLibrary>
      <LinkIncremental>false</LinkIncremental>
      <OutputFile>$(OutDir)\EchoInterfeCalcutePool.exe</OutputFile>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <SubSystem>Windows</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Link>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;WIN32;WIN64;QT_DEPRECATED_WARNINGS;_STUMEMPOOL_LIBRARY;_GLWidgetBase_LIBRARY;_ECHO_KERNEL_LIBRARY;QT_NO_DEBUG;QT_OPENGL_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>.;..\inc;../../../inc;../../../inc/GLWidget;../../../cuda/inc;../../../inc/MemPool;..\inc\Thread;..\inc\Class;..\inc\public;../inc/cuda;..\inc\GLWidget;../../../inc/Extern;C:\cuda\v10.0\include;C:\cuda\v10.0\common\inc;C:/cuda/v10.0;..\..\..\..\Dadar;..\..\EchoInterfeCalcutePool_Com;$(QTDIR)\include;$(QTDIR)\include\QtOpenGL;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtCore;debug;.;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>debug\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;WIN32;WIN64;QT_DEPRECATED_WARNINGS;_STUMEMPOOL_LIBRARY;_GLWidgetBase_LIBRARY;_ECHO_KERNEL_LIBRARY;QT_OPENGL_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <WarningLevel>Level3</WarningLevel>
      <ProgramDataBaseFileName>$(IntDir)vc$(PlatformToolsetVersion).pdb</ProgramDataBaseFileName>
    </ClCompile>
    <Link>
      <AdditionalDependencies>$(QTDIR)\lib\qtmaind.lib;shell32.lib;opengl32.lib;cudart.lib;cublas.lib;cufft.lib;curand.lib;_stumempoold.lib;GLWidgetBased.lib;ShareMemoryd.lib;$(QTDIR)\lib\Qt5OpenGLd.lib;$(QTDIR)\lib\Qt5Widgetsd.lib;$(QTDIR)\lib\Qt5Guid.lib;$(QTDIR)\lib\Qt5Cored.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(QTDIR)\lib;C:\utils\my_sql\my_sql\lib;C:\utils\postgresql\pgsql\lib;$(QTDIR)\lib;C:\cuda\v10.0\lib\x64;C:\CUDA\v10.0\lib\x64;D:\zhuhongjun\????\Dadar\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'" %(AdditionalOptions)</AdditionalOptions>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreImportLibrary>true</IgnoreImportLibrary>
      <OutputFile>$(OutDir)\EchoInterfeCalcutePool.exe</OutputFile>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <SubSystem>Windows</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Link>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;WIN32;WIN64;QT_DEPRECATED_WARNINGS;_STUMEMPOOL_LIBRARY;_GLWidgetBase_LIBRARY;_ECHO_KERNEL_LIBRARY;QT_OPENGL_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\src\Class\ClassClutterRcsRead.cpp" />
    <ClCompile Include="..\src\Class\ClassCoorDinate.cpp" />
    <ClCompile Include="..\src\Class\ClassEchoWaveFd.cpp" />
    <ClCompile Include="..\src\Class\ClassRcsRead.cpp" />
    <ClCompile Include="..\src\GLWidget\GLWidgetInstanceBWFreq.cpp" />
    <ClCompile Include="..\src\GLWidget\GLWidgetWave.cpp" />
    <ClCompile Include="..\src\MainWindow.cpp" />
    <ClCompile Include="..\..\..\inc\MemPool\MyMalloc.cpp" />
    <ClCompile Include="..\..\..\inc\MemPool\MyMallocCUDA.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassAntennaGen.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassBase.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassBlanketJamGen.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassChaffEcho.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassClutter.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassDeceptionJamGen.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassGPUCalcute.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassGenJAM.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassJFEcho.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassSarEcho.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassSarEchoClutter.cpp" />
    <ClCompile Include="..\src\Thread\ThreadClassSendWaveGen.cpp" />
    <ClCompile Include="..\src\Thread\ThreadEchoDataFormat.cpp" />
    <ClCompile Include="..\src\Thread\ThreadEchoDataSave.cpp" />
    <ClCompile Include="..\src\Thread\ThreadResourceScheduling.cpp" />
    <ClCompile Include="..\src\Thread\ThreadSocket.cpp" />
    <ClCompile Include="..\src\Thread\ThreadSocketTCP.cpp" />
    <ClCompile Include="..\src\Thread\ThreadSocketTCPExtern.cpp" />
    <ClCompile Include="..\src\public\WhiteCalGloable.cpp" />
    <ClCompile Include="..\src\WidgetShow.cpp" />
    <ClCompile Include="..\src\main.cpp" />
    <ClCompile Include="Release\moc_WidgetShow.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\inc\Class\ClassClutterRcsRead.h" />
    <ClInclude Include="..\inc\Class\ClassCoorDinate.h" />
    <ClInclude Include="..\inc\Class\ClassEchoWaveFd.h" />
    <ClInclude Include="..\inc\Class\ClassRcsRead.h" />
    <ClInclude Include="ui_MainWindow.h" />
    <ClInclude Include="ui_WidgetShow.h" />
    <CustomBuild Include="..\inc\GLWidget\GLWidgetInstanceBWFreq.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing GLWidgetInstanceBWFreq.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing GLWidgetInstanceBWFreq.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\GLWidget\GLWidgetWave.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing GLWidgetWave.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing GLWidgetWave.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <ClInclude Include="..\..\..\cuda\inc\KernelAntenna.h" />
    <ClInclude Include="..\..\..\cuda\inc\KernelChaff.h" />
    <ClInclude Include="..\..\..\cuda\inc\KernelCoorConver.h" />
    <ClInclude Include="..\..\..\cuda\inc\KernelCultter.h" />
    <ClInclude Include="..\..\..\cuda\inc\KernelJAM.h" />
    <ClInclude Include="..\..\..\cuda\inc\KernelPublic.h" />
    <ClInclude Include="..\..\..\cuda\inc\KernelSar.h" />
    <ClInclude Include="..\..\..\cuda\inc\KernelSpline.h" />
    <ClInclude Include="..\..\..\cuda\inc\KernelTarHsys.h" />
    <ClInclude Include="..\..\..\cuda\inc\KernelTarHsysFd.h" />
    <CustomBuild Include="..\inc\MainWindow.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing MainWindow.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing MainWindow.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <ClInclude Include="..\inc\MemPool\MyMalloc.h" />
    <ClInclude Include="..\inc\MemPool\MyMallocCUDA.h" />
    <ClInclude Include="..\..\..\inc\RedGloable.h" />
    <CustomBuild Include="..\inc\Thread\ThreadClassAntennaGen.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassAntennaGen.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassAntennaGen.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassBase.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassBase.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassBase.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassBlanketJamGen.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassBlanketJamGen.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassBlanketJamGen.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassChaffEcho.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassChaffEcho.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassChaffEcho.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassClutter.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassClutter.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassClutter.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassDeceptionJamGen.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassDeceptionJamGen.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassDeceptionJamGen.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassGPUCalcute.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassGPUCalcute.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassGPUCalcute.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassGenJAM.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassGenJAM.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassGenJAM.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassJFEcho.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassJFEcho.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassJFEcho.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassSarEcho.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassSarEcho.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassSarEcho.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadClassSarEchoClutter.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassSarEchoClutter.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassSarEchoClutter.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <ClInclude Include="..\inc\Thread\ThreadClassSarGenImag.h" />
    <CustomBuild Include="..\inc\Thread\ThreadClassSendWaveGen.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadClassSendWaveGen.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadClassSendWaveGen.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadEchoDataFormat.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadEchoDataFormat.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadEchoDataFormat.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadEchoDataSave.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadEchoDataSave.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadEchoDataSave.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadResourceScheduling.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadResourceScheduling.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadResourceScheduling.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadSocket.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadSocket.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadSocket.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadSocketTCP.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadSocketTCP.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadSocketTCP.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <CustomBuild Include="..\inc\Thread\ThreadSocketTCPExtern.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing ThreadSocketTCPExtern.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing ThreadSocketTCPExtern.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <ClInclude Include="..\..\..\inc\WhiteCalGlobel.h" />
    <CustomBuild Include="..\inc\WidgetShow.h">
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DNDEBUG "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\release" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Moc%27ing WidgetShow.h...</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\moc.exe"  "%(FullPath)" -o "$(ConfigurationName)\moc_%(Filename).cpp"  -D_WINDOWS -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -D_STUMEMPOOL_LIBRARY -D_GLWidgetBase_LIBRARY -D_ECHO_KERNEL_LIBRARY -DQT_OPENGL_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB "-I." "-I.\..\inc" "-I.\..\..\..\inc" "-I.\..\..\..\inc\GLWidget" "-I.\..\..\..\cuda\inc" "-I.\..\..\..\inc\MemPool" "-I.\..\inc\Thread" "-I.\..\inc\Class" "-I.\..\inc\public" "-I.\..\inc\cuda" "-I.\..\inc\GLWidget" "-I.\..\..\..\inc\Extern" "-IC:\cuda\v10.0\include" "-IC:\cuda\v10.0\common\inc" "-IC:\cuda\v10.0" "-I.\..\..\..\..\Dadar" "-I.\..\..\EchoInterfeCalcutePool_Com" "-I$(QTDIR)\include" "-I$(QTDIR)\include\QtOpenGL" "-I$(QTDIR)\include\QtWidgets" "-I$(QTDIR)\include\QtGui" "-I$(QTDIR)\include\QtANGLE" "-I$(QTDIR)\include\QtCore" "-I.\debug" "-I$(QTDIR)\mkspecs\win32-msvc"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Moc%27ing WidgetShow.h...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ConfigurationName)\moc_%(Filename).cpp</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe;%(FullPath)</AdditionalInputs>
    </CustomBuild>
    <ClInclude Include="..\..\..\inc\GLWidget\_GLWidgetBase.h" />
    <ClInclude Include="..\..\..\inc\MemPool\_stumempool.h" />
    <ClInclude Include="..\..\..\inc\Extern\float2.h" />
    <ClInclude Include="..\..\..\inc\globalExternalStruct.h" />
    <ClInclude Include="..\..\..\inc\globalJamStruct.h" />
    <ClInclude Include="..\..\..\inc\globalRadarStruct.h" />
    <ClInclude Include="..\..\..\inc\Extern\wMemoryOperation.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Debug\moc_GLWidgetInstanceBWFreq.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_GLWidgetWave.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_MainWindow.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassAntennaGen.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassBase.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassBlanketJamGen.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassChaffEcho.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassClutter.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassDeceptionJamGen.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassGenJAM.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassGPUCalcute.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassJFEcho.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassSarEcho.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassSarEchoClutter.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadClassSendWaveGen.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadEchoDataFormat.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadEchoDataSave.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadResourceScheduling.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadSocket.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadSocketTCP.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_ThreadSocketTCPExtern.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Debug\moc_WidgetShow.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_GLWidgetInstanceBWFreq.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_GLWidgetWave.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_MainWindow.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassAntennaGen.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassBase.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassBlanketJamGen.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassChaffEcho.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassClutter.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassDeceptionJamGen.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassGenJAM.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassGPUCalcute.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassJFEcho.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassSarEcho.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassSarEchoClutter.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadClassSendWaveGen.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadEchoDataFormat.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadEchoDataSave.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadResourceScheduling.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadSocket.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadSocketTCP.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Release\moc_ThreadSocketTCPExtern.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <CustomBuild Include="debug\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zi -MDd -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;debug\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\moc_predefs.h;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="release\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -O2 -MD -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;release\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\moc_predefs.h;%(Outputs)</Outputs>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\ui\MainWindow.ui">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\uic.exe" -o ".\ui_%(Filename).h" "%(FullPath)"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Uic%27ing %(Identity)...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.\ui_%(Filename).h;%(Outputs)</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\uic.exe" -o ".\ui_%(Filename).h" "%(FullPath)"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Uic%27ing %(Identity)...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\ui_%(Filename).h;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\ui\WidgetShow.ui">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">"$(QTDIR)\bin\uic.exe" -o ".\ui_%(Filename).h" "%(FullPath)"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Uic%27ing %(Identity)...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.\ui_%(Filename).h;%(Outputs)</Outputs>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">"$(QTDIR)\bin\uic.exe" -o ".\ui_%(Filename).h" "%(FullPath)"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Uic%27ing %(Identity)...</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\ui_%(Filename).h;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\..\..\cuda\src\KernelAntenna.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\cuda\src\KernelAntenna.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe --machine 64 -arch=sm_61 -c -o KernelAntennatestcuda.obj ..\..\..\cuda\src\KernelAntenna.cu -Xcompiler /MD</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">KernelAntennatestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelChaff.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\cuda\src\KernelChaff.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe --machine 64 -arch=sm_61 -c -o KernelChafftestcuda.obj ..\..\..\cuda\src\KernelChaff.cu -Xcompiler /MD</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">KernelChafftestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelCoorConver.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\cuda\src\KernelCoorConver.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe --machine 64 -arch=sm_61 -c -o KernelCoorConvertestcuda.obj ..\..\..\cuda\src\KernelCoorConver.cu -Xcompiler /MD</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">KernelCoorConvertestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelCultter.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\cuda\src\KernelCultter.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe --machine 64 -arch=sm_61 -c -o KernelCulttertestcuda.obj ..\..\..\cuda\src\KernelCultter.cu -Xcompiler /MD</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">KernelCulttertestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelJAM.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\cuda\src\KernelJAM.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe --machine 64 -arch=sm_61 -c -o KernelJAMtestcuda.obj ..\..\..\cuda\src\KernelJAM.cu -Xcompiler /MD</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">KernelJAMtestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelPublic.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\cuda\src\KernelPublic.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe --machine 64 -arch=sm_61 -c -o KernelPublictestcuda.obj ..\..\..\cuda\src\KernelPublic.cu -Xcompiler /MD</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">KernelPublictestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelSar.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\cuda\src\KernelSar.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe --machine 64 -arch=sm_61 -c -o KernelSartestcuda.obj ..\..\..\cuda\src\KernelSar.cu -Xcompiler /MD</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">KernelSartestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelSpline.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\cuda\src\KernelSpline.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe --machine 64 -arch=sm_61 -c -o KernelSplinetestcuda.obj ..\..\..\cuda\src\KernelSpline.cu -Xcompiler /MD</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">KernelSplinetestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelTarHsys.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\cuda\src\KernelTarHsys.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe --machine 64 -arch=sm_61 -c -o KernelTarHsystestcuda.obj ..\..\..\cuda\src\KernelTarHsys.cu -Xcompiler /MD</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">KernelTarHsystestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelTarHsysFd.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\cuda\src\KernelTarHsysFd.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe --machine 64 -arch=sm_61 -c -o KernelTarHsysFdtestcuda.obj ..\..\..\cuda\src\KernelTarHsysFd.cu -Xcompiler /MD</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">KernelTarHsysFdtestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\..\..\cuda\src\KernelAntenna.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\cuda\src\KernelAntenna.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe -D_DEBUG --machine 64 -arch=sm_61 -c -o KernelAntennatestcuda.obj ..\..\..\cuda\src\KernelAntenna.cu -Xcompiler /MDd</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">KernelAntennatestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelChaff.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\cuda\src\KernelChaff.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe -D_DEBUG --machine 64 -arch=sm_61 -c -o KernelChafftestcuda.obj ..\..\..\cuda\src\KernelChaff.cu -Xcompiler /MDd</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">KernelChafftestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelCoorConver.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\cuda\src\KernelCoorConver.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe -D_DEBUG --machine 64 -arch=sm_61 -c -o KernelCoorConvertestcuda.obj ..\..\..\cuda\src\KernelCoorConver.cu -Xcompiler /MDd</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">KernelCoorConvertestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelCultter.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\cuda\src\KernelCultter.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe -D_DEBUG --machine 64 -arch=sm_61 -c -o KernelCulttertestcuda.obj ..\..\..\cuda\src\KernelCultter.cu -Xcompiler /MDd</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">KernelCulttertestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelJAM.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\cuda\src\KernelJAM.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe -D_DEBUG --machine 64 -arch=sm_61 -c -o KernelJAMtestcuda.obj ..\..\..\cuda\src\KernelJAM.cu -Xcompiler /MDd</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">KernelJAMtestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelPublic.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\cuda\src\KernelPublic.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe -D_DEBUG --machine 64 -arch=sm_61 -c -o KernelPublictestcuda.obj ..\..\..\cuda\src\KernelPublic.cu -Xcompiler /MDd</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">KernelPublictestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelSar.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\cuda\src\KernelSar.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe -D_DEBUG --machine 64 -arch=sm_61 -c -o KernelSartestcuda.obj ..\..\..\cuda\src\KernelSar.cu -Xcompiler /MDd</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">KernelSartestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelSpline.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\cuda\src\KernelSpline.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe -D_DEBUG --machine 64 -arch=sm_61 -c -o KernelSplinetestcuda.obj ..\..\..\cuda\src\KernelSpline.cu -Xcompiler /MDd</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">KernelSplinetestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelTarHsys.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\cuda\src\KernelTarHsys.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe -D_DEBUG --machine 64 -arch=sm_61 -c -o KernelTarHsystestcuda.obj ..\..\..\cuda\src\KernelTarHsys.cu -Xcompiler /MDd</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">KernelTarHsystestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="..\..\..\cuda\src\KernelTarHsysFd.cu">
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\cuda\src\KernelTarHsysFd.cu;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe -D_DEBUG --machine 64 -arch=sm_61 -c -o KernelTarHsysFdtestcuda.obj ..\..\..\cuda\src\KernelTarHsysFd.cu -Xcompiler /MDd</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:/cuda/v10.0/bin/nvcc.exe</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">KernelTarHsysFdtestcuda.obj;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties UicDir="." RccDir="." Qt5Version_x0020_x64="QT5.9.1" />
    </VisualStudio>
  </ProjectExtensions>
</Project>