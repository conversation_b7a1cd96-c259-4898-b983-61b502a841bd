/*******************************************************************************************
 * FileProperties: 
 *     FileName: MainWindow.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/MainWindow.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>

#include "_stumempool.h"

#include "WhiteCalGlobel.h"
#include "ThreadResourceScheduling.h"
#include "ThreadClassBase.h"
#include "ThreadSocketTCP.h"
#include "ThreadSocketTCPExtern.h"

#include "WidgetShow.h"

namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow,_StuMemPool
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = 0);
    ~MainWindow();
signals:
    void sendTestRun(void *p);
    void sendThreadReplay(int flag);
    
private slots:
    void on_comboBoxSimMode_currentIndexChanged(int index);
    
    void on_spinBoxCPITime_valueChanged(int arg1);

    void on_pushButtonSettingPara_clicked();
    
    void on_pushButtonRun_clicked();
    
    void on_pushButtonWaveShow_clicked();

    void on_pushButtonSARRPL_clicked();

private:
    Ui::MainWindow *ui;
    WidgetShow *pWidgetShow;
    

    ThreadResourceScheduling	*pThreadResourceScheduling;
	ThreadClassBase				*pThreadClassBase;
    ThreadSocketTCPExtern       *pThreadSocketTCP;


    bool m_ParaSettingFlag;
};

#endif // MAINWINDOW_H
