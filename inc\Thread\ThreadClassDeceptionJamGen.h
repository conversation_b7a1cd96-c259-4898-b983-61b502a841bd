/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassInterference.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassDeceptionJamGen.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef THREADCLASSDECEPTIONJAMGEN_H
#define THREADCLASSDECEPTIONJAMGEN_H

#include <QObject>

#include"MyMalloc.h"
#include"MyMallocCUDA.h"
#include "_stumempool.h"

#include "WhiteCalGlobel.h"

#include<cufft.h>
#include <curand.h>

class ThreadClassDeceptionJamGen : public QObject,MyMalloc,MyMallocCUDA,_StuMemPool
{
    Q_OBJECT
public:
    explicit ThreadClassDeceptionJamGen(QObject *parent = 0);
    ~ThreadClassDeceptionJamGen();
    
public:
    //初始化GPU设备工作参数
	bool InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic);
	//初始化cuda参数
	bool InitCUDAPara();

	//欺骗干扰主函数
	void GenDeceptionJam(void *p, stEchoData *pStEchoData);

	//天线
	void slotSettingAntPara(void *p);

signals:
	void sendJamDataFormat(void *p);


private:
	//压制噪声参数初始化
	void JamParamSet(JAM_DECEPTION_PARAM *JamParam);
	template<class T, class T2, class T3, class T4>
	void CalAntWeight(int DYT, ANTPARAM *AntParam, RADAR *ParamR, T *Radar_Pos, T *JamDev_Pos, T2 *AntFuction, T2 *AziAntFuction, T2 *PitAntFuction, T3 *AntGain, T4 *Range);
    //噪声生成
    template <class T,class T2,class T3,class T4,class T5,class T6,class T7>
    void NoiseModelGen(int NoiseSigLen, T MeanValue, T2 StdValue, T3 NoiseFc, T4 NoiseBr, T5 NoiseAmp, T6 Fs, T7 *dev_NoiseModel);

    //距离拖引
    template<class T,class T2>
	void RangePull(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, double Tr, int JamSigLen, double SimTime,
											double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);
	//速度拖引
	template<class T, class T2>
	void VelocityPull(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, double Tr, int JamSigLen, double SimTime,
											double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);
	//距离速度同步拖引
	template<class T, class T2>
	void RangeVelocityPull(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, double Tr, int JamSigLen, double SimTime,
											double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);

	//频移干扰
	template<class T, class T2>
	void FreqShiftJam(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, double Tr, int JamSigLen, double SimTime,
											double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);

	//密集复制+重复转发干扰
	template<class T, class T2>
	void CopyTransmitJam(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, int JamSigLen, double SimTime,int PRTLen,
											double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);

	//切片干扰
	template<class T, class T2>
	void CutJam(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, double Tr, int JamSigLen, int PRTLen, double SimTime,
											double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);

    // 多普勒闪烁干扰
    template<class T,class T2>
    void DopplerJamGen(JAM_PARAM JamParam, double Fs, double Tr, int JamSigLen, int PRFID, double SimTime,
                                            double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);


    // 灵巧噪声干扰
    template<class T,class T2>
    void NoiseJamGen(JAM_PARAM JamParam, double Fs, int JamSigLen, int PRFID, int PRTLen, double SimTime,
                                            double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);

    // 随机假目标干扰
    template<class T,class T2>
    void RandMulTargetJamGen(JAM_PARAM JamParam, double Fs, double Tr, int JamSigLen, int PRFID, double SimTime,
                                            double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);
    // 多普勒噪声干扰
    template<class T,class T2>
    void DopplerNoiseJamGen(JAM_PARAM JamParam, double Fs, int JamSigLen, double SimTime,
                                            double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);

        
private:
    int m_DevID;
	char *d_BufferFather, *d_BufferInit;
	long long BufferInit_SizeTotal, BufferInit_Cur;

    curandGenerator_t	gen_curand;
	cufftHandle			plan;

    cufftHandle plan_1k;
    cufftHandle plan_2k;
    cufftHandle plan_4k;
    cufftHandle plan_8k;
    cufftHandle plan_16k;
    cufftHandle plan_32k;
    cufftHandle plan_64k;
    cufftHandle plan_128k;
    cufftHandle plan_256k;
    cufftHandle plan_512k;
    cufftHandle CufftPlanCheck(int fftDot);

	int CosValueLen;//三角函数表长
	float *dev_CosValue, *dev_SinValue;//三角函数查表显存
	float *m_CosValue, *m_SinValue;
    Complexf *dev_RandomComplexf;

	float   *pSumAntennaFunction, *pAziSubAntennaFunction, *pPitSubAntennaFunction;
	ANTPARAM *m_AntParam, *m_AntParamJam;
    
};

#endif // THREADCLASSDECEPTIONJAMGEN_H
