/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassJFEcho.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassJFEcho.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassJFEcho.h"
#include<QThread>
#include <QDateTime>

#include <cuda_runtime_api.h>
#include<cuda_runtime.h>
#include<cuda_device_runtime_api.h>
#include<cufft.h>
#include<curand.h>

#include <QFile>
#include <QDebug>

#include "KernelSar.h"
#include "KernelPublic.h"

#include "ThreadClassSendWaveGen.h"

ThreadClassJFEcho::ThreadClassJFEcho(int DevID,int buffer_size,char *d_Buffer,QObject *parent) : QObject(parent)
{
    SetMemPoolSrcFileName((char*)"ThreadClassJFEcho", sizeof("ThreadClassJFEcho"));
	_DefineMemPool(stTarLink, 50);
    //分配CPU伪内存池空间
    InitMyMalloc(64*1024*1024);
	m_DevID				= -1;

	m_StTarLink = (stTarLink*)_MallocMemObj(stTarLink); m_StTarLink->init();

    pClassCoorDinate = new ClassCoorDinate;

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassJFEcho::~ThreadClassJFEcho()
{

}
//初始化GPU设备工作参数
bool ThreadClassJFEcho::InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic)
{
	m_BytesOffsetTarRCS		= 0;
	BufferInit_Cur			= 0;
	BufferInit_SizeTotal	= (long long)512 * 1024 * 1024;
	BufferInitTarRcs_Size	= (long long)1024 * 1024 * 1024;

	m_BytesOffsetTarRCS		= 0;
    if(d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
        m_DevID				= DevID;
		d_BufferInit		= d_Buffer;
		m_d_BufferTarRCS	= d_Buffer + BufferInit_SizeTotal;
		d_BufferFather		= d_BufferPublic;
        //分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, 5.0 * 1024 * 1024 * 1024);
    }
    else{//作为独立的线程使用，单独使用一个GPU卡
        m_DevID         = -1;
        d_BufferFather  = nullptr;
        //分配GPU伪内存池空间
		cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
		cudaMalloc((void**)&m_d_BufferTarRCS, BufferInitTarRcs_Size);
        cudaMalloc((void**)&d_Buffer,buffer_size);
		d_BufferFather = d_Buffer;
		InitMyMallocCUDA(d_BufferFather, buffer_size);
    }
    m_pBufferTarRCS = (char*)malloc(32 * 1024 * 1024.f);
	memset(m_pBufferTarRCS, 0, 32* 1024 * 1024.f);

	cudaEventCreate(&e_start);
	cudaEventCreate(&e_stop);
	cudaEventCreate(&e_start2);
	cudaEventCreate(&e_stop2);

    return true;
}
//加载目标RCS系数
void ThreadClassJFEcho::InitTarRcsPara(void *p)
{
	cudaSetDevice(m_DevID);
	stTarPitLink *pStTarPitLinkRecv = (stTarPitLink*)p;

	stTarPitLink *pStTarPitLinkHead = (stTarPitLink*)_MallocMemObj(stTarPitLink); pStTarPitLinkHead->init();

	float *pTemp = (float*)m_pBufferTarRCS;

	stTarPitLink *pStTarPitLinkRecvNode = pStTarPitLinkRecv;
	while (pStTarPitLinkRecvNode->next)
	{
		pStTarPitLinkRecvNode = pStTarPitLinkRecvNode->next;
		stTarPitLink *pStTarPitLinkNode		= (stTarPitLink*)_MallocMemObj(stTarPitLink); pStTarPitLinkNode->init();
		stTarAziLink *pStTarAziLinkHead		= (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkHead->init();
		pStTarPitLinkNode->pStTarAziLink	= pStTarAziLinkHead;

		stTarAziLink *pStTarAziLinkRecv		= pStTarPitLinkRecvNode->pStTarAziLink;
		while (pStTarAziLinkRecv->next)
		{
			pStTarAziLinkRecv = pStTarAziLinkRecv->next;
			stTarAziLink *pStTarAziLinkNode = (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkNode->init();

			int Bytes = (pStTarAziLinkRecv->pointNum*sizeof(float) + 16) / 16 * 16;

			pStTarAziLinkNode->pMissileDataGPU.x = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.x, pStTarAziLinkRecv->pMissileData->x, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.y = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.y, pStTarAziLinkRecv->pMissileData->y, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.z = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.z, pStTarAziLinkRecv->pMissileData->z, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.rcs = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			for (int i = 0; i < pStTarAziLinkRecv->pointNum; i++){ pTemp[i] = pStTarAziLinkRecv->pMissileData->rcs[i] * pStTarAziLinkRecv->pMissileData->rcs[i]; }
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.rcs, pTemp, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.phi = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			for (int i = 0; i < pStTarAziLinkRecv->pointNum; i++){ pTemp[i] = (pStTarAziLinkRecv->pMissileData->phi[i] + 180) / 360; }
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.phi, pTemp, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);

			pStTarAziLinkNode->pointNum = pStTarAziLinkRecv->pointNum;
			pStTarAziLinkNode->pit = pStTarAziLinkRecv->pit;
			pStTarAziLinkNode->azi = pStTarAziLinkRecv->azi;
			AddLink(pStTarAziLinkHead, pStTarAziLinkNode);
		}
		AddLink(pStTarPitLinkHead, pStTarPitLinkNode);
	}
	stTarLink *pStTarLink = (stTarLink*)_MallocMemObj(stTarLink); pStTarLink->init();
	pStTarLink->pStTarPitLink	= pStTarPitLinkHead;
	pStTarLink->iTarNo			= pStTarPitLinkRecv->iTarNo;
	AddLink(m_StTarLink, pStTarLink);
}
//提取角反模型散射系数
void ThreadClassJFEcho::ExtractCorRefScatterCoef(Target *pTarget, PLATFORM *pPlatForm, int TarNum)
{
	cudaError_t Err;

	float *pTemp = (float*)_Malloc(sizeof(float) * 32);
	float *pMatixG = (float*)pTemp;
	float *dev_Temp = (float*)_MallocCUDA(sizeof(float) * 32);
	float *dev_MatixG = (float*)(dev_Temp);
	float *dev_origin = (float*)(dev_Temp + 16);

	memset(pTemp, 0, sizeof(float) * 32);
	cudaMemset(dev_Temp, 0, sizeof(float) * 32);

	pClassCoorDinate->_Release_Malloc();
	for (int frame = 0; frame < TarNum; frame++)
	{
		//提取目标RCS链表
		int iTarNo = pTarget[frame].ModeNo;
		stTarPitLink *pStTarPitLink = nullptr;
		stTarLink *pStTarLinkTemp = m_StTarLink;
		bool IsTarExit = false;
		while (pStTarLinkTemp->next)
		{
			pStTarLinkTemp = pStTarLinkTemp->next;
			if (pStTarLinkTemp->iTarNo == iTarNo)
			{
				pStTarPitLink = pStTarLinkTemp->pStTarPitLink;
				IsTarExit = true;
				break;
			}
		}
		if (IsTarExit == false){
			qDebug() << "GPU:" << m_DevID << __LINE__ << __FUNCTION__ << "-----------------------------------IsTarExit: false!" << iTarNo;
			continue;
		}
		//取出对应角度上的散射系数
		float *pTarDYTRang = (float*)_Malloc(3 * sizeof(float));
		pTarDYTRang[0] = pPlatForm[0].NorthPos;//
		pTarDYTRang[1] = pPlatForm[0].SkyPos;//
		pTarDYTRang[2] = pPlatForm[0].EastPos;//
		//计算目标旋转矩阵
		float pTarVel[3] = { 0 };
		pTarVel[0] = 3; pTarVel[1] = 0.001; pTarVel[2] = 0.001;
		float *TarPoz = (float*)(pTemp + 16);
		TarPoz[0] = pTarget[frame].TargetX;
		TarPoz[1] = pTarget[frame].TargetY;
		TarPoz[2] = pTarget[frame].TargetZ;

		float Azi = 0, pit = 0;
		pClassCoorDinate->ConvertAngleMatixG(pTarVel, TarPoz, pTarDYTRang, Azi, pit, pMatixG);
		stTarAziLink *pStTarAziLinkNode = CalTarRcs(abs(pit * 180 / PI), abs(Azi * 180 / PI), pStTarPitLink);
		//散射点从本体转到北天东
		cudaMemcpy(dev_Temp, pTemp, sizeof(float) * 32, cudaMemcpyHostToDevice);
		Err = cudaGetLastError();
		pClassCoorDinate->CoorBoatToBtd_CUDA(pStTarAziLinkNode->pointNum, dev_MatixG, dev_origin,
			pStTarAziLinkNode->pMissileDataGPU.x, pStTarAziLinkNode->pMissileDataGPU.y, pStTarAziLinkNode->pMissileDataGPU.z,
			dev_Target_ALL1 + m_SacreNum, dev_Target_ALL2 + m_SacreNum, dev_Target_ALL3 + m_SacreNum);
		cudaMemcpy(dev_Target_ALL4 + m_SacreNum, pStTarAziLinkNode->pMissileDataGPU.rcs, pStTarAziLinkNode->pointNum*sizeof(float), cudaMemcpyDeviceToDevice);
		cudaMemcpy(dev_Target_ALL5 + m_SacreNum, pStTarAziLinkNode->pMissileDataGPU.phi, pStTarAziLinkNode->pointNum*sizeof(float), cudaMemcpyDeviceToDevice);
		Err = cudaGetLastError();

		m_SacreNum += pStTarAziLinkNode->pointNum;
	}

}

void ThreadClassJFEcho::GenSenceEcho(void *p,stEchoData *pStEchoData)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	stSarEchoPara   *pStSarEchoPara = (stSarEchoPara*)p;
	SimStruct       *SimData		= (SimStruct*)&pStSarEchoPara->SimData;
	PLATFORM        *pPlatForm		= pStSarEchoPara->PlatForm;
	Target          *pTarget		= pStSarEchoPara->pCorRef;

	dev_Target_ALL1 = pStEchoData->dev_Target_ALL1 + pStEchoData->SacreNum;
	dev_Target_ALL2 = pStEchoData->dev_Target_ALL2 + pStEchoData->SacreNum;
	dev_Target_ALL3 = pStEchoData->dev_Target_ALL3 + pStEchoData->SacreNum;
	dev_Target_ALL4 = pStEchoData->dev_Target_ALL4 + pStEchoData->SacreNum;
	dev_Target_ALL5 = pStEchoData->dev_Target_ALL5 + pStEchoData->SacreNum;

	//目标散射系数提取
	m_SacreNum = 0;
	ExtractCorRefScatterCoef(pTarget, pPlatForm, pStSarEchoPara->CorRefNum);
	pStEchoData->SacreNum += m_SacreNum;

}




