/*******************************************************************************************
 * FileProperties: 
 *     FileName: MyMalloc.h
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/MemPool/MyMalloc.h $
 *         $Author: yening $
 *         $Revision: 19 $
 *         $Date: 2024-12-22 15:19:29 $
*******************************************************************************************/
#ifndef MYMALLOC
#define MYMALLOC

class MyMalloc
{
public:
    MyMalloc();

public:
	bool InitMyMalloc(long long bytes);
	char *_Malloc(long long bytes);
    void _Release_Malloc();

private:
    char *m_Buffer;
    long long m_BufferLength;



};

#endif // MYMALLOC

