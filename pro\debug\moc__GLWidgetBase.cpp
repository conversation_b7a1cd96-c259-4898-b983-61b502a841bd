/****************************************************************************
** Meta object code from reading C++ file '_GLWidgetBase.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../inc/GLWidget/_GLWidgetBase.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file '_GLWidgetBase.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata__GLWidgetBase_t {
    QByteArrayData data[49];
    char stringdata0[541];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata__GLWidgetBase_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata__GLWidgetBase_t qt_meta_stringdata__GLWidgetBase = {
    {
QT_MOC_LITERAL(0, 0, 13), // "_GLWidgetBase"
QT_MOC_LITERAL(1, 14, 19), // "sendRxZoomRangePara"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 6), // "rxStrt"
QT_MOC_LITERAL(4, 42, 5), // "rxEnd"
QT_MOC_LITERAL(5, 48, 20), // "sendMainWindowKeyVal"
QT_MOC_LITERAL(6, 69, 10), // "QKeyEvent*"
QT_MOC_LITERAL(7, 80, 5), // "event"
QT_MOC_LITERAL(8, 86, 19), // "slotRxZoomRangePara"
QT_MOC_LITERAL(9, 106, 7), // "_rxStrt"
QT_MOC_LITERAL(10, 114, 6), // "_rxEnd"
QT_MOC_LITERAL(11, 121, 10), // "enterEvent"
QT_MOC_LITERAL(12, 132, 7), // "QEvent*"
QT_MOC_LITERAL(13, 140, 10), // "leaveEvent"
QT_MOC_LITERAL(14, 151, 15), // "mousePressEvent"
QT_MOC_LITERAL(15, 167, 12), // "QMouseEvent*"
QT_MOC_LITERAL(16, 180, 14), // "mouseMoveEvent"
QT_MOC_LITERAL(17, 195, 17), // "mouseReleaseEvent"
QT_MOC_LITERAL(18, 213, 21), // "mouseDoubleClickEvent"
QT_MOC_LITERAL(19, 235, 10), // "wheelEvent"
QT_MOC_LITERAL(20, 246, 12), // "QWheelEvent*"
QT_MOC_LITERAL(21, 259, 13), // "keyPressEvent"
QT_MOC_LITERAL(22, 273, 8), // "drawRect"
QT_MOC_LITERAL(23, 282, 13), // "strtPointDraw"
QT_MOC_LITERAL(24, 296, 12), // "endPointDraw"
QT_MOC_LITERAL(25, 309, 5), // "pSize"
QT_MOC_LITERAL(26, 315, 7), // "rxStart"
QT_MOC_LITERAL(27, 323, 5), // "Y_MAX"
QT_MOC_LITERAL(28, 329, 5), // "Y_MIN"
QT_MOC_LITERAL(29, 335, 13), // "PixPosCalcute"
QT_MOC_LITERAL(30, 349, 19), // "ClassDrawMousePara*"
QT_MOC_LITERAL(31, 369, 14), // "pDrawMousePara"
QT_MOC_LITERAL(32, 384, 17), // "ClassDrawAllFreq*"
QT_MOC_LITERAL(33, 402, 16), // "pDrawAllFreqPara"
QT_MOC_LITERAL(34, 419, 12), // "drawTotalDot"
QT_MOC_LITERAL(35, 432, 13), // "drawDotOffset"
QT_MOC_LITERAL(36, 446, 4), // "int&"
QT_MOC_LITERAL(37, 451, 9), // "maxValPoz"
QT_MOC_LITERAL(38, 461, 6), // "float&"
QT_MOC_LITERAL(39, 468, 6), // "maxVal"
QT_MOC_LITERAL(40, 475, 10), // "PixPosDraw"
QT_MOC_LITERAL(41, 486, 6), // "hRatio"
QT_MOC_LITERAL(42, 493, 6), // "vRatio"
QT_MOC_LITERAL(43, 500, 13), // "StrtDrawIndex"
QT_MOC_LITERAL(44, 514, 14), // "SetWidgetTitle"
QT_MOC_LITERAL(45, 529, 1), // "x"
QT_MOC_LITERAL(46, 531, 1), // "y"
QT_MOC_LITERAL(47, 533, 3), // "str"
QT_MOC_LITERAL(48, 537, 3) // "fnt"

    },
    "_GLWidgetBase\0sendRxZoomRangePara\0\0"
    "rxStrt\0rxEnd\0sendMainWindowKeyVal\0"
    "QKeyEvent*\0event\0slotRxZoomRangePara\0"
    "_rxStrt\0_rxEnd\0enterEvent\0QEvent*\0"
    "leaveEvent\0mousePressEvent\0QMouseEvent*\0"
    "mouseMoveEvent\0mouseReleaseEvent\0"
    "mouseDoubleClickEvent\0wheelEvent\0"
    "QWheelEvent*\0keyPressEvent\0drawRect\0"
    "strtPointDraw\0endPointDraw\0pSize\0"
    "rxStart\0Y_MAX\0Y_MIN\0PixPosCalcute\0"
    "ClassDrawMousePara*\0pDrawMousePara\0"
    "ClassDrawAllFreq*\0pDrawAllFreqPara\0"
    "drawTotalDot\0drawDotOffset\0int&\0"
    "maxValPoz\0float&\0maxVal\0PixPosDraw\0"
    "hRatio\0vRatio\0StrtDrawIndex\0SetWidgetTitle\0"
    "x\0y\0str\0fnt"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data__GLWidgetBase[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      17,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   99,    2, 0x06 /* Public */,
       5,    1,  104,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    2,  107,    2, 0x0a /* Public */,
      11,    1,  112,    2, 0x0a /* Public */,
      13,    1,  115,    2, 0x0a /* Public */,
      14,    1,  118,    2, 0x0a /* Public */,
      16,    1,  121,    2, 0x0a /* Public */,
      17,    1,  124,    2, 0x0a /* Public */,
      18,    1,  127,    2, 0x0a /* Public */,
      19,    1,  130,    2, 0x0a /* Public */,
      21,    1,  133,    2, 0x0a /* Public */,
      22,    3,  136,    2, 0x0a /* Public */,
      22,    4,  143,    2, 0x0a /* Public */,
      29,    6,  152,    2, 0x0a /* Public */,
      40,    6,  165,    2, 0x0a /* Public */,
      44,    4,  178,    2, 0x0a /* Public */,
      44,    3,  187,    2, 0x2a /* Public | MethodCloned */,

 // signals: parameters
    QMetaType::Void, QMetaType::Float, QMetaType::Float,    3,    4,
    QMetaType::Void, 0x80000000 | 6,    7,

 // slots: parameters
    QMetaType::Void, QMetaType::Float, QMetaType::Float,    9,   10,
    QMetaType::Void, 0x80000000 | 12,    2,
    QMetaType::Void, 0x80000000 | 12,    2,
    QMetaType::Void, 0x80000000 | 15,    7,
    QMetaType::Void, 0x80000000 | 15,    7,
    QMetaType::Void, 0x80000000 | 15,    7,
    QMetaType::Void, 0x80000000 | 15,    7,
    QMetaType::Void, 0x80000000 | 20,    7,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void, QMetaType::QPoint, QMetaType::QPoint, QMetaType::QSize,   23,   24,   25,
    QMetaType::Void, QMetaType::Double, QMetaType::Double, QMetaType::Float, QMetaType::Float,   26,    4,   27,   28,
    QMetaType::Void, 0x80000000 | 30, 0x80000000 | 32, QMetaType::Int, QMetaType::Int, 0x80000000 | 36, 0x80000000 | 38,   31,   33,   34,   35,   37,   39,
    QMetaType::Void, 0x80000000 | 32, QMetaType::Int, QMetaType::Float, QMetaType::Float, QMetaType::Float, QMetaType::Int,   33,   37,   39,   41,   42,   43,
    QMetaType::Void, QMetaType::Double, QMetaType::Double, QMetaType::QString, QMetaType::QFont,   45,   46,   47,   48,
    QMetaType::Void, QMetaType::Double, QMetaType::Double, QMetaType::QString,   45,   46,   47,

       0        // eod
};

void _GLWidgetBase::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        _GLWidgetBase *_t = static_cast<_GLWidgetBase *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sendRxZoomRangePara((*reinterpret_cast< float(*)>(_a[1])),(*reinterpret_cast< float(*)>(_a[2]))); break;
        case 1: _t->sendMainWindowKeyVal((*reinterpret_cast< QKeyEvent*(*)>(_a[1]))); break;
        case 2: _t->slotRxZoomRangePara((*reinterpret_cast< float(*)>(_a[1])),(*reinterpret_cast< float(*)>(_a[2]))); break;
        case 3: _t->enterEvent((*reinterpret_cast< QEvent*(*)>(_a[1]))); break;
        case 4: _t->leaveEvent((*reinterpret_cast< QEvent*(*)>(_a[1]))); break;
        case 5: _t->mousePressEvent((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 6: _t->mouseMoveEvent((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 7: _t->mouseReleaseEvent((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 8: _t->mouseDoubleClickEvent((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 9: _t->wheelEvent((*reinterpret_cast< QWheelEvent*(*)>(_a[1]))); break;
        case 10: _t->keyPressEvent((*reinterpret_cast< QKeyEvent*(*)>(_a[1]))); break;
        case 11: _t->drawRect((*reinterpret_cast< QPoint(*)>(_a[1])),(*reinterpret_cast< QPoint(*)>(_a[2])),(*reinterpret_cast< QSize(*)>(_a[3]))); break;
        case 12: _t->drawRect((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2])),(*reinterpret_cast< float(*)>(_a[3])),(*reinterpret_cast< float(*)>(_a[4]))); break;
        case 13: _t->PixPosCalcute((*reinterpret_cast< ClassDrawMousePara*(*)>(_a[1])),(*reinterpret_cast< ClassDrawAllFreq*(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< int(*)>(_a[4])),(*reinterpret_cast< int(*)>(_a[5])),(*reinterpret_cast< float(*)>(_a[6]))); break;
        case 14: _t->PixPosDraw((*reinterpret_cast< ClassDrawAllFreq*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< float(*)>(_a[3])),(*reinterpret_cast< float(*)>(_a[4])),(*reinterpret_cast< float(*)>(_a[5])),(*reinterpret_cast< int(*)>(_a[6]))); break;
        case 15: _t->SetWidgetTitle((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2])),(*reinterpret_cast< QString(*)>(_a[3])),(*reinterpret_cast< const QFont(*)>(_a[4]))); break;
        case 16: _t->SetWidgetTitle((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2])),(*reinterpret_cast< QString(*)>(_a[3]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (_GLWidgetBase::*_t)(float , float );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&_GLWidgetBase::sendRxZoomRangePara)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (_GLWidgetBase::*_t)(QKeyEvent * );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&_GLWidgetBase::sendMainWindowKeyVal)) {
                *result = 1;
                return;
            }
        }
    }
}

const QMetaObject _GLWidgetBase::staticMetaObject = {
    { &QGLWidget::staticMetaObject, qt_meta_stringdata__GLWidgetBase.data,
      qt_meta_data__GLWidgetBase,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *_GLWidgetBase::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *_GLWidgetBase::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata__GLWidgetBase.stringdata0))
        return static_cast<void*>(const_cast< _GLWidgetBase*>(this));
    return QGLWidget::qt_metacast(_clname);
}

int _GLWidgetBase::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QGLWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 17)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 17;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 17)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 17;
    }
    return _id;
}

// SIGNAL 0
void _GLWidgetBase::sendRxZoomRangePara(float _t1, float _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void _GLWidgetBase::sendMainWindowKeyVal(QKeyEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
