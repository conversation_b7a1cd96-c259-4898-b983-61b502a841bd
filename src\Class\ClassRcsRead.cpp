/*******************************************************************************************
 * FileProperties: 
 *     FileName: ClassRcsRead.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Class/ClassRcsRead.cpp $
 *         $Author: yening $
 *         $Revision: 160 $
 *         $Date: 2025-02-05 17:40:22 $
*******************************************************************************************/
#include "ClassRcsRead.h"
#include <QFile>
#include <QDir>
#include "ThreadSocketTCP.h"

#include <iostream>
#include<fstream>
#include<cmath>
#include<sstream>
#include<string>
#include<cstdlib>
#include<limits>
#include<locale>
#include<codecvt>

ClassRcsRead::ClassRcsRead()
{
    SetMemPoolSrcFileName((char*)"ClassRcsRead", sizeof("ClassRcsRead"));
    _DefineMemPool(stTarPitLink, 2000);
    _DefineMemPool(stTarAziLink, 25000);
    _DefineMemPool(MissileData, 5000);
	_DefineMemPool(stRCS_FIlePara, 10);


}

ClassRcsRead::~ClassRcsRead()
{

}

void ClassRcsRead::extractNumbers(char* filename3, double &pit, double &azi)
{
    int numbercount = 0;
    string filename2 = (string)filename3;
    string filename1;
    size_t lastSlash = filename2.find_last_of("/\\");
    if (lastSlash != string::npos) {
        filename1 = filename2.substr(lastSlash + 1);
    }
    else {
        filename1 = filename2;
    }
    const char *pBuffer = filename1.c_str();
    string number;
    char pName[512]; memset(pName, 0, 512);
    int ll = filename1.length();
    memcpy(pName, pBuffer, ll);
    for (int i = 0; i < ll; i++) {
        if (pName[i] >= 48 && pName[i] <= 57) {
            number += pName[i];
        }
        else {
            if (!number.empty()) {
                if (numbercount == 0) {
                    pit = stof(number);
                }
                else if(numbercount == 1)  {
                azi = stof(number);
                }
                number.clear();
                numbercount++;
            }
        }
    }

}

stTarAziLink* ClassRcsRead::CalTarRcs(float pit, float azi, stTarPitLink* pSt)
{
    stTarPitLink* closestFile = nullptr;
    float minpitDistance=FLT_MAX;
    stTarPitLink* pStTarPitLinkFind = pSt;
	float pitdistance = 0;
    while (pStTarPitLinkFind->next) {
        pStTarPitLinkFind = pStTarPitLinkFind->next;
        pitdistance = abs(pit - pStTarPitLinkFind->pStTarAziLink->next->pit);
        if (pitdistance < minpitDistance) {
            minpitDistance = pitdistance;
            closestFile = pStTarPitLinkFind;
        }
    }
    float minaziDistance = FLT_MAX;
    float azidistance;
    stTarAziLink*  finalClosest = nullptr;
    stTarAziLink * pStTarAziLinkFind = closestFile->pStTarAziLink;
    while (pStTarAziLinkFind->next) {
        pStTarAziLinkFind = pStTarAziLinkFind->next;
        azidistance = abs(azi - pStTarAziLinkFind->azi);
        if (azidistance < minaziDistance) {
            minaziDistance = azidistance;
            finalClosest = pStTarAziLinkFind;
        }
    }

    return finalClosest;
}

stTarPitLink* ClassRcsRead::readModPhara(char* filename2)
{
    string filename = (string)filename2;
	vector<string> files;

    int filecount = 0;

	QDir dir(filename.c_str());
	if (!dir.exists()){
		return 0;
	}
	QStringList filters("*.3dsc");
	dir.setFilter(QDir::Files | QDir::NoSymLinks);
	dir.setNameFilters(filters);
	filecount = dir.count();
	if (filecount <= 0){
		return 0;
	}
	QFileInfoList list = dir.entryInfoList();
	std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;
	for (int i = 0; i < list.size(); i++)
	{
		QFileInfo fileInfo = list.at(i);
		QString qstr = fileInfo.absoluteFilePath();
		QByteArray byteArr = qstr.toLocal8Bit();
		string str = string(byteArr);
		files.push_back(str);
	}

    stTarPitLink *pStTarPitLinkHead = (stTarPitLink*)_MallocMemObj(stTarPitLink); pStTarPitLinkHead->init();

	for (int fileIndex = 0; fileIndex < filecount; fileIndex++) 
	{
        double pit = 0;
        double azi = 0;
        extractNumbers((char*)files[fileIndex].c_str(), pit, azi);
        ifstream fileopen(files[fileIndex]);
        int lineCount = 0;
        string line;
        while (getline(fileopen, line)) {
            lineCount++;
			if (lineCount >= MAX_TAR_RCS_DOT)
				break;
        }
		
        fileopen.clear();
        fileopen.seekg(0);
        MissileData* missileData = (MissileData*)_MallocMemObj(MissileData);

        stTarPitLink *pStTarPitLinkTemp = pStTarPitLinkHead;
        bool is_NodeExit = false;
        while (pStTarPitLinkTemp->next)
        {
            pStTarPitLinkTemp = pStTarPitLinkTemp->next;
            if (pStTarPitLinkTemp->pStTarAziLink->next->pit == pit)
            {
                stTarAziLink *pStTarAziLinkNode = (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkNode->init();
                pStTarAziLinkNode->pMissileData = missileData;
				pStTarAziLinkNode->pointNum		= lineCount;
				pStTarAziLinkNode->pit			= pit;
				pStTarAziLinkNode->azi			= azi;
                AddLink(pStTarPitLinkTemp->pStTarAziLink, pStTarAziLinkNode);
                is_NodeExit = true;
                break;
            }
        }
        if (is_NodeExit == false)
        {
            stTarPitLink *pStTarPitLinkNode = (stTarPitLink*)_MallocMemObj(stTarPitLink); pStTarPitLinkNode->init();
            stTarAziLink *pStTarAziLinkHead = (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkHead->init();
            pStTarPitLinkNode->pStTarAziLink = pStTarAziLinkHead;
            stTarAziLink *pStTarAziLinkNode = (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkNode->init();

            pStTarAziLinkNode->pMissileData = missileData;
			pStTarAziLinkNode->pointNum		= lineCount;
			pStTarAziLinkNode->pit			= pit;
			pStTarAziLinkNode->azi			= azi;
            AddLink(pStTarPitLinkNode->pStTarAziLink, pStTarAziLinkNode);
            AddLink(pStTarPitLinkHead, pStTarPitLinkNode);
        }
        if (fileopen.is_open()) {
            for (int j = 0; j < lineCount; j++) {
				fileopen >> missileData->x[j]
							>> missileData->y[j]
							>> missileData->z[j]
							>> missileData->rcs[j]
							>> missileData->phi[j];
            }
            fileopen.close();
        }
        else {
            return 0;
        }
    }

    stTarPitLink *pStTarPitLinkNode = pStTarPitLinkHead;

    stTarPitLink *pStTarPitLinkMax = pStTarPitLinkHead->next;
    stTarPitLink *pStTarPitLinkEnd = nullptr;
    int MaxFlag = 0;
    while (pStTarPitLinkNode->next)//俯仰排序
    {
        pStTarPitLinkNode = pStTarPitLinkNode->next;
        if (pStTarPitLinkNode->next)
        {
            if (pStTarPitLinkMax->pStTarAziLink->next->pit < pStTarPitLinkNode->next->pStTarAziLink->next->pit)
            {
                pStTarPitLinkMax = pStTarPitLinkNode->next;
            }
        }
        else
        {
            RemoveLinkNode(pStTarPitLinkHead, pStTarPitLinkMax);
            AddLink(pStTarPitLinkHead, pStTarPitLinkMax,false);
            if (MaxFlag == 0)
            {
                MaxFlag				= 1;
                pStTarPitLinkEnd	= pStTarPitLinkMax;
            }
            if (pStTarPitLinkEnd->next)
            {
                pStTarPitLinkMax	= pStTarPitLinkEnd->next;
                pStTarPitLinkNode	= pStTarPitLinkEnd;
            }
            else { break; }
        }
    }

    //方位排序
    pStTarPitLinkNode = pStTarPitLinkHead;
    stTarAziLink *pStTarAziLinkEnd = nullptr;
    MaxFlag = 0;
    while (pStTarPitLinkNode->next)//
    {
        pStTarPitLinkNode = pStTarPitLinkNode->next;

        stTarAziLink *pStTarAziLinkHead = pStTarPitLinkNode->pStTarAziLink;
        stTarAziLink *pStTarAziLinkNode = pStTarPitLinkNode->pStTarAziLink;

        stTarAziLink *pStTarAziLinkMax = pStTarPitLinkNode->pStTarAziLink->next;
		MaxFlag = 0;
        while (pStTarAziLinkNode->next)//俯仰排序
        {
            pStTarAziLinkNode = pStTarAziLinkNode->next;
            if (pStTarAziLinkNode->next)
            {
                if (pStTarAziLinkMax->azi < pStTarAziLinkNode->next->azi)
                {
                    pStTarAziLinkMax = pStTarAziLinkNode->next;
                }
            }
            else
            {
                RemoveLinkNode(pStTarAziLinkHead, pStTarAziLinkMax);
                AddLink(pStTarAziLinkHead, pStTarAziLinkMax, false);
                if (MaxFlag == 0)
                {
                    MaxFlag = 1;
                    pStTarAziLinkEnd = pStTarAziLinkMax;
                }
                if (pStTarAziLinkMax->next)
                {
                    pStTarAziLinkMax = pStTarAziLinkEnd->next;
                    pStTarAziLinkNode = pStTarAziLinkEnd;
                }
                else { break; }
            }
        }
    }
	string str2 = filename + string("\\RCS.dat");
	WriteRCS2File(filecount,pStTarPitLinkHead, (char*)str2.c_str());

    return pStTarPitLinkHead;
}

void ClassRcsRead::WriteRCS2File(int AngleNum,stTarPitLink *pStTarPitLinkHead,char *pFilePath)
{
	stRCS_FIlePara *pStRCS_FIlePara = (stRCS_FIlePara*)_MallocMemObj(stRCS_FIlePara);

	QFile *pQFile = new QFile;
	pQFile->setFileName(pFilePath);
	pQFile->open(QFile::WriteOnly);
	pStRCS_FIlePara->FileHeadCode = 0x5758595a;
	pStRCS_FIlePara->AngleNum = AngleNum;
	pQFile->write((char*)pStRCS_FIlePara, sizeof(stRCS_FIlePara));

	stTarPitLink *pStTarPitLinkRecvNode = pStTarPitLinkHead;
	while (pStTarPitLinkRecvNode->next)
	{
		pStTarPitLinkRecvNode = pStTarPitLinkRecvNode->next;

		stTarAziLink *pStTarAziLinkRecv = pStTarPitLinkRecvNode->pStTarAziLink;
		while (pStTarAziLinkRecv->next)
		{
			pStTarAziLinkRecv = pStTarAziLinkRecv->next;
			pQFile->write((char*)&pStTarAziLinkRecv->pit, sizeof(float));
			pQFile->write((char*)&pStTarAziLinkRecv->azi, sizeof(float));
			pQFile->write((char*)&pStTarAziLinkRecv->pointNum, sizeof(int));
			for (int i = 0; i < pStTarAziLinkRecv->pointNum; i++)
			{
				if (abs(pStTarAziLinkRecv->pMissileData->x[i]) > 500)
				{
					int aa = 10;
				}
			}
			pQFile->write((char*)pStTarAziLinkRecv->pMissileData->x, sizeof(float)*pStTarAziLinkRecv->pointNum);
			pQFile->write((char*)pStTarAziLinkRecv->pMissileData->y, sizeof(float)*pStTarAziLinkRecv->pointNum);
			pQFile->write((char*)pStTarAziLinkRecv->pMissileData->z, sizeof(float)*pStTarAziLinkRecv->pointNum);
			pQFile->write((char*)pStTarAziLinkRecv->pMissileData->rcs, sizeof(float)*pStTarAziLinkRecv->pointNum);
			pQFile->write((char*)pStTarAziLinkRecv->pMissileData->phi, sizeof(float)*pStTarAziLinkRecv->pointNum);
		}
	}
	pQFile->close();
}
stTarPitLink* ClassRcsRead::ReadRCS_File2Link(char *pFilePath)
{
	stRCS_FIlePara *pStRCS_FIlePara = (stRCS_FIlePara*)_MallocMemObj(stRCS_FIlePara);
	string filename = (string)pFilePath;
	string str2 = filename + string("\\RCS.dat");

	QFile *pQFile = new QFile;
	pQFile->setFileName(str2.c_str());
	pQFile->open(QFile::ReadOnly);
	pQFile->read((char*)pStRCS_FIlePara, sizeof(stRCS_FIlePara));

	int AngleNum = pStRCS_FIlePara->AngleNum;
	stTarPitLink *pStTarPitLinkHead = (stTarPitLink*)_MallocMemObj(stTarPitLink); pStTarPitLinkHead->init();
	for (int i = 0; i < AngleNum; i++)
	{
		float pit = 0;
		float azi = 0;
		int pointNum = 0;
		pQFile->read((char*)&pit, sizeof(float));
		pQFile->read((char*)&azi, sizeof(float));
		pQFile->read((char*)&pointNum, sizeof(int));
		MissileData* missileData = (MissileData*)_MallocMemObj(MissileData);

		stTarPitLink *pStTarPitLinkTemp = pStTarPitLinkHead;
		bool is_NodeExit = false;
		while (pStTarPitLinkTemp->next)
		{
			pStTarPitLinkTemp = pStTarPitLinkTemp->next;
			if (pStTarPitLinkTemp->pStTarAziLink->next->pit == pit)
			{
				stTarAziLink *pStTarAziLinkNode = (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkNode->init();
				pStTarAziLinkNode->pMissileData = missileData;
				pQFile->read((char*)pStTarAziLinkNode->pMissileData->x, sizeof(float)*pointNum);
				pQFile->read((char*)pStTarAziLinkNode->pMissileData->y, sizeof(float)*pointNum);
				pQFile->read((char*)pStTarAziLinkNode->pMissileData->z, sizeof(float)*pointNum);
				pQFile->read((char*)pStTarAziLinkNode->pMissileData->rcs, sizeof(float)*pointNum);
				pQFile->read((char*)pStTarAziLinkNode->pMissileData->phi, sizeof(float)*pointNum);
				for (int i = 0; i < pStTarAziLinkNode->pointNum; i++)
				{
					if (abs(pStTarAziLinkNode->pMissileData->x[i]) > 500)
					{
						int aa = 10;
					}
				}
				pStTarAziLinkNode->pointNum = pointNum;
				pStTarAziLinkNode->pit = pit;
				pStTarAziLinkNode->azi = azi;
				AddLink(pStTarPitLinkTemp->pStTarAziLink, pStTarAziLinkNode);
				is_NodeExit = true;
				break;
			}
		}
		if (is_NodeExit == false)
		{
			stTarPitLink *pStTarPitLinkNode = (stTarPitLink*)_MallocMemObj(stTarPitLink); pStTarPitLinkNode->init();
			stTarAziLink *pStTarAziLinkHead = (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkHead->init();
			pStTarPitLinkNode->pStTarAziLink = pStTarAziLinkHead;
			stTarAziLink *pStTarAziLinkNode = (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkNode->init();
			pStTarAziLinkNode->pMissileData = missileData;

			pQFile->read((char*)pStTarAziLinkNode->pMissileData->x, sizeof(float)*pointNum);
			pQFile->read((char*)pStTarAziLinkNode->pMissileData->y, sizeof(float)*pointNum);
			pQFile->read((char*)pStTarAziLinkNode->pMissileData->z, sizeof(float)*pointNum);
			pQFile->read((char*)pStTarAziLinkNode->pMissileData->rcs, sizeof(float)*pointNum);
			pQFile->read((char*)pStTarAziLinkNode->pMissileData->phi, sizeof(float)*pointNum);
			for (int i = 0; i < pStTarAziLinkNode->pointNum; i++)
			{
				if (abs(pStTarAziLinkNode->pMissileData->x[i]) > 500)
				{
					int aa = 10;
				}
			}
			pStTarAziLinkNode->pointNum = pointNum;
			pStTarAziLinkNode->pit = pit;
			pStTarAziLinkNode->azi = azi;
			AddLink(pStTarPitLinkNode->pStTarAziLink, pStTarAziLinkNode);
			AddLink(pStTarPitLinkHead, pStTarPitLinkNode);
		}
	}
	pQFile->close();
	_ReleaseMemObj(stRCS_FIlePara, pStRCS_FIlePara);

	return pStTarPitLinkHead;
}
