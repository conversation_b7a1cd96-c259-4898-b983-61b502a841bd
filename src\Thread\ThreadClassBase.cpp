/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassBase.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassBase.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassBase.h"
#include<QThread>
#include<QFile>
#include <QDateTime>
#include <QDebug>

#include "WhiteCalGlobel.h"
#include "ThreadClassSendWaveGen.h"

#include <cuda_runtime_api.h>


ThreadClassBase::ThreadClassBase(QObject *parent) : QObject(parent)
{
	SetMemPoolSrcFileName((char*)"ThreadClassBase", sizeof("ThreadClassBase"));
	_DefineMemPool(stSarEchoPara, 100);
	_DefineMemPool(stClutterEchoPara, 20);
	_DefineMemPool(stJamEchoPara,20);
	_DefineMemPool(stRadarAPC, 100);
    _DefineMemPool(stWhitePara,20);

	stSarEchoPara *po = _MallocMemObj(stSarEchoPara);    
    m_Tar_Rcs_Pos = nullptr;
    m_Tar_Rcs_Pos = (char*)malloc(1024);
    memset(m_Tar_Rcs_Pos,0,1024);
    memcpy(m_Tar_Rcs_Pos,"..\\bin\\Tar_Rcs_Pos",sizeof("..\\bin\\Tar_Rcs_Pos"));

    m_TarNumSar = 0;

	m_SimData		= (SimStruct *)malloc(sizeof(SimStruct));
	m_ParamR		= (RADAR *)malloc(sizeof(RADAR));
	m_RadarPos		= (PLATFORM *)malloc(sizeof(PLATFORM));

	m_JamPlatForm	= (PLATFORM *)malloc(sizeof(PLATFORM) * 8);
	m_Jammer		= (JAM_PARAM *)malloc(sizeof(JAM_PARAM) * 8);
	m_Rcs			= (RCS *)malloc(sizeof(RCS) * 8);

	m_Target		= (PLATFORM *)malloc(sizeof(PLATFORM) * 256);
	m_SourceRadar	= (RADAR *)malloc(sizeof(RADAR) * 256);
	m_AntParam		= (ANTPARAM *)malloc(sizeof(ANTPARAM));
	m_cfgPara		= (CfgPara *)malloc(sizeof(CfgPara));

	memset(m_SimData, 0, sizeof(SimStruct));
	memset(m_ParamR, 0, sizeof(RADAR));
	memset(m_RadarPos, 0, sizeof(PLATFORM));

	memset(m_JamPlatForm, 0, sizeof(PLATFORM) * 8);
	memset(m_Jammer, 0, sizeof(JAM_PARAM) * 8);
	memset(m_Rcs, 0, sizeof(RCS) * 8);

	memset(m_Target, 0, sizeof(PLATFORM) * 256);
	memset(m_SourceRadar, 0, sizeof(RADAR) * 256);
	memset(m_AntParam, 0, sizeof(ANTPARAM));

	pThreadClassAntennaGen = new ThreadClassAntennaGen;
	pThreadClassAntennaGen->InitDeviceWorkPara(0, 512 * 1024 * 1024.f, nullptr);

	m_SenceTarget = nullptr;
	m_FrameSerial = 0;

	//天线参数 to do 
	ParaAntSet();
	ParaSimSet();
	
	//面目标杂波 wyz
	ParaSarEcho((double*&)m_SenceTarget);

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassBase::~ThreadClassBase()
{

}

//仿真参数配置
void ThreadClassBase::ParaSimSet()
{
	RADAR *ParamR		= m_ParamR;
	PLATFORM *RadarPos	= m_RadarPos;			//雷达位置   弹头最多8个


	SimStruct *SimData = m_SimData;//仿真参数

	SimData->JamNum			= 1;			//干扰源个数
	//SimData->SenceTarNum = 2;		//面目标个数,362
	SimData->CultterType	= 1;		//实时模型杂波，-1不仿真杂波,0-6对应着道格拉斯海情等级（不开放0级海情)
	SimData->StatClutterMod = 1;	//0无杂波;1仿真K分布海杂波;2仿真韦伯分布海杂波;3仿真对数正态分布海杂波;4瑞丽分布分布海杂波
	SimData->SARImagFlag	= 1;		//SAR成像标志，0不做SAR成像，1单基地SAR成像
	SimData->TransLossFlag	= 0;		//传输损耗计算标志，0电压值进行量化，1功率值进行量化（默认为电压值量化）
	SimData->SaveFlie_Nr	= 8192;		//需要保存的帧信号长度,2的幂,且SaveFlie_Nr>=Tp*Fs（脉冲体制），搜索8192，截获2048
    SimData->SaveFlie_Na	= 128;	//需要保存的信号帧数,2的幂2048,搜索模式为单个波位的MTD快拍数，截获模式为子距离门个数*距离门宽度

	//杂波参数
	//-----------绘制各类概率密度函数
	SimData->spectrum_way = 1;
	SimData->f3dB = 10e3; //杂波谱半带宽
	SimData->mu = 1;
	SimData->sigma_m = 0.8; //中值
	SimData->Shape = 0.8;
	SimData->Scale = 1.5;
	SimData->viu = 0.5;
	SimData->StatClutterMod = 4;//0无杂波;1仿真K分布海杂波;2仿真韦伯分布海杂波;3仿真对数正态分布海杂波;4瑞丽分布分布海杂波
	SimData->Fs = 300e3;

	//ParamR->Na  = int(2*SimData->Rmin/C*ParamR->Prf + 1) + SimData->SaveFlie_Na;		//信号帧数 + Rmin对应的Na
	ParamR->Na					= SimData->SaveFlie_Na;		//信号帧数 + Rmin对应的Na
	SimData->RangeGateMode		= 0;			//距离门选通模式，0固定距离门，1可变距离门
	SimData->RangeGateRegion	= (9000) / ParamR->Fs;	//可变距离门选通范围(s),45(2048)(不可大于1/PRF，即一个脉冲的时间)
	SimData->RangeGateWidth		= 8192 / ParamR->Fs;	//可变距离门宽度(s)总的距离门点数需要为2的幂次方
	SimData->SarchNum			= 64;				//可变距离门驻留次数
	int RangeGateNum			= int(SimData->RangeGateRegion / SimData->RangeGateWidth + 1);		//计算子距离门个数
	if (fabs(SimData->RangeGateRegion / SimData->RangeGateWidth - int(SimData->RangeGateRegion / SimData->RangeGateWidth)) < MINEQUERR)
		RangeGateNum = int(SimData->RangeGateRegion / SimData->RangeGateWidth + MINEQUERR);  //当两个double型数刚好可以整除时

	if (SimData->RangeGateMode == 1)
	{
		SimData->SaveFlie_Nr = SimData->RangeGateWidth * ParamR->Fs;		//仅在距离门点数为2的幂次方时成立(GateSampleNum)
		SimData->SaveFlie_Na = RangeGateNum*SimData->SarchNum;
		ParamR->Na = SimData->SaveFlie_Na;
	}

	//************雷达位置**************//
	RadarPos->NorthPos	= -6128.4;	    //北向位置
	RadarPos->SkyPos	= 6000;		//天向位置,6000
	RadarPos->EastPos	= -5142.3;	//东向位置,-100000
	RadarPos->NorthVel	= 500;		//北向速度,1000

	//RadarPos->NorthPos = 0;	    //北向位置
	//RadarPos->SkyPos = 2000;		//天向位置,6000
	//RadarPos->EastPos = 3925;	//东向位置,-100000
	//RadarPos->NorthVel = 50;		//北向速度,1000

	RadarPos->SkyVel	= 0;		//天向速度,-200
	RadarPos->EastVel	= 0;		//东向速度
	RadarPos->NorthAcc	= 0;		//北向加速度
	RadarPos->SkyAcc	= 0;		//天向加速度
	RadarPos->EastAcc	= 0;		//东向加速度
	//************雷达位置**************//

	//************参数自动计算*************//
	//计算弹幕距离矢量
	SimData->Radar2Target.x = 0 - RadarPos->NorthPos;		//弹目距离矢量（目标-雷达）即设置波束指向，并认为指向不变，500
	SimData->Radar2Target.y = 0 - RadarPos->SkyPos;
	SimData->Radar2Target.z = 0 - RadarPos->EastPos;
	//Rmin解算
	double R0;
	R0 = sqrt(SimData->Radar2Target.x*SimData->Radar2Target.x + SimData->Radar2Target.y*SimData->Radar2Target.y + SimData->Radar2Target.z*SimData->Radar2Target.z);
	//SimData->Rmin = R0 - C/2*(SimData->SaveFlie_Nr/(2*ParamR->Fs) - ParamR->Tp/2);    //搜索模式适用
	SimData->Rmin = R0 - C_ / 2 * (SimData->SaveFlie_Nr / (2 * ParamR->Fs));    //脉压结果居中
	////to do
	//SimData->Rmin = 1200;
	//计算Fdc
	SimData->Fdc = 2 * (RadarPos->NorthVel*SimData->Radar2Target.x + RadarPos->SkyVel*SimData->Radar2Target.y +
		RadarPos->EastVel*SimData->Radar2Target.z) / R0 / ParamR->Lambda;    //矫正用得Fdc，并不是目标的Fdc，默认是求取波束中心处的多普勒
	if (SimData->SARImagFlag == 1)
		SimData->Fdc = 0;
}
//雷达参数配置
void ThreadClassBase::ParaRadarSet()
{
	int idx = 0;
	//***********最多有8个目标、干扰*******//
	RADAR		*ParamR		= m_ParamR;

	ParamR->SignalType	= Chirp;				//发射信号类型,0,Pulse,1,Chirp,3,BPSK中三选一,SinCos,Sawtooth
	ParamR->TriggerType = ConstantPRF;			//触发器类型0恒定重频ConstantPRF、2重频抖动JitteringPRF、1重频参差StaggeredPRF

	ParamR->Amp			= 10000;				//发射信号功率,10e3
	ParamR->Fc			= 16e9;					//载频(KU波段12-18Ghz)
	ParamR->F0			= 10e6 * 1;
	ParamR->Lambda		= C_ / ParamR->Fc;		//波长
	ParamR->RadomeLoss	= pow(10.0, 0 / 10.0);	//天线罩损耗（dB）
	ParamR->Prf			= 10e3;					//脉冲重复频率,搜索1e3，截获5e3
	ParamR->Fs			= 300e6;				//采样率(论文中20e6)
	ParamR->Tp			= 4e-6;					//时宽，chirp时参考值4e-6，pulse时参考值4e-8
	ParamR->Br			= 100e6;					//带宽,当为Pulse和BPSK时，Br=1.0/ParamR->Tp,30e6
	ParamR->Kr			= ParamR->Br / ParamR->Tp;				//调频斜率
	ParamR->Nr			= 16384;// ((int)(ParamR->Fs / ParamR->Prf / 2.0 + 0.5)) * 2;		//一帧信号长度

	ParamR->CodeTr		= 1e-7;				//码元宽度
	ParamR->CodeLen		= 5;				//几元巴克码(2,3,4,5,7,11,13)

	//变PRF参数设置
	ParamR->StagNum		= 1;				//区分组内抖动和组间抖动，若为1，则为组内参差，若大于1，则为组间参差，必须为整数，且大于0
	//重频参差
	ParamR->PRIMin		= 0.04e-3;			//最小参差PRT（即PRF=25e3）
	ParamR->FreamNum	= 8;				//帧周期的脉冲数（仅可选8 9 12 14）
	//重频抖动
	ParamR->BasePRI		= 0.04e-3;			//脉冲重复周期的基准（即PRF=25e3）
	ParamR->JitGama		= 0.5;				//最大抖动范围，0.005

	//捷变信号
	ParamR->StepType			= 1;		//捷变频信号类型，0正弦捷变频信号，1步进频率信号，2伪随机捷变频信号(只开放1)
	ParamR->StepNum				= 6;		//频率步进个数
	ParamR->NumTheSameFrequency = 1;		//脉组捷变中同一频率的脉冲个数
	ParamR->StepFrequency		= 1e6;		//频率步进量

	//锯齿调制连续波雷达信号
	ParamR->SawModuleBr			= 30e6;		//调制带宽
	ParamR->SawModuleTime		= 4e-6;		//调制周期

}
//被动辐射源
void ThreadClassBase::ParaSourceSet()
{
	double SimTime = 64e-3;   //仿真时间，杂波0.012,0.1
	int idx = 0;
	//*********被动辐射源信号**********//
	SimStruct	*SimData = m_SimData;	//仿真参数
	RADAR *SourceRadar = m_SourceRadar;
	for (idx = 0; idx < SimData->RadarNum; idx++)
	{
		SourceRadar[idx].Delay		= 1e-6;			//延时
		SourceRadar[idx].Amp		= 1;			//幅度
		SourceRadar[idx].Fc			= 10e6;			//载频
		SourceRadar[idx].Lambda		= C_ / SourceRadar[idx].Fc;	//波长
		SourceRadar[idx].Prf		= 10e3;			//脉冲重复频率
		SourceRadar[idx].Br			= 100e6;		//带宽
		SourceRadar[idx].Tp			= 7e-6;			//时宽	
		SourceRadar[idx].Kr			= SourceRadar[idx].Br / SourceRadar[idx].Tp;		//调频斜率
		SourceRadar[idx].TriggerType	= JitteringPRF;//触发器类型,ConstantPRF = 0,StaggeredPRF,JitteringPRF,SlipPRF
		SourceRadar[idx].SignalType		= Pulse;	//雷达信号类型Pulse = 0,Chirp,FSK,BPSK,QPSK,SinCos
		SourceRadar[idx].FskNum		= 2;
		SourceRadar[idx].FSK_FC[0]	= 410e6;	//2FSK点频
		SourceRadar[idx].FSK_FC[1]	= 440e6;	//2FSK点频
		SourceRadar[idx].FSK_FC[2]	= 430e6;	//2FSK点频
		SourceRadar[idx].FSK_FC[3]	= 440e6;	//2FSK点频

		//参差PRT模型
		SourceRadar[idx].PRIMin		= 1e-4;		//最小的脉冲重复周期
		SourceRadar[idx].StagNum	= 1;		//区分组内参差和组间参差，若为1，则为组内参差，若大于1，则为组间参差，必须为整数，且大于0
		SourceRadar[idx].FreamNum	= 8;		//帧周期的脉冲数
		//抖动PRF模型
		SourceRadar[idx].BasePRI	= 1e-4;	//脉冲重复周期的基准
		SourceRadar[idx].JitGama	= 0.3;	//最大抖动范围
		//SourceRadar[idx]->JitNum = 2;
		//重频滑动模型
		//SourceRadar[idx]->SlipNum = 4;	//最大滑动范围
		SourceRadar[idx].SlipGama	= 0.2;	//最大滑变量
		SourceRadar[idx].CicleTime	= 0.02;	//滑变周期
		SourceRadar[idx].CicleTime	= 1 / SourceRadar[idx].Prf;//滑变周期


		if (SourceRadar[idx].TriggerType == ConstantPRF)		//恒定重频
		{
			SourceRadar[idx].Na = (int)(SimTime*SourceRadar[idx].Prf);		//信号帧数
		}
		else if (SourceRadar[idx].TriggerType == StaggeredPRF)	//重频参差
		{
			SourceRadar[idx].Na = (int)(SimTime / SourceRadar[idx].PRIMin);	//信号帧数
		}
		else if (SourceRadar[idx].TriggerType == JitteringPRF)	//重频抖动
		{
			SourceRadar[idx].Na = (int)(SimTime / SourceRadar[idx].BasePRI*1.2);	//信号帧数
		}
		else if (SourceRadar[idx].TriggerType == SlipPRF)		//重频滑动
		{
			SourceRadar[idx].Na = (int)(SimTime*SourceRadar[idx].Prf*1.2);	//信号帧数
		}

		//BPSK信号
		SourceRadar[idx].CodeTr		= 0.1e-6;	//码元宽度
		SourceRadar[idx].CodeLen	= 7;		//码元数
		//SourceRadar[idx]->BPSKFc = 10e3;	//BPSK的中频
	}
	SourceRadar[0].SignalType	= Pulse;			//雷达信号类型Pulse = 0,Chirp,FSK,BPSK,QPSK,SinCos
	SourceRadar[0].Fc			= 400e6;					//雷达信号类型Pulse = 0,Chirp,FSK,BPSK,QPSK,SinCos
	SourceRadar[0].Prf			= 20e3;					//雷达信号类型Pulse = 0,Chirp,FSK,BPSK,QPSK,SinCos
	SourceRadar[0].TriggerType	= ConstantPRF;	//触发器类型,ConstantPRF = 0,StaggeredPRF,JitteringPRF,SlipPRF

	SourceRadar[1].SignalType	= Chirp;			//雷达信号类型Pulse = 0,Chirp,FSK,BPSK,QPSK,SinCos
	SourceRadar[1].Fc			= 900e6;					//雷达信号类型Pulse = 0,Chirp,FSK,BPSK,QPSK,SinCos
	SourceRadar[1].Prf			= 20e3;					//雷达信号类型Pulse = 0,Chirp,FSK,BPSK,QPSK,SinCos
	SourceRadar[1].TriggerType	= ConstantPRF;	//触发器类型,ConstantPRF = 0,StaggeredPRF,JitteringPRF,SlipPRF
	//*********被动辐射源信号**********//
}
//天线参数
void ThreadClassBase::ParaAntSet()
{
	SimStruct	*SimData	= m_SimData;	//仿真参数
	RADAR		*ParamR		= m_ParamR;
	//**********天线**************//
	ANTPARAM *AntParam			= m_AntParam;	//天线参数
	AntParam->AntennaType		= Line;			//线阵天线Line，矩形阵列RectAngle
	AntParam->SubAntType		= ArryElement;	//阵元和子阵 ArryElem；SubArray 

	AntParam->AntGain			= 30;				//天线增益，单位dB
	

	//和差差天线
	SimData->AziEleFalg = 2;	//0则单波束，1则和差差波束(比相)，2和差差波束（比幅）
	AntParam->AziOverlap = 4;// 10 * PI / 180;		//和差差天线 - 方位方向重叠波束（比幅） 单位：度
	AntParam->EleOverlap = 4;// 10 * PI / 180;		//和差差天线 - 俯仰方向重叠波束（比幅） 单位：度
	//AntParam->AziSlope		= 2 / AntParam->AziOverlap;			//和差差天线 - 方位方向差斜率(比幅)
	//AntParam->EleSlope		= 2 / AntParam->EleOverlap;			//和差差天线 - 俯仰方向差斜率(比幅)

	AntParam->Main3dBlobeWidth[0]		= 6;			   // 主瓣3dB宽度 ，2维，方位及俯仰，单位：度
	AntParam->Main3dBlobeWidth[1]		= 6;
	AntParam->MainlobeZeroPos[0]		= AntParam->Main3dBlobeWidth[0] / 2.0 * 3;			   //主瓣零点位置，2维，方位及俯仰，单位：度
	AntParam->MainlobeZeroPos[1]		= AntParam->Main3dBlobeWidth[1] / 2.0 * 3;
	AntParam->SidelobeMaxPos[0]			= AntParam->Main3dBlobeWidth[0] / 2.0 * 5;				 //第一旁瓣最大值位置，2维，方位及俯仰，单位：度
	AntParam->SidelobeMaxPos[1]			= AntParam->Main3dBlobeWidth[1] / 2.0 * 5;
	AntParam->AntenMainAndSideGain[0]	= 55;		 //天线方向图功率增益，输入1行4列数组(主瓣最大增益、主瓣零点增益、第一旁瓣中心增益、平均旁瓣增益)，单位：dB
	AntParam->AntenMainAndSideGain[1]	= 36;
	AntParam->AntenMainAndSideGain[2]	= 45;
	AntParam->AntenMainAndSideGain[3]	= 32;
	AntParam->AngleBound[0]				= 120;// 180;                // 天线方向图输出角度范围，2维，方位及俯仰，单位：度
	AntParam->AngleBound[1]				= 120;//180;
	AntParam->AntAziBound				= 120;// AntParam->AngleBound[0] / 180 * PI;		//仿真的行角度范围
	AntParam->AntEleBound				= 120;// AntParam->AngleBound[1] / 180 * PI;		//仿真的列角度范围

	AntParam->AngSampSpace[0]			= 0.05;		     // 在AngleBound范围内的方向图采样间隔，2维，方位及俯仰，单位：度
	AntParam->AngSampSpace[1]			= 0.05;

	AntParam->AntennaMode				= 0;				//天线模型，提供了‘Sinc’,'拟合Sinc','Gauss', 'Cos', '余割平方'
	AntParam->OverlapCoef[0]			= 0.15;         //波束重叠因子    
	AntParam->OverlapCoef[1]			= 0.15;

	AntParam->AziSpace = AntParam->AngSampSpace[0];
	AntParam->EleSpace = AntParam->AngSampSpace[1];

	AntParam->AntFuncAziNum = (int)(2 * AntParam->AngleBound[0] / AntParam->AngSampSpace[0] + 0.5) + 1;	//方位向点数
	AntParam->AntFuncEleNum = (int)(2 * AntParam->AngleBound[1] / AntParam->AngSampSpace[1] + 0.5) + 1;	//俯仰向点数

	AntParam->SumAntennaFunction = (float*)malloc(sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum);	//方向图初始化
	AntParam->AziSubAntennaFunction = (float*)malloc(sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum);
	AntParam->PitSubAntennaFunction = (float*)malloc(sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum);
	AntParam->AntPos = (Complex*)malloc(sizeof(Complex)*AntParam->ArrayNumRow*AntParam->ArrayNumColumn);
	AntParam->SubAntPos = (Complex*)malloc(sizeof(Complex)*AntParam->SubArrayNumRow*AntParam->SubArrayNumColumn);

	memset(AntParam->SumAntennaFunction, 0, sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum);
	memset(AntParam->AziSubAntennaFunction, 0, sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum);
	memset(AntParam->PitSubAntennaFunction, 0, sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum);

	memset(AntParam->SumAntennaFunction, 0, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum);

	//**********天线**************//
	//天线方向图生成
	ParamR->Fc = 17e9;
	SimData->AziEleFalg = 2;
	pThreadClassAntennaGen->AntennaSimRe(ParamR, AntParam, SimData);

	QFile *pQFile = new QFile;
	pQFile->setFileName("..\\..\\..\\rcs\\ANT");
	if (pQFile->open(QFile::ReadOnly))
	{
		pQFile->read((char*)AntParam->SumAntennaFunction, 4801 * 4801 * sizeof(float));
		pQFile->read((char*)AntParam->AziSubAntennaFunction, 4801 * 4801 * sizeof(float));
		pQFile->read((char*)AntParam->PitSubAntennaFunction, 4801 * 4801 * sizeof(float));
		pQFile->close();
	}
	delete pQFile;

	AntParam->AziSlope = 0.0693;
	AntParam->EleSlope = 0.0693;
}
//天线参数
void ThreadClassBase::ParaAntSetRealTime()
{
	SimStruct	*SimData = m_SimData;	//仿真参数
	RADAR		*ParamR = m_ParamR;
	//**********天线**************//
	ANTPARAM *AntParam = m_AntParam;	//天线参数
	//AntParam->AntennaType = RectAngle;			//线阵天线Line，矩形阵列RectAngle
	//AntParam->SubAntType = ArryElement;	//阵元和子阵 ArryElem；SubArray 
	//AntParam->ArrayNumRow = 32;				//行阵元个数（线阵则表示阵元数目）,4
	//AntParam->ArrayNumColumn = 32;			//列阵元个数（线阵无用），8
	//AntParam->AntSpaceRow = C_ / 2 / ParamR->Fc;	//阵元模式-行阵元间距，
	//AntParam->AntSpaceColumn = C_ / 2 / ParamR->Fc;//阵元模式-列阵元间距，子阵模式-子阵间距
	//AntParam->InitialePhase = 0;			//第一个阵元的初始相位
	//AntParam->AntFuncAziNum = 201;			//仿真天线方向图方位向（行）的点数,201
	//AntParam->AntFuncEleNum = 201;			//仿真天线方向图俯仰向（列）的点数,201
	//AntParam->AntAziBound = 2 * PI / 3.0;		//仿真的行角度范围
	//AntParam->AntEleBound = 2 * PI / 3.0;		//仿真的列角度范围
	//AntParam->AntGain = 30;				//天线增益，单位dB
	

	//AntParam->SubArrayNumRow = 2;		//子阵-行子阵个数（线阵则表示阵元数目）
	//AntParam->SubArrayNumColumn = 12;		//子阵-列子阵个数（线阵无用）
	//AntParam->SubArraySpcaeRow = 50 * C_ / 2 / ParamR->Fc;	//子阵行间距，N个半波长
	//AntParam->SubArraySpcaeColumn = 50 * C_ / 2 / ParamR->Fc;	//子阵列间距，N个半波长

	//if (ArryElement == AntParam->SubAntType)
	//{
	//	AntParam->AziSpace = (AntParam->ArrayNumRow - 1) / 2.0*(C_ / ParamR->Fc / 2);		//和差差天线 - 方位方向间距
	//	AntParam->EleSpace = (AntParam->ArrayNumColumn - 1) / 2.0*(C_ / ParamR->Fc / 2);		//和差差天线 - 俯仰方向间距	
	//}
	//else
	//{
	//	AntParam->AziSpace = (AntParam->SubArrayNumRow - 1) / 2.0*AntParam->SubArraySpcaeRow;	//差波束阵元间距-行
	//	AntParam->EleSpace = (AntParam->SubArrayNumColumn - 1) / 2.0*AntParam->SubArraySpcaeRow;	//差波束阵元间距-列
	//}
	//AntParam->AziOverlap = 10 * PI / 180;		//和差差天线 - 方位方向重叠波束（比幅）
	//AntParam->EleOverlap = 10 * PI / 180;		//和差差天线 - 俯仰方向重叠波束（比幅）
	//AntParam->AziSlope = 2 / AntParam->AziOverlap;			//和差差天线 - 方位方向差斜率(比幅)
	//AntParam->EleSlope = 2 / AntParam->EleOverlap;			//和差差天线 - 俯仰方向差斜率(比幅)

	if (AntParam->Omiga == nullptr)
	{
		AntParam->Omiga				= (Complex*)malloc(sizeof(Complex)*AntParam->ArrayNumRow*AntParam->ArrayNumColumn);
		AntParam->SubOmiga			= (Complex*)malloc(sizeof(Complex)*AntParam->SubArrayNumRow*AntParam->SubArrayNumColumn);
        AntParam->SumAntennaFunction	= (float*)malloc(sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum);	//方向图初始化
		AntParam->AziSubAntennaFunction = (float*)malloc(sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum);
		AntParam->PitSubAntennaFunction = (float*)malloc(sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum);
		AntParam->AntPos			= (Complex*)malloc(sizeof(Complex)*AntParam->ArrayNumRow*AntParam->ArrayNumColumn);
		AntParam->SubAntPos			= (Complex*)malloc(sizeof(Complex)*AntParam->SubArrayNumRow*AntParam->SubArrayNumColumn);
        memset(AntParam->SumAntennaFunction, 0, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum);
		memset(AntParam->AziSubAntennaFunction, 0, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum);
		memset(AntParam->PitSubAntennaFunction, 0, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum);
		//**********天线**************//
	}
	AntParam->AntennaType = RectAngle;
	AntParam->AntFuncAziNum = 201;
	AntParam->AntFuncEleNum = 201;
	//天线方向图生成
	cudaError_t Err = cudaGetLastError();
	pThreadClassAntennaGen->AntennaSimRe(ParamR, AntParam, SimData);
	Err = cudaGetLastError();
	//和差差天线
	SimData->AziEleFalg = 2;	//0则单波束，1则和差差波束(比相)，2和差差波束（比幅）
}
//干扰参数配置
void ThreadClassBase::ParaJamSet()
{
	int idx = 0;
	//***********最多有8个目标、干扰*******//
	RADAR *ParamR			= m_ParamR;
	SimStruct *SimData		= m_SimData;		//仿真参数
	PLATFORM *JamPlatForm	= m_JamPlatForm;	//干扰运动参数
	JAM_PARAM *Jammer		= m_Jammer;			//干扰源参数

	//**********干扰**********//
	for (idx = 0; idx < SimData->JamNum; idx++)
	{
		JamPlatForm[idx].NorthPos	= 0;			//北向位置，50000（压制式）155.86105820148623
		JamPlatForm[idx].SkyPos		= 0;			//天向位置,0	10.496116283237825
		JamPlatForm[idx].EastPos	= 50;			//东向位置,50000（压制式）-10016.376281235194
		JamPlatForm[idx].NorthVel	= 0;			//北向速度
		JamPlatForm[idx].SkyVel		= 0;			//天向速度
		JamPlatForm[idx].EastVel	= 0;			//东向速度，100
		JamPlatForm[idx].NorthAcc	= 0;			//北向加速度
		JamPlatForm[idx].SkyAcc		= 0;			//天向加速度
		JamPlatForm[idx].EastAcc	= 0;			//东向加速度
		//JamPlatForm[idx]->MoveType = ConicalMove;	//Static = 0,RadialMove,ConicalMove

		//******************系统噪声*****************//
		//Jammer[idx].NoiesType = 1;	//0功率,1热噪声
		//Jammer[idx].NoisePower = 0;	//输出功率,1(功率)
		//Jammer[idx].ReceiverBand = 150e6;	//接收机带宽（热噪声）
		//Jammer[idx].Temperature = 27+273;	//温度，按照此参数热噪声方差很低（热噪声）；27+273
		//Jammer[idx].SNR = 10;			//信噪比
		//******************系统噪声*****************//

		//******************干扰设置*****************//
		Jammer[idx].Gain		= 40;			//干扰机增益（dB），30
		Jammer[idx].JamPower	= 1e-3 / (4 * PI);	//干扰机功率(W)(压制式)
		//干扰类型选择
		Jammer[idx].JamType		= 0;		//0无源干扰,1压制干扰,2欺骗干扰
		Jammer[idx].BalJamType	= 0;		//（压制干扰子项）0连续噪声干扰Block，1扫频噪声干扰Sweep，2间断噪声干扰PulseJam，3梳状谱噪声干扰CombSpec
		Jammer[idx].PullOffType = 1;		//（欺骗干扰子项）1距离门拖引，2速度门拖引，3密集假目标干扰，4频移干扰，5前沿复制干扰，6间隔采样转发干扰，7多普勒闪烁，8灵巧噪声,9随机假目标,10多普勒噪声干扰
		Jammer[idx].PassiveType = 0;	//（无源干扰子项）0箔条，1方形反射器，2圆形反射器，3三角反射器
		Jammer[idx].SideLen		= 0.5;		//边长（无源干扰子项除开箔条有效时需设定）

		//Jammer[idx].Power = 1;		//干扰机功率(未使用)
		//Jammer[idx].SJRID = 1;		//信噪比/干信比关联的目标序号（序号从0开始）
		//Jammer[idx].SJR = -10;		//干信比

		//******************箔条干扰*****************//
		Jammer[idx].ChaffNum	= 5e6;		//箔条数
		Jammer[idx].ChaffPos[0] = 1200;	//箔条云中心-北-天-东
		Jammer[idx].ChaffPos[1] = 100;
		Jammer[idx].ChaffPos[2] = 0;
		Jammer[idx].ChaffVel[0] = 10;	//箔条云-风速-北-天-东
		Jammer[idx].ChaffVel[1] = 20;
		Jammer[idx].ChaffVel[2] = 0;
		Jammer[idx].DiffuseRan	= 15;	//初始扩散范围（m）
		Jammer[idx].AveRcs		= ParamR->Lambda*ParamR->Lambda / 4.0;		//箔条平均RCS(此处lambda应该为射频的波长！！)

		//******************压制干扰*****************//
		//压制式噪声公用参数部分（连续，间断，扫频）
		Jammer[idx].DeltaFc		= 0e6;		//载频偏置（连续，间断，扫频）
		Jammer[idx].GaussType	= 0;		//0高斯，1高斯调频(连续，间断)
		Jammer[idx].Km			= 1e6;			//噪声调频的调频斜率（指定为高斯调频噪声时设置）
		Jammer[idx].FreqVar		= 100e12;	//噪声调频的频率范围（指定为高斯调频噪声时设置）
		Jammer[idx].Br			= 10e6;			//带宽（指定为高斯时噪声设置）
		Jammer[idx].AmpVar		= 4;			//高斯噪声方差（指定为高斯时噪声设）
		//扫频噪声干扰
		Jammer[idx].SweepType	= SawtoothSweep; 	//0三角波TriangleSweep、1正弦波CosSweep、2锯齿波SawtoothSweep
        //Jammer[idx].SweepTr		= 2e-6;		//扫频周期,2e-6
		//间断噪声干扰
		Jammer[idx].PulJamType	= RanPulse;  //0同步脉冲SynPulse，1杂乱脉冲RanPulse
		Jammer[idx].PRF			= 25e3;		//PRF, 200e3
		Jammer[idx].DutyCycle	= 0.2;	//占空比,0.2
		//梳状谱噪声干扰
        //Jammer[idx].FreqNum			= 4;    //梳状谱频点个数
        //Jammer[idx].FreqSequency	= (double*)malloc(sizeof(double)*Jammer[idx].FreqNum);
        //Jammer[idx].FreqSequency[0] = 1e6;		// 不建议超过fs/2
        //Jammer[idx].FreqSequency[1] = 50e6;
        //Jammer[idx].FreqSequency[2] = 80e6;
        //Jammer[idx].FreqSequency[3] = 100e6;
        //Jammer[idx].BrSequency		= (double*)malloc(sizeof(double)*Jammer[idx].FreqNum);
        //Jammer[idx].BrSequency[0]	= 1e6;
        //Jammer[idx].BrSequency[1]	= 2e6;
        //Jammer[idx].BrSequency[2]	= 1.8e6;
        //Jammer[idx].BrSequency[3]	= 6e6;
        //Jammer[idx].JamAmp			= 100;
		//******************压制干扰*****************//

		//******************欺骗干扰*****************//
		//Jammer[idx].Delay = 0;		//转发延时
		Jammer[idx].StartTime		= 0;		//欺骗干扰仿真开始时间
		Jammer[idx].StopTime		= 1;		//欺骗干扰仿真结束时间
		//距离拖引相关设置参数
		Jammer[idx].PullOffRan		= 600;	//拖引目标初始斜距，10
		Jammer[idx].PullOffVel		= 0;	//拖引目标速度，100，100000仿真时效果明显
		Jammer[idx].PullOffAcc		= 0;		//拖引目标加速度，0
		//速度拖引相关设置参数
		Jammer[idx].DeltaDop		= 400;		//固定多普勒拖引速度，400
        //Jammer[idx].DopRate			= 0;		//多普勒拖引变化率，100000仿真时效果明显
		//密集多假目标设置参数
		Jammer[idx].MulTarNum		= 20;		//密集多目标个数
		Jammer[idx].MulPullOffRan	= (double*)malloc(sizeof(double)*Jammer[idx].MulTarNum);	//初始拖引目标距离序列
		Jammer[idx].MulPullOffVel	= (double*)malloc(sizeof(double)*Jammer[idx].MulTarNum);	//拖引目标速度序列
		Jammer[idx].MulPullOffAcc	= (double*)malloc(sizeof(double)*Jammer[idx].MulTarNum);	//拖引目标加速度序列
		Jammer[idx].MulDeltaDop		= (double*)malloc(sizeof(double)*Jammer[idx].MulTarNum);	//多普勒拖引速度序列,11,22
        //Jammer[idx].MulDopRate		= (double*)malloc(sizeof(double)*Jammer[idx].MulTarNum);    //多普勒拖引率序列,500,300
		for (int idzzz = 0; idzzz < Jammer[idx].MulTarNum; idzzz++)
		{
			Jammer[idx].MulPullOffRan[idzzz]	= 200 + idzzz * 15;//113
			Jammer[idx].MulPullOffVel[idzzz]	= 0 + ((double)rand()) / RAND_MAX * 3;//2
			Jammer[idx].MulPullOffAcc[idzzz]	= 0;
			Jammer[idx].MulDeltaDop[idzzz]		= 0;
            //Jammer[idx].MulDopRate[idzzz]		= 0;
		}
		//Jammer[idx].MulPullOffAcc[1] = 0;
		//Jammer[idx].MulDeltaDop[1] = 0;
		//Jammer[idx].MulDopRate[1] = 0;
		//频移干扰设置参数
		Jammer[idx].FreqShift		= 5e6;		//频移干扰频移量
		//前沿复制+切片复制干扰
		Jammer[idx].FrontCopyTimes	= 4;		//转发复制信号次数,前沿预设为3，若为脉内等间隔采样则为6
		Jammer[idx].FrontDelay		= 0e-6;		//干扰机转发延时
        //Jammer[idx].FrontLen		= 1e-6;	//脉冲切片宽度,若为脉内等间隔采样则为1e-6，限制FrontLen<=Tp
		//切片复制干扰
		Jammer[idx].CopyTimes		= 4;		//转发复制信号次数
		Jammer[idx].CutDelay		= 0;		//干扰机转发延时
        //Jammer[idx].CutLen			= 1e-6;	//脉冲切片宽度Tw
		Jammer[idx].CutEdge			= 1e-6;		//切片前沿起始时刻（s）
		Jammer[idx].CutPRT			= 5e-6;	//切片周期，限制：CutLen*(CopyTimes+1)<=CutPRT,Ts
		//多普勒闪烁设置参数
		Jammer[idx].FreqDopJam[0]	= 1e3;	//多普勒闪烁频率，仅可设置2个
		Jammer[idx].FreqDopJam[1]	= 2e3;

		//Jammer[idx].FreqDopJam[0] = 1000;	//多普勒闪烁频率，仅可设置2个
		//Jammer[idx].FreqDopJam[1] = 2000;

		//Jammer[idx].FreqDopJam[0] = 100;	//多普勒闪烁频率，仅可设置2个
		//Jammer[idx].FreqDopJam[1] = 200;

		//灵巧噪声设置参数
		Jammer[idx].SmartNoiseBr		= 40e6;	//视频噪声带宽(大于信号Br)
		Jammer[idx].SmartNoiseTp		= 2e-6;	//视频噪声时宽（小于信号Tp）
		Jammer[idx].SmartNoiseVar		= 100;		//视频噪声方差
		//随机假目标设置参数
		Jammer[idx].RandMulTargetNum	= 50;
		Jammer[idx].RandRangeVar		= 100;
		Jammer[idx].RandDopplerVar		= 50;
		Jammer[idx].RandRangeSeq		= (double*)malloc(sizeof(double)*Jammer[idx].RandMulTargetNum);
		Jammer[idx].RandDopplerSeq		= (double*)malloc(sizeof(double)*Jammer[idx].RandMulTargetNum);
		for (int idy = 0; idy < Jammer[idx].RandMulTargetNum; idy++)
		{
			Jammer[idx].RandRangeSeq[idy]	= randn(0, 1)*Jammer[idx].RandRangeVar;
			Jammer[idx].RandDopplerSeq[idy] = randn(0, 1)*Jammer[idx].RandDopplerVar;
		}
		//多普勒噪声干扰设置参数
		Jammer[idx].Kpm		= 1;			//多普勒噪声系数
		Jammer[idx].PhaVar	= 1;			//相位方差
		//******************欺骗干扰*****************//
	}
}

//回波生成
void ThreadClassBase::ParaSarEcho(double *&SenceTarget)
{
	int idx = 0;
	SimStruct	*SimData = m_SimData;	//仿真参数

	//*****面目标位置+RCS*****//
	int FlieLen = 0;
	//if (SenceTarget != NULL){
	//	free(SenceTarget);
	//	SenceTarget = NULL;
	//}
	//
	{
        QFile *fid_read_Pos_Rcs = new QFile;
        QString str = m_Tar_Rcs_Pos;
        fid_read_Pos_Rcs->setFileName(str);
        if(fid_read_Pos_Rcs->open(QFile::ReadOnly))
        {
            fid_read_Pos_Rcs->read((char*)&FlieLen,sizeof(int));
            double *SencePosRcs = (double*)malloc(sizeof(double)*FlieLen * TARGET_LEN);
            fid_read_Pos_Rcs->read((char*)SencePosRcs,sizeof(double)*FlieLen * TARGET_LEN);
            fid_read_Pos_Rcs->close();
            delete fid_read_Pos_Rcs;

            if (SenceTarget == NULL)
                SenceTarget = (double*)malloc(sizeof(double) * TARGET_LEN * FlieLen);
            memset(SenceTarget, 0, sizeof(double) * TARGET_LEN * FlieLen);
            for (idx = 0; idx < FlieLen; idx++)
            {
                SenceTarget[idx * TARGET_LEN + 0] = SencePosRcs[idx];				//北
                SenceTarget[idx * TARGET_LEN + 1] = SencePosRcs[idx + FlieLen];		//天
                SenceTarget[idx * TARGET_LEN + 2] = SencePosRcs[idx + FlieLen * 2];	//东
                SenceTarget[idx * TARGET_LEN + 3] = SencePosRcs[idx + FlieLen * 3];	//RCS
                SenceTarget[idx * TARGET_LEN + 4] = SencePosRcs[idx + FlieLen * 4];	//RCS
            }
            SimData->SenceTarNum = FlieLen;		//面目标个数
            m_TarNumSar = FlieLen;
        }
	}
}
void ThreadClassBase::ParaSarEcho_2(double *&SenceTarget)
{
	int idx = 0;
	SimStruct	*SimData = m_SimData;	//仿真参数

	//*****面目标位置+RCS*****//
	int FlieLen = 0;
	//if (SenceTarget != NULL){
	//	free(SenceTarget);
	//	SenceTarget = NULL;
	//}
	//
	{
        QFile *fid_read_Pos_Rcs = new QFile;
        QString str = m_Tar_Rcs_Pos;
        fid_read_Pos_Rcs->setFileName(str);
        if(fid_read_Pos_Rcs->open(QFile::ReadOnly))
        {
            fid_read_Pos_Rcs->read((char*)&FlieLen,sizeof(int));
            double *SencePosRcs = (double*)malloc(sizeof(double)*FlieLen * TARGET_LEN);
            fid_read_Pos_Rcs->read((char*)SencePosRcs,sizeof(double)*FlieLen * TARGET_LEN);
            fid_read_Pos_Rcs->close();
            delete fid_read_Pos_Rcs;

            if (SenceTarget == NULL)
                SenceTarget = (double*)malloc(sizeof(double) * TARGET_LEN * FlieLen);
            memset(SenceTarget, 0, sizeof(double) * TARGET_LEN * FlieLen);
            for (idx = 0; idx < FlieLen; idx++)
            {
                SenceTarget[idx * TARGET_LEN + 0] = SencePosRcs[idx];				//北
                SenceTarget[idx * TARGET_LEN + 1] = SencePosRcs[idx + FlieLen];		//天
                SenceTarget[idx * TARGET_LEN + 2] = SencePosRcs[idx + FlieLen * 2];	//东
                SenceTarget[idx * TARGET_LEN + 3] = SencePosRcs[idx + FlieLen * 3];	//RCS
                SenceTarget[idx * TARGET_LEN + 4] = SencePosRcs[idx + FlieLen * 4];	//phase
            }
            if(FlieLen > 512*512)
                FlieLen = 512*512;
            SimData->SenceTarNum = FlieLen;		//面目标个数
            m_TarNumSar = FlieLen;
        }
	}
}

void ThreadClassBase::slotTestRun2()
{

    
}
//测试函数
void ThreadClassBase::slotTestRun(void *p)
{
	stTestPara	*pStTestPara	= (stTestPara*)p;
	SimStruct	*SimData		= m_SimData;
	RADAR		*ParamR			= m_ParamR;
	PLATFORM	*RadarPos		= m_RadarPos;		//雷达位置   弹头最多8个

	char *pBuf1 = (char*)malloc(24 * 1024 * 1024);
	char *pBuf2 = (char*)malloc(24 * 1024 * 1024);

    //参数预加载
    if(pStTestPara->TypeSarEcho == 0)
    {
        stSarEchoPara *pStSarEchoPara = (stSarEchoPara*)_MallocMemObj(stSarEchoPara);
        //雷达参数
        ParaRadarSet();
        //仿真参数设置
        ParaSimSet();
        //天线参数
        ParaAntSet();

//		//发送参数到红方处理单元
//		stWhitePara *pStWhitePara = (stWhitePara*)_MallocMemObj(stWhitePara);
//		memcpy(&pStWhitePara->RadarParam, m_ParamR, sizeof(RADAR));
//		memcpy(&pStWhitePara->SimData, m_SimData, sizeof(SimStruct));
//		emit sendSettingSarGenImag(pStWhitePara);

        if(pStTestPara->SimMode == 0)//Sar回波
        {
            //
            ParaSarEcho(m_SenceTarget);
            pStSarEchoPara->EchoType    = 0;//0=单脉冲 1=SAR成像
            pStSarEchoPara->SenceTarget = m_SenceTarget;
            pStSarEchoPara->TarNum      = m_TarNumSar;
            pStSarEchoPara->RadarAPC    = 0;
            pStSarEchoPara->RadarVel    = 0;
            memcpy(pStSarEchoPara->AntParam, m_AntParam, sizeof(ANTPARAM));
            emit sendSettingSarSence(pStSarEchoPara);
        }
        else if(pStTestPara->SimMode == 1)//
        {

        }
        else if(pStTestPara->SimMode == 2)//
        {

        }
    }
    else if(pStTestPara->TypeSarEcho == 1)
    {
		if (pStTestPara->runState == 0)
		{
			_ReleaseMemObj(stTestPara, p);
			return;
		}
        emit sendCreateFile();

        ParamR->Na = SimData->SaveFlie_Na;		//信号帧数 + Rmin对应的Na
        int frame = ParamR->Na;
        //PRT
        double *PRI = (double*)_MallocMemObj(stRadarAPC);	//真实重复周期
        memset(PRI, 0, sizeof(double)*ParamR->Na);
        GenPRI(ParamR, PRI);		//生成变PRT序列
        double SumPRI = 0;
		qint64 t0 = QDateTime::currentMSecsSinceEpoch();
		qDebug() << "------------------------------------------------------start time " << t0;
		int countPulse = 32;
		for (int i = 0; i < frame / countPulse; i++)
        {
			ParamR->Na = SimData->SaveFlie_Na = countPulse;
            //APC生成
            double *RadarAPC = (double*)_MallocMemObj(stRadarAPC);;
            double *RadarVel = (double*)_MallocMemObj(stRadarAPC);;
            memset(RadarAPC, 0, sizeof(double)*ParamR->Na * 3);
            memset(RadarVel, 0, sizeof(double)*ParamR->Na * 3);
			PLATFORM	PlatForm[32];

            int cnt = 0;
			for (int idx = countPulse * i; idx < countPulse * i + ParamR->Na; idx++)
            {
                RadarAPC[cnt * 3 + 0] = RadarPos->NorthPos + RadarPos->NorthVel*SumPRI + 0.5*RadarPos->NorthAcc*SumPRI*SumPRI;
                RadarAPC[cnt * 3 + 1] = RadarPos->SkyPos + RadarPos->SkyVel  *SumPRI + 0.5*RadarPos->SkyAcc  *SumPRI*SumPRI;
                RadarAPC[cnt * 3 + 2] = RadarPos->EastPos + RadarPos->EastVel *SumPRI + 0.5*RadarPos->EastAcc *SumPRI*SumPRI;

                RadarVel[cnt * 3 + 0] = RadarPos->NorthVel + RadarPos->NorthAcc*SumPRI;
                RadarVel[cnt * 3 + 1] = RadarPos->SkyVel + RadarPos->SkyAcc  *SumPRI;
                RadarVel[cnt * 3 + 2] = RadarPos->EastVel + RadarPos->EastAcc *SumPRI;

				PlatForm[cnt].NorthPos	= RadarAPC[cnt * 3 + 0];
				PlatForm[cnt].SkyPos	= RadarAPC[cnt * 3 + 1];
				PlatForm[cnt].EastPos	= RadarAPC[cnt * 3 + 2];

                SumPRI += PRI[idx];
                cnt++;
            }

            if (pStTestPara->SimMode == 0) //0=回波
            {
                stSarEchoPara *pStSarEchoPara = (stSarEchoPara*)_MallocMemObj(stSarEchoPara);
                pStSarEchoPara->EchoType = 0;	//0=单脉冲 1=SAR成像
                pStSarEchoPara->SenceTarget = m_SenceTarget;
                pStSarEchoPara->RadarAPC = RadarAPC;
                pStSarEchoPara->RadarVel = RadarVel;
                memcpy(&pStSarEchoPara->SimData, m_SimData, sizeof(SimStruct));
                memcpy(pStSarEchoPara->ParamR, m_ParamR, sizeof(RADAR));
				memcpy(&pStSarEchoPara->AntParam, m_AntParam, sizeof(ANTPARAM));
				memcpy(pStSarEchoPara->PlatForm, PlatForm, sizeof(PLATFORM)*ParamR->Na);
				pStSarEchoPara->FrameSerial = i;
                emit sendCalcuteEcho(pStSarEchoPara);

            }
            if (pStTestPara->SimMode == 1) //1=杂波
            {
                stClutterEchoPara *pStClutterEchoPara = (stClutterEchoPara*)_MallocMemObj(stClutterEchoPara);
                pStClutterEchoPara->SenceTarget = m_SenceTarget;
                pStClutterEchoPara->RadarAPC = RadarAPC;
                pStClutterEchoPara->PRI = PRI;
                memcpy(&pStClutterEchoPara->RadarPos, m_RadarPos, sizeof(PLATFORM));
                memcpy(&pStClutterEchoPara->SimData, m_SimData, sizeof(SimStruct));
                memcpy(&pStClutterEchoPara->ParamR, m_ParamR, sizeof(RADAR));
                memcpy(&pStClutterEchoPara->AntParam, m_AntParam, sizeof(ANTPARAM));
                emit sendCalcuteEcho(pStClutterEchoPara);
            }
            if (pStTestPara->SimMode == 2) //2=干扰
            {
                ParaJamSet();//干扰参数生成
                stJamEchoPara *pStJamEchoPara = (stJamEchoPara*)_MallocMemObj(stJamEchoPara);
                memcpy(pStJamEchoPara->Target, m_Target, sizeof(PLATFORM));
                memcpy(pStJamEchoPara->Jammer, m_Jammer, sizeof(JAM_PARAM));
                memcpy(&pStJamEchoPara->ParamR, m_ParamR, sizeof(RADAR));
                memcpy(&pStJamEchoPara->AntParam, m_AntParam, sizeof(ANTPARAM));
                memcpy(&pStJamEchoPara->SimData, m_SimData, sizeof(SimStruct));
                memcpy(&pStJamEchoPara->RadarPos, m_RadarPos, sizeof(PLATFORM));
                emit sendCalcuteEcho(pStJamEchoPara);
            }
			qint64 t1 = QDateTime::currentMSecsSinceEpoch();
			QThread::msleep(50);
			memcpy(pBuf1, pBuf2, 4 * 1024 * 1024);
			qint64 t2 = QDateTime::currentMSecsSinceEpoch();
			int qt2 = t2 - t1;
			//qDebug() << "------------------------------------------------------sleep time length" << qt2;
        }
		qint64 tn = QDateTime::currentMSecsSinceEpoch();
		//qDebug() << "------------------------------------------------------end time " << tn;
        _ReleaseMemObj(stRadarAPC, PRI);
    }
    
    _ReleaseMemObj(stTestPara,p);
}
//将TCP参数更新到本地结构体
void ThreadClassBase::slotParaRecive(void *p)
{
	DecodeType eDecode = *(DecodeType*)p;
	switch (eDecode)
	{
    case ParaRedCfg://红方配置计算池
	{
        stParaRedCfg *pStParaRedCfg = (stParaRedCfg*)p;
        memcpy(m_cfgPara, &pStParaRedCfg->cfgPara, sizeof(CfgPara));
        memcpy(m_SimData, &pStParaRedCfg->SimData, sizeof(SimStruct));
		memcpy(m_ParamR, &pStParaRedCfg->radarPara, sizeof(SimStruct));
        memcpy(m_AntParam, &pStParaRedCfg->antPara, sizeof(ANTPARAM) - sizeof(double*) * 6 - sizeof(double) * 9);
        ParaAntSetRealTime();       //天线方向图生成
        ParaSarEcho(m_SenceTarget); //地图加载
        _ReleaseMemObj(stParaRedCfg, p);
        //emit sendCreateFile();
        stSarEchoPara *pStSarEchoPara	= (stSarEchoPara*)_MallocMemObj(stSarEchoPara);
		pStSarEchoPara->EchoType		= m_SimData->SARImagFlag;//0=不做SAR成像   1=单基地SAR成像
        pStSarEchoPara->SenceTarget     = m_SenceTarget;
        pStSarEchoPara->TarNum          = m_TarNumSar;
        pStSarEchoPara->RadarAPC		= 0;
        pStSarEchoPara->RadarVel		= 0;
		memcpy(pStSarEchoPara->AntParam, m_AntParam, sizeof(ANTPARAM));
        emit sendSettingSarSence(pStSarEchoPara);
		break;
	}
	case ParaRedRun:
	{
		stParaRedRun *pStParaRedRun = (stParaRedRun*)p;
		//memcpy(cfgPara, &pStParaRedRun->cfgPara, sizeof(CfgPara));
		memcpy(m_RadarPos, &pStParaRedRun->platPara[0], sizeof(PLATFORM));
		memcpy(m_ParamR, &pStParaRedRun->radarPara, sizeof(RADAR));

		RADAR		*ParamR		= m_ParamR;
		//PRT APC生成
		//double *PRI			= (double*)_MallocMemObj(stRadarAPC);	//真实重复周期
		double *RadarAPC	= (double*)_MallocMemObj(stRadarAPC);
		double *RadarVel	= (double*)_MallocMemObj(stRadarAPC);
		//memset(PRI, 0, sizeof(double)*ParamR->Na);
		memset(RadarAPC, 0, sizeof(double)*ParamR->Na * 3);
		memset(RadarVel, 0, sizeof(double)*ParamR->Na * 3);
		//GenPRI(ParamR, PRI);		//生成变PRT序列

		for (int idx = 0; idx < ParamR->Na; idx++)
		{
			PLATFORM	*RadarPos = (PLATFORM*)&pStParaRedRun->platPara[idx];
			RadarAPC[idx * 3 + 0] = RadarPos->NorthPos;
			RadarAPC[idx * 3 + 1] = RadarPos->SkyPos;
			RadarAPC[idx * 3 + 2] = RadarPos->EastPos;

			RadarVel[idx * 3 + 0] = RadarPos->NorthVel;
			RadarVel[idx * 3 + 1] = RadarPos->SkyVel;
			RadarVel[idx * 3 + 2] = RadarPos->EastVel;
		}

		stSarEchoPara *pStSarEchoPara	= (stSarEchoPara*)_MallocMemObj(stSarEchoPara);
		pStSarEchoPara->FrameSerial		= m_FrameSerial++;
		pStSarEchoPara->EchoType		= m_SimData->SARImagFlag;//0=不做SAR成像   1=单基地SAR成像
		pStSarEchoPara->SenceTarget		= m_SenceTarget;
		pStSarEchoPara->RadarAPC		= RadarAPC;
		pStSarEchoPara->RadarVel		= RadarVel;
		memcpy(&pStSarEchoPara->SimData, m_SimData, sizeof(SimStruct));
		memcpy(pStSarEchoPara->ParamR, m_ParamR, sizeof(RADAR));
		memcpy(pStSarEchoPara->AntParam, m_AntParam, sizeof(ANTPARAM));
		memcpy(pStSarEchoPara->PlatForm, pStParaRedRun->platPara, pStSarEchoPara->ParamR->Na*sizeof(PLATFORM));


		emit sendCalcuteEcho(pStSarEchoPara);
		_ReleaseMemObj(stParaRedRun, p);
		break;
	}
	default:break;
	}

	
}

//******************生成变PRT的序列************//
void ThreadClassBase::Gen_PRI(RADAR *ParamR, double*PRI)
{
	GenPRI(ParamR, PRI);		//生成变PRT序列
}
stSarEchoPara* ThreadClassBase::SettingSarSence(stParaRedCfg *pStParaRedCfg,char *pTar_Rcs_Pos)
{
    memset(m_Tar_Rcs_Pos,0,1024);
    memcpy(m_Tar_Rcs_Pos,pTar_Rcs_Pos,1024);
    
    memcpy(m_cfgPara, &pStParaRedCfg->cfgPara, sizeof(CfgPara));
    memcpy(m_SimData, &pStParaRedCfg->SimData, sizeof(SimStruct));
    memcpy(m_AntParam, &pStParaRedCfg->antPara, sizeof(ANTPARAM) - sizeof(double*) * 6 - sizeof(double) * 9);
    m_ParamR->Fc = pStParaRedCfg->radarPara.Fc;//临时固定 17e9
    ParaAntSetRealTime();       //天线方向图生成
    ParaSarEcho_2(m_SenceTarget); //地图加载

    stSarEchoPara *pStSarEchoPara	= (stSarEchoPara*)_MallocMemObj(stSarEchoPara);
    pStSarEchoPara->SenceTarget     = m_SenceTarget;
    pStSarEchoPara->TarNum          = m_TarNumSar;
    pStSarEchoPara->RadarAPC		= 0;
    pStSarEchoPara->RadarVel		= 0;

	memcpy(&pStSarEchoPara->AntParam[0], m_AntParam, sizeof(ANTPARAM));
    
    return pStSarEchoPara;
}
double* ThreadClassBase::GetSenceTarget()
{
	return m_SenceTarget;
}
void ThreadClassBase::slotMasterRedParaExtern(void *p)
{

}
