/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadEchoDataSave.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadEchoDataSave.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef THREADECHODATASAVE_H
#define THREADECHODATASAVE_H

#include <QObject>
#include <QTimer>
#include<QFile>

#include "_stumempool.h"
#include"globalRadarStruct.h"

class ThreadEchoDataSave : public QObject,_StuMemPool
{
    Q_OBJECT
public:
    explicit ThreadEchoDataSave(QObject *parent = 0);
    ~ThreadEchoDataSave();
    

signals:
	void sendReplaySar();
	void sendEchoDataImag(void *p);

public slots:
    void slotEchoDataSave(void *p);
    void slotCreateFile();
	void slotReplay();
	void handleTimeout();
	void slotReplayFlag(int flag);

private:
    void EchoWaveDataFormat(int dot,int &offset,Complexf *pComplexf,char *pBuffer);
	void platformPara(int &offset, PLATFORM *PlatForm, char *pBuffer);
	float AmpMax(int dot, float m_AmpMax, Complexf *pComplexf);
    void EchoDataSave(void *p);

	float m_AmpMax;
    int m_Nr;
    float m_Rmin;
    char *m_pBuffer;
    QFile *pQFile;
    QString strFilePath;
	QTimer *timer;
	QFile *pQFileReplay;
	bool m_ReplayFlagSAR;
	
};

#endif // THREADECHODATAFORMAT_H
