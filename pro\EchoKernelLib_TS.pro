# =========================================================================================
# FileProperties:
#     FileName: EchoKernelLib.pro
#     SvnProperties: 
#         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/pro/EchoKernelLib_TS.pro $
#         $Author: yening $
#         $Revision: 202 $
#         $Date: 2025-02-13 18:50:42 $
# =========================================================================================
#-------------------------------------------------
#
# Project created by QtCreator 2024-04-01T10:50:55
#
#-------------------------------------------------

QT       -= core gui opengl

greaterThan(QT_MAJOR_VERSION, 4): QT -= widgets

CONFIG(debug, debug|release){
    TARGET = _Echo_Kerneld
}
else{
    TARGET = _Echo_Kernel
}


TEMPLATE = lib

DESTDIR += ../dll

DEFINES += _ECHO_KERNEL_LIBRARY

CONFIG +=plugin
CONFIG  += c++11


INCLUDEPATH +=  ../inc \
                ../../../inc \
                ../../../cuda/inc \


SOURCES +=  \

HEADERS  += ../../../inc/WhiteCalGlobel.h \
            ../../../cuda/inc/KernelSar.h     \
            ../../../cuda/inc/KernelCultter.h \
            ../../../cuda/inc/KernelPublic.h \
            ../../../cuda/inc/KernelJAM.h \
            ../../../cuda/inc/KernelAntenna.h \
            ../../../cuda/inc/KernelSpline.h \
            ../../../cuda/inc/KernelTarHsys.h \
            ../../../cuda/inc/KernelTarHsysFd.h \
            ../../../cuda/inc/KernelCoorConver.h \
            ../../../cuda/inc/KernelChaff.h

#Cuda sources
CUDA_SOURCES += \
                ../../../cuda/src/KernelSar.cu    \
                ../../../cuda/src/KernelCultter.cu \
                ../../../cuda/src/KernelJAM.cu    \
                ../../../cuda/src/KernelPublic.cu \
                ../../../cuda/src/KernelAntenna.cu \
                ../../../cuda/src/KernelSpline.cu \
                ../../../cuda/src/KernelTarHsys.cu \
                ../../../cuda/src/KernelTarHsysFd.cu \
                ../../../cuda/src/KernelCoorConver.cu \
                ../../../cuda/src/KernelChaff.cu

#windows下配置CUDA环境依赖
win32{


}
unix{
    #cuda
    OBJECTS_DIR = ./debug__
    CUDA_OBJECTS_DIR = ./debug__

    # CUDA settings <-- may change depending on your system
    CUDA_SDK = "/usr/local/cuda"   # Path to cuda SDK install
    CUDA_DIR = "/usr/local/cuda"   # Path to cuda toolkit install

    # DO NOT EDIT BEYOND THIS UNLESS YOU KNOW WHAT YOU ARE DOING....
    SYSTEM_NAME = ubuntu        # Depending on your system either 'Win32', 'x64', or 'Win64'
    SYSTEM_TYPE = 64            # '32' or '64', depending on your system
    CUDA_ARCH = sm_50           # Type of CUDA architecture,
                                # for example 'compute_10', 'compute_11', 'sm_10'
    NVCC_OPTIONS = -g
    NVCC_FLAGS += -Xcompiler -fPIC

    # include paths
    INCLUDEPATH += $$CUDA_DIR/include

    # library directories
    QMAKE_LIBDIR += $$CUDA_DIR/lib64/
    QMAKE_LIBDIR += /usr/lib
    # Add the necessary libraries
    CUDA_LIBS = -lcudart

    # The following makes sure all path names (which often include spaces)
    # are put between quotation marks
    CUDA_INC = $$join(INCLUDEPATH,'" -I"','-I"','"')
    LIBS += $$CUDA_LIBS




    # Configuration of the Cuda compiler
    CONFIG(debug, debug|release) {
        # Debug mode
        cuda.input  = CUDA_SOURCES
        cuda.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}_cuda.o
        cuda.commands = $$CUDA_DIR/bin/clang++ -D_DEBUG $$NVCC_FLAGS $$NVCC_OPTIONS \
                        $$CUDA_INC $$NVCC_LIBS \
                        -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME}
        cuda.dependency_type = TYPE_C
        QMAKE_EXTRA_COMPILERS += cuda
    }
    else {
        # Release mode
        cuda.input = CUDA_SOURCES
        cuda.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}_cuda.o
        cuda.commands = $$CUDA_DIR/bin/clang++ $$NVCC_FLAGS $$NVCC_OPTIONS \
                        $$CUDA_INC $$NVCC_LIBS  \
                        -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME}
        cuda.dependency_type = TYPE_C
        QMAKE_EXTRA_COMPILERS += cuda
    }

}





