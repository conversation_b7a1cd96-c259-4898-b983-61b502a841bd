﻿/*******************************************************************************************
 * FileProperties: 
 *     FileName: GLWidgetInstanceBWFreq.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/GLWidget/GLWidgetInstanceBWFreq.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include <QtGui>
#include <QtOpenGL>

#include <math.h>
#include "GLWidgetInstanceBWFreq.h"

#if _MSC_VER>=1600
#pragma execution_character_set("utf-8")
#endif

GLWidgetInstanceBWFreq::GLWidgetInstanceBWFreq()
{
    pDrawAllFreqPara = NULL;
    pDrawAllFreqPara = new ClassDrawAllFreq;
    memset(pDrawAllFreqPara,0,sizeof(ClassDrawAllFreq));

    uBw = 0;
    uFs = 0;
    paint = nullptr;
}

GLWidgetInstanceBWFreq::~GLWidgetInstanceBWFreq()
{
    
}
//绘制收到的数据
void GLWidgetInstanceBWFreq::glDrawSpec()
{
    //没有收到数据时返回
    if(uBw <= 0 || uFs <= 0 || paint == nullptr)return;
    qglColor(Qt::white);
    glLineWidth(2);
    glLoadIdentity();

    float rxStrtDraw,rxEndDraw;
    rxStrtDraw = rxStrt;
    rxEndDraw = rxEnd;

    float ShowRange = rxEndDraw - rxStrtDraw;

    UINT DorwTotalDot = ShowRange * pDrawAllFreqPara->m_ShowTotalDot;

    if(DorwTotalDot < 16)DorwTotalDot = 16;


    float hRatio = 1.99f / DorwTotalDot; //横坐标
    float vRatio = 1.8f / (140);	//纵坐标

    //绘制鼠标标记矩形区域
    if(mouseLeftDown)
    {
        drawRect(strtPointDraw,endPointDraw,pSize);
    }

    float *pPaint = (float*)paint;
    int StrtDrawIndex = rxStrtDraw * pDrawAllFreqPara->m_ShowTotalDot;
    static int maxValPoz = 0;
    float maxVal = 0;

    pDrawAllFreqPara->pDrawAllFreq = pPaint;
    //画标记点
    PixPosCalcute(pDrawMousePara,
                  pDrawAllFreqPara,
                  DorwTotalDot,         //绘图总点数
                  StrtDrawIndex,        //绘图相对于总点数的偏移量
                  maxValPoz,
                  maxVal);

    //绘制鼠标标记点
    m_iMaxMinFinal = 60.f;
    PixPosDraw(pDrawAllFreqPara,
                  maxValPoz,
                  maxVal,
                  hRatio,
                  vRatio,
                  StrtDrawIndex
                  );

    glBegin(GL_LINE_STRIP);
    GLfloat x,y;
    glColor3f(0,0.8f,0);
    for(uint i = StrtDrawIndex ; i < StrtDrawIndex +  DorwTotalDot; i++)
    {
        x = hRatio*(i - StrtDrawIndex)  - 0.995f ;
        y = (pPaint[i] + 60.f)* vRatio;
        glVertex2f(x,y);

    }
    glEnd();

}
void GLWidgetInstanceBWFreq::paintGL()
{
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    //user paint 
    glDrawSpec();
    //
    drawGridLine(5,15,2,GL_LINE_STIPPLE);
    //
    if(pDrawAllFreqPara)
    {
        float RangeFre = pDrawAllFreqPara->m_InstantBW;
        float strtFre = pDrawAllFreqPara->m_ShowCenterFreq - pDrawAllFreqPara->m_InstantBW/2.f + rxStrt*pDrawAllFreqPara->m_InstantBW;
        float stopFre = pDrawAllFreqPara->m_ShowCenterFreq + pDrawAllFreqPara->m_InstantBW/2.f - (1.f-rxEnd)*pDrawAllFreqPara->m_InstantBW;;
        font.setFamily("微软雅黑");font.setPointSize(10);
        drawGridCorrd(strtFre/1000,stopFre/1000,-130,10,5,5,font);
    }
    //title
    font.setFamily("微软雅黑");
    font.setPointSize(13);
    SetWidgetTitle(-0.1f, 1.7/2,QString("频域图"),font);
}

void GLWidgetInstanceBWFreq::onSendInstanceFreShow(void *p)
{
//    stuSpectrum *pStuSpectrum = (stuSpectrum*)p;
//    if(pStuSpectrum)
//    {
//        if(pStuSpectrumRelease)
//        {
//            _ReleaseMemObj(stuSpectrum,pStuSpectrumRelease);
//        }
//        pStuSpectrumRelease = pStuSpectrum;
//        //获取长度和数据
//        int len     = pStuSpectrum->dLon;
//        paint       = (float*)pStuSpectrum->uSpectrum;
//        uBw         = pStuSpectrum->uBw;
//        uFs         = pStuSpectrum->uFs;
//        fCenterFre  = pStuSpectrum->uCenterFre;
        
//        pDrawAllFreqPara->m_InstantBW           = uBw;
//        pDrawAllFreqPara->m_ShowBW              = uBw;
//        pDrawAllFreqPara->m_ShowTotalDot        = len;
//        pDrawAllFreqPara->m_ShowCenterFreq      = fCenterFre;
        
//        gMax = (GLfloat)(pStuSpectrum->uMax);
//        gMin = (GLfloat)(pStuSpectrum->uMin);
//        if (m_bState == true)
//            updateGL();
//    }

}
