/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadEchoDataFormat.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadEchoDataFormat.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef THREADECHODATAFORMAT_H
#define THREADECHODATAFORMAT_H

#include <QObject>
#include <QTimer>

#include "_stumempool.h"
#include"globalRadarStruct.h"

class ThreadEchoDataFormat : public QObject,_StuMemPool
{
    Q_OBJECT
public:
    explicit ThreadEchoDataFormat(QObject *parent = 0);
    ~ThreadEchoDataFormat();
    

signals:
    void sendSocketMsgTCP(void *p, unsigned int eDecoteType);
    void sendEchoDataSave(void *p);
    void sendWaveShow(void *p);

public slots:
    void slotEchoDataFormat(void *p);
	void slotEchoDataPackFormat(void *p);
    void slotASKWaveDate();

private:
    void EchoWaveDataFormat(int dot,int &offset,Complexf *pComplexf,char *pBuffer);
	void platformPara(int &offset, PLATFORM *PlatForm, char *pBuffer);
	float AmpMax(int dot, float m_AmpMax, Complexf *pComplexf);

    void EchoDataFormat(stEchoData *pStEchoData,stEchoDataPack *pStEchoDataPack);

	float m_AmpMax;
    bool m_bWaveDataShowFlag;
};

#endif // THREADECHODATAFORMAT_H
