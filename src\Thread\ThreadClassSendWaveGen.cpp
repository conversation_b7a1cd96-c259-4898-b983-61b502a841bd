/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassSendWaveGen.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassSendWaveGen.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassSendWaveGen.h"
#include<QThread>

ThreadClassSendWaveGen::ThreadClassSendWaveGen(QObject *parent) : QObject(parent)
{
    //分配CPU伪内存池空间
    InitMyMalloc(10*1024*1024);


    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassSendWaveGen::~ThreadClassSendWaveGen()
{

}
void ThreadClassSendWaveGen::RadarSendWaveGen(RADAR *ParamR,double *Real,double *Imag,double FreqShift)
{
    SendWaveGen(ParamR,(Complexd*)nullptr,Real,Imag,1,FreqShift);
}

void ThreadClassSendWaveGen::RadarSendWaveGen(RADAR *ParamR,Complexd *BaseSignal)
{
    SendWaveGen(ParamR,BaseSignal,(double*)nullptr,(double*)nullptr,0);
}

//产生发射信号
void ThreadClassSendWaveGen::RadarSendWaveGen(RADAR *ParamR,Complex *BaseSignal)
{
    SendWaveGen(ParamR,BaseSignal,(double*)nullptr,(double*)nullptr,0);
}
//产生发射信号
template <class T,class T2>
void ThreadClassSendWaveGen::SendWaveGen(RADAR *ParamR,T *BaseSignal,T2 *Real,T2 *Imag,int GenMode)
{
    int TrNum  = (int)(ParamR->Fs*ParamR->Tp+0.5);		//一个脉宽的点数
    double KrTime;		//调频斜率对应的采样时刻
    //**********发射信号生成***********//
	if(ParamR->SignalType == Chirp)
	{
        if(GenMode == 0){
            for(int idz = 0;idz<TrNum;idz++)
            {
                KrTime = (idz - TrNum/2)/ParamR->Fs;
                (BaseSignal +idz)->x = cos(PI*ParamR->Kr*KrTime*KrTime);
                (BaseSignal +idz)->y = sin(PI*ParamR->Kr*KrTime*KrTime);
            }
        }
        else if(GenMode == 1){
            for(int idz = 0;idz<TrNum;idz++)
            {
                KrTime = (idz - TrNum/2)/ParamR->Fs;
                Real[idz] = cos(PI*ParamR->Kr*KrTime*KrTime);
                Imag[idz] = sin(PI*ParamR->Kr*KrTime*KrTime);
            }
        }
	}
	else if(ParamR->SignalType == Pulse)	//简单脉冲
	{
        if(GenMode == 0){
            for(int idz = 0;idz<TrNum;idz++)
            {
                KrTime = idz/ParamR->Fs;
                (BaseSignal +idz)->x = cos(2*PI*ParamR->F0*KrTime);
                (BaseSignal +idz)->y = sin(2*PI*ParamR->F0*KrTime);
            }
        }
        else if(GenMode == 1){
            for(int idz = 0;idz<TrNum;idz++)
            {
                KrTime = idz/ParamR->Fs;
                Real[idz] = cos(2*PI*ParamR->F0*KrTime);
                Imag[idz] = sin(2*PI*ParamR->F0*KrTime);
            }
        }
	}
	else if(ParamR->SignalType == BPSK)
	{
		int *CodeSymbol = (int*)_Malloc(sizeof(int)*ParamR->CodeLen);
		TrNum = (int)(ParamR->Fs*ParamR->CodeTr*ParamR->CodeLen +0.5);
        //码元生成
        PSK_Code_Gen(ParamR->CodeLen,CodeSymbol);
		int CodeNum  = (int)(ParamR->Fs*ParamR->CodeTr+0.5);	//一个码元的点数
		int idn = 0;	
        if(GenMode == 0){
            for(int idz = 0;idz<TrNum;idz++)
            {
                if(idz%CodeNum == 0){
                    idn++;
                }
                (BaseSignal +idz)->x = CodeSymbol[idn-1]*cos(2*PI*idz*ParamR->F0/ParamR->Fs);
                (BaseSignal +idz)->y = CodeSymbol[idn-1]*sin(2*PI*idz*ParamR->F0/ParamR->Fs);
            }
        }
        else if(GenMode == 1){
            for(int idz = 0;idz<TrNum;idz++)
            {
                if(idz%CodeNum == 0){
                    idn++;
                }
                Real[idz] = CodeSymbol[idn-1]*cos(2*PI*idz*ParamR->F0/ParamR->Fs);
                Imag[idz] = CodeSymbol[idn-1]*sin(2*PI*idz*ParamR->F0/ParamR->Fs);
            }
        }
	}
	//**********发射信号生成***********//
}
//产生发射信号
template <class T,class T2>
void ThreadClassSendWaveGen::SendWaveGen(RADAR *ParamR,T *BaseSignal,T2 *Real,T2 *Imag,int GenMode,double FreqShift)
{
    int TrNum  = (int)(ParamR->Fs*ParamR->Tp+0.5);		//一个脉宽的点数
    double KrTime;		//调频斜率对应的采样时刻
    //**********发射信号生成***********//
	if(ParamR->SignalType == Chirp)
	{
        if(GenMode == 0){
            for(int idz = 0;idz<TrNum;idz++)
            {
                KrTime = (idz - TrNum/2)/ParamR->Fs;
                (BaseSignal +idz)->x = cos(PI*ParamR->Kr*KrTime*KrTime + 2*PI*FreqShift*KrTime);
                (BaseSignal +idz)->y = sin(PI*ParamR->Kr*KrTime*KrTime + 2*PI*FreqShift*KrTime);
            }
        }
        else if(GenMode == 1){
            for(int idz = 0;idz<TrNum;idz++)
            {
                KrTime = (idz - TrNum/2)/ParamR->Fs;
                Real[idz] = cos(PI*ParamR->Kr*KrTime*KrTime + 2*PI*FreqShift*KrTime);
                Imag[idz] = sin(PI*ParamR->Kr*KrTime*KrTime + 2*PI*FreqShift*KrTime);
            }
        }
	}
	else if(ParamR->SignalType == Pulse)	//简单脉冲
	{
        if(GenMode == 0){
            for(int idz = 0;idz<TrNum;idz++)
            {
                KrTime = idz/ParamR->Fs;
                (BaseSignal +idz)->x = cos(2*PI*ParamR->F0*KrTime + 2*PI*FreqShift*KrTime);
                (BaseSignal +idz)->y = sin(2*PI*ParamR->F0*KrTime + 2*PI*FreqShift*KrTime);
            }
        }
        else if(GenMode == 1){
            for(int idz = 0;idz<TrNum;idz++)
            {
                KrTime = idz/ParamR->Fs;
                Real[idz] = cos(2*PI*ParamR->F0*KrTime + 2*PI*FreqShift*KrTime);
                Imag[idz] = sin(2*PI*ParamR->F0*KrTime + 2*PI*FreqShift*KrTime);
            }
        }
	}
	else if(ParamR->SignalType == BPSK)
	{
		int *CodeSymbol = (int*)_Malloc(sizeof(int)*ParamR->CodeLen);
		TrNum = (int)(ParamR->Fs*ParamR->CodeTr*ParamR->CodeLen +0.5);
        //码元生成
        PSK_Code_Gen(ParamR->CodeLen,CodeSymbol);
		int CodeNum  = (int)(ParamR->Fs*ParamR->CodeTr+0.5);	//一个码元的点数
		int idn = 0;	
        if(GenMode == 0){
            for(int idz = 0;idz<TrNum;idz++)
            {
                if(idz%CodeNum == 0){
                    idn++;
                }
                KrTime = (idz)/ParamR->Fs;
                (BaseSignal +idz)->x = CodeSymbol[idn-1]*cos(2*PI*idz*ParamR->F0/ParamR->Fs + 2*PI*FreqShift*KrTime);
                (BaseSignal +idz)->y = CodeSymbol[idn-1]*sin(2*PI*idz*ParamR->F0/ParamR->Fs + 2*PI*FreqShift*KrTime);
            }
        }
        else if(GenMode == 1){
            for(int idz = 0;idz<TrNum;idz++)
            {
                if(idz%CodeNum == 0){
                    idn++;
                }
                KrTime = (idz)/ParamR->Fs;
                Real[idz] = CodeSymbol[idn-1]*cos(2*PI*idz*ParamR->F0/ParamR->Fs + 2*PI*FreqShift*KrTime);
                Imag[idz] = CodeSymbol[idn-1]*sin(2*PI*idz*ParamR->F0/ParamR->Fs + 2*PI*FreqShift*KrTime);
            }
        }
	}
	//**********发射信号生成***********//
}
//相位编码码元生成
void ThreadClassSendWaveGen::PSK_Code_Gen(int CodeLen,int *CodeSymbol)
{
    switch(CodeLen)
    {
    case 1:		//+
        CodeSymbol[0] = 1;
        break;
    case 2:		//+-，++
        CodeSymbol[0] = 1;
        CodeSymbol[1] = -1;
        break;
    case 3:		//++-，+-+
        CodeSymbol[0] = 1;
        CodeSymbol[1] = 1;
        CodeSymbol[2] = -1;
        break;
    case 4:		//++-+，+++-
        CodeSymbol[0] = 1;
        CodeSymbol[1] = 1;
        CodeSymbol[2] = -1;
        CodeSymbol[3] = 1;
        break;
    case 5:		//+++-+
        CodeSymbol[0] = 1;
        CodeSymbol[1] = 1;
        CodeSymbol[2] = 1;
        CodeSymbol[3] = -1;
        CodeSymbol[4] = 1;
        break;
    case 7:		//+++--+-
        CodeSymbol[0] = 1;
        CodeSymbol[1] = 1;
        CodeSymbol[2] = 1;
        CodeSymbol[3] = -1;
        CodeSymbol[4] = -1;
        CodeSymbol[5] = 1;
        CodeSymbol[6] = -1;
        break;
    case 11:	//+++---+--+-
        CodeSymbol[0]  = 1;
        CodeSymbol[1]  = 1;
        CodeSymbol[2]  = 1;
        CodeSymbol[3]  = -1;
        CodeSymbol[4]  = -1;
        CodeSymbol[5]  = -1;
        CodeSymbol[6]  = 1;
        CodeSymbol[7]  = -1;
        CodeSymbol[8]  = -1;
        CodeSymbol[9]  = 1;
        CodeSymbol[10] = -1;
        break;
    case 13:	//+++++--++-+-+
        CodeSymbol[0]  = 1;
        CodeSymbol[1]  = 1;
        CodeSymbol[2]  = 1;
        CodeSymbol[3]  = 1;
        CodeSymbol[4]  = 1;
        CodeSymbol[5]  = -1;
        CodeSymbol[6]  = -1;
        CodeSymbol[7]  = 1;
        CodeSymbol[8]  = 1;
        CodeSymbol[9]  = -1;
        CodeSymbol[10] = 1;
        CodeSymbol[11]  = -1;
        CodeSymbol[12] = 1;
        break;
    }
}

//生成发射信号
void ThreadClassSendWaveGen::TransSignalSim(RADAR *ParamR, SimStruct *SimData, int TrNum, Complexf *BaseSignal)
{
	int idx, idy;

	//生成辐射源
	switch (ParamR->SignalType)
	{
	case Pulse:		//脉冲
	{
		for (idx = 0; idx<TrNum; idx++)
		{
			BaseSignal[idx].x = ParamR->Amp*cos(2 * PI*idx*ParamR->F0 / ParamR->Fs);
			BaseSignal[idx].y = ParamR->Amp*sin(2 * PI*idx*ParamR->F0 / ParamR->Fs);
		}
		break;
	}
	case Chirp:			//线性调频
	{
		double FcPhase = 2 * PI*ParamR->F0 / ParamR->Fs;
		for (idx = 0; idx<TrNum; idx++)
		{
			double KrTime = (idx - TrNum / 2) / ParamR->Fs;
			BaseSignal[idx].x = ParamR->Amp*cos(idx*FcPhase + PI*ParamR->Kr*KrTime*KrTime);
			BaseSignal[idx].y = ParamR->Amp*sin(idx*FcPhase + PI*ParamR->Kr*KrTime*KrTime);
		}
		break;
	}
	case FSK:		//FSK
	{
		if (ParamR->FskNum == 2)
		{
			double FcPhase0[2];
			FcPhase0[0] = 2 * PI*ParamR->FSK_FC[0] / ParamR->Fs;
			FcPhase0[1] = 2 * PI*ParamR->FSK_FC[1] / ParamR->Fs;
			double FcPhase = FcPhase0[0];
			int CodeNum = (int)(ParamR->Fs*ParamR->CodeTr + 0.5);	//一个码元的点数
			idy = 0;
			for (idx = 0; idx<CodeNum * 2; idx++)
			{
				if (idx%CodeNum == 0)
				{
					FcPhase = FcPhase0[idy % 2];
				}
				BaseSignal[idx].x = ParamR->Amp*cos(idx*FcPhase);
				BaseSignal[idx].y = ParamR->Amp*sin(idx*FcPhase);
			}
			for (idx = CodeNum * 2; idx<TrNum; idx++)
			{
				if (idx%CodeNum == 0)
				{
					FcPhase = FcPhase0[(int)(2 * (double)rand() / (double)RAND_MAX)];
				}
				BaseSignal[idx].x = ParamR->Amp*cos(idx*FcPhase);
				BaseSignal[idx].y = ParamR->Amp*sin(idx*FcPhase);
			}
		}
		else
		{
			double FcPhase0[4];
			FcPhase0[0] = 2 * PI*ParamR->FSK_FC[0] / ParamR->Fs;
			FcPhase0[1] = 2 * PI*ParamR->FSK_FC[3] / ParamR->Fs;
			FcPhase0[2] = 2 * PI*ParamR->FSK_FC[1] / ParamR->Fs;
			FcPhase0[3] = 2 * PI*ParamR->FSK_FC[2] / ParamR->Fs;
			double FcPhase = FcPhase0[0];
			int CodeNum = (int)(ParamR->Fs*ParamR->CodeTr + 0.5);	//一个码元的点数
			idy = 0;
			for (idx = 0; idx<CodeNum * 4; idx++)
			{
				if (idx%CodeNum == 0)
				{
					FcPhase = FcPhase0[idy % 4];
					idy++;
				}
				BaseSignal[idx].x = ParamR->Amp*cos(idx*FcPhase);
				BaseSignal[idx].y = ParamR->Amp*sin(idx*FcPhase);
			}
			for (idx = CodeNum * 4; idx<TrNum; idx++)
			{
				if (idx%CodeNum == 0)
				{
					FcPhase = FcPhase0[(int)(4 * (double)rand() / (double)RAND_MAX)];
				}
				BaseSignal[idx].x = ParamR->Amp*cos(idx*FcPhase);
				BaseSignal[idx].y = ParamR->Amp*sin(idx*FcPhase);
			}
		}
		break;
	}
	case BPSK:			//BPSK
	{
		double FcPhase = 2 * PI*ParamR->F0 / ParamR->Fs;
		int CodeNum = (int)(ParamR->Fs*ParamR->CodeTr + 0.5);	//一个码元的点数
		int CodeSymbol;
		for (idx = 0; idx<TrNum; idx++)
		{
			if (idx%CodeNum == 0)
			{
				CodeSymbol = (int)(2 * (double)rand() / (double)RAND_MAX) * 2 - 1;
			}
			BaseSignal[idx].x = CodeSymbol*ParamR->Amp*cos(idx*FcPhase);
			BaseSignal[idx].y = CodeSymbol*ParamR->Amp*sin(idx*FcPhase);
		}
		break;
	}
	case QPSK:		//QPSK
	{
		double FcPhase = 2 * PI*ParamR->F0 / ParamR->Fs;
		int CodeNum = (int)(ParamR->Fs*ParamR->CodeTr + 0.5);	//一个码元的点数
		int CodeSymbol;
		for (idx = 0; idx<TrNum; idx++)
		{
			if (idx%CodeNum == 0)
			{
				CodeSymbol = (int)(4 * (double)rand() / (double)RAND_MAX);
			}
			BaseSignal[idx].x = ParamR->Amp*(cos(idx*FcPhase)*cos(PI*CodeSymbol / 2.0) + sin(idx*FcPhase)*sin(PI*CodeSymbol / 2.0));
			BaseSignal[idx].y = ParamR->Amp*(sin(idx*FcPhase)*cos(PI*CodeSymbol / 2.0) + cos(idx*FcPhase)*sin(PI*CodeSymbol / 2.0));
		}
		break;
	}
	}
}


//计算池生成辐射源
void ThreadClassSendWaveGen::RadiatorSignalSim(RADAR *SourceRadar[40], SimStruct *SimData, 
                            Complex *SumSourcePolar1, Complex *AziSourcePolar1, Complex *EleSourcePolar1, Complex *ExSourcePolar1, 
                            Complex *SumSourcePolar2, Complex *AziSourcePolar2, Complex *EleSourcePolar2, Complex *ExSourcePolar2)
{
	int idx, idy, idz, idn;
	int k, g;

	int SaveFlieLen = SimData->SimLength;
	double PRTTime;	//当前时刻
	double TranGain;//传播衰减
	int DelayID, PRTDelayID;	//延时
	int TrNum;
	double KrTime;
	int Symbol = 1;
	double *PRI = NULL;	//真实重复周期
	for (idn = 0; idn < SimData->RadarNum; idn++)
	{
		DelayID = (int)(SourceRadar[idn]->Delay * SimData->Fs + 0.5);
		if (SinCos == SourceRadar[idn]->SignalType)		//连续波
		{
			double Pha = 2 * PI*SourceRadar[idn]->Fc / SimData->Fs;
			TranGain = SourceRadar[idn]->Amp;
			for (int idx = 0; idx < SaveFlieLen; idx++)
			{
				(SumSourcePolar1 + idx + DelayID)->x += TranGain * cos(Pha*idx);
				(SumSourcePolar1 + idx + DelayID)->y += TranGain * sin(Pha*idx);
				(AziSourcePolar1 + idx + DelayID)->x += TranGain * cos(Pha*idx);
				(AziSourcePolar1 + idx + DelayID)->y += TranGain * sin(Pha*idx);
				(EleSourcePolar1 + idx + DelayID)->x += TranGain * cos(Pha*idx);
				(EleSourcePolar1 + idx + DelayID)->y += TranGain * sin(Pha*idx);
				(ExSourcePolar1 + idx + DelayID)->x += TranGain * cos(Pha*idx);
				(ExSourcePolar1 + idx + DelayID)->y += TranGain * sin(Pha*idx);
				(SumSourcePolar2 + idx + DelayID)->x += TranGain * cos(Pha*idx);
				(SumSourcePolar2 + idx + DelayID)->y += TranGain * sin(Pha*idx);
				(AziSourcePolar2 + idx + DelayID)->x += TranGain * cos(Pha*idx);
				(AziSourcePolar2 + idx + DelayID)->y += TranGain * sin(Pha*idx);
				(EleSourcePolar2 + idx + DelayID)->x += TranGain * cos(Pha*idx);
				(EleSourcePolar2 + idx + DelayID)->y += TranGain * sin(Pha*idx);
				(ExSourcePolar2 + idx + DelayID)->x += TranGain * cos(Pha*idx);
				(ExSourcePolar2 + idx + DelayID)->y += TranGain * sin(Pha*idx);
			}
			continue;
		}

		//if(BPSK == SourceRadar[idn]->SignalType || QPSK == SourceRadar[idn]->SignalType)
		//	SourceRadar[idn]->Tp = SourceRadar[idn]->CodeTr*SourceRadar[idn]->CodeLen;
		TrNum = (int)(SimData->Fs*SourceRadar[idn]->Tp + 0.5);//一个脉宽的点数
		if (ConstantPRF == SourceRadar[idn]->TriggerType)	//恒定重频
		{
			SourceRadar[idn]->Na = (int)(SimData->SimTime * SourceRadar[idn]->Prf);
			PRI = (double*)malloc(sizeof(double)*SourceRadar[idn]->Na);	//真实重复周期
			for (idx = 0, idy = 0; idx < SourceRadar[idn]->Na; idx++, idy++)
			{
				PRI[idx] = 1 / SourceRadar[idn]->Prf;
			}
		}
		else if (StaggeredPRF == SourceRadar[idn]->TriggerType)	//重频参差
		{
			SourceRadar[idn]->Na = (int)(SimData->SimTime / SourceRadar[idn]->PRIMin);
			PRI = (double*)malloc(sizeof(double)*SourceRadar[idn]->Na);	//真实重复周期
			switch (SourceRadar[idn]->FreamNum)
			{
			case 8:
				k = 4; g = 3;
				break;
			case 9:
				k = 1; g = 1;
				break;
			case 12:
				k = 0; g = 7;
				break;
			case 14:
				k = 0; g = 5;
				break;
			default:
				printf("雷思尼克参差信号模型中输入的帧周期数需要确认！");
			}
			idx = 0;
			for (idy = 0; idy < SourceRadar[idn]->Na; idy = idy + SourceRadar[idn]->StagNum)
			{
				idx = idx % (SourceRadar[idn]->FreamNum - 1);
				PRI[idy] = SourceRadar[idn]->PRIMin + SourceRadar[idn]->Tp*((k + idx*g) % (SourceRadar[idn]->FreamNum - 1));
				for (idz = 0; idz < SourceRadar[idn]->StagNum; idz++)
				{
					PRI[idy + idz] = PRI[idy];
				}
				idx++;
			}
		}
		else if (JitteringPRF == SourceRadar[idn]->TriggerType)	//重频抖动
		{
			SourceRadar[idn]->Na = (int)(SimData->SimTime / SourceRadar[idn]->BasePRI);
			PRI = (double*)malloc(sizeof(double)*SourceRadar[idn]->Na);	//真实重复周期
			for (idx = 0; idx < SourceRadar[idn]->Na; idx = idx + SourceRadar[idn]->StagNum)
			{
				PRI[idx] = SourceRadar[idn]->BasePRI + SourceRadar[idn]->BasePRI*SourceRadar[idn]->JitGama*((double)rand() / RAND_MAX - 0.5) * 2;
				for (idz = 0; idz < SourceRadar[idn]->StagNum; idz++)
				{
					PRI[idx + idz] = PRI[idx];
				}
			}
		}
		else if (SlipPRF == SourceRadar[idn]->TriggerType)	//重频滑动
		{
			double TimeEachSum = 0;
			SourceRadar[idn]->Na = (int)(SimData->SimTime / SourceRadar[idn]->BasePRI);
			PRI = (double*)malloc(sizeof(double)*SourceRadar[idn]->Na);	//真实重复周期
			for (idx = 0; idx < SourceRadar[idn]->Na; idx = idx + SourceRadar[idn]->StagNum)
			{
				PRI[idx] = SourceRadar[idn]->BasePRI + SourceRadar[idn]->BasePRI*SourceRadar[idn]->SlipGama*sin(2 * PI*TimeEachSum / SourceRadar[idn]->CicleTime);
				for (idz = 0; idz < SourceRadar[idn]->StagNum; idz++)
				{
					PRI[idx + idz] = PRI[idx];
				}
				TimeEachSum += PRI[idx];
			}
		}

		double *HysRe = (double*)malloc(sizeof(double)*TrNum);
		double *HysIm = (double*)malloc(sizeof(double)*TrNum);
		memset(HysRe, 0, sizeof(double)*TrNum);
		memset(HysRe, 0, sizeof(double)*TrNum);
		PRTTime = 0;

		DelayID = (int)((PRTTime + SourceRadar[idn]->Delay)*SimData->Fs + 0.5);

		//生成辐射源
		switch (SourceRadar[idn]->SignalType)
		{
		case Pulse:		//脉冲
		{

			for (idx = 0; idx<TrNum; idx++)
			{
				HysRe[idx] = SourceRadar[idn]->Amp*cos(2 * PI*idx*SourceRadar[idn]->Fc / SimData->Fs);
				HysIm[idx] = SourceRadar[idn]->Amp*sin(2 * PI*idx*SourceRadar[idn]->Fc / SimData->Fs);
			}
			break;
		}
		case Chirp:			//线性调频
		{
			double FcPhase = 2 * PI*SourceRadar[idn]->Fc / SimData->Fs;
			for (idx = 0; idx<TrNum; idx++)
			{
				KrTime = (idx - TrNum / 2) / SimData->Fs;
				HysRe[idx] = SourceRadar[idn]->Amp*cos(idx*FcPhase + PI*SourceRadar[idn]->Kr*KrTime*KrTime);
				HysIm[idx] = SourceRadar[idn]->Amp*sin(idx*FcPhase + PI*SourceRadar[idn]->Kr*KrTime*KrTime);
			}
			break;
		}
		case FSK:		//FSK
		{
			if (SourceRadar[idn]->FskNum == 2)
			{
				double FcPhase0[2];
				FcPhase0[0] = 2 * PI*SourceRadar[idn]->FSK_FC[0] / SimData->Fs;
				FcPhase0[1] = 2 * PI*SourceRadar[idn]->FSK_FC[1] / SimData->Fs;
				double FcPhase = FcPhase0[0];
				int CodeNum = (int)(SimData->Fs*SourceRadar[idn]->CodeTr + 0.5);	//一个码元的点数
				idy = 0;
				for (idx = 0; idx<CodeNum * 2; idx++)
				{
					if (idx%CodeNum == 0)
					{
						FcPhase = FcPhase0[idy % 2];
					}
					HysRe[idx] = SourceRadar[idn]->Amp*cos(idx*FcPhase);
					HysIm[idx] = SourceRadar[idn]->Amp*sin(idx*FcPhase);
				}
				for (idx = CodeNum * 2; idx<TrNum; idx++)
				{
					if (idx%CodeNum == 0)
					{
						FcPhase = FcPhase0[(int)(2 * (double)rand() / (double)RAND_MAX)];
					}
					HysRe[idx] = SourceRadar[idn]->Amp*cos(idx*FcPhase);
					HysIm[idx] = SourceRadar[idn]->Amp*sin(idx*FcPhase);
				}
			}
			else
			{
				double FcPhase0[4];
				FcPhase0[0] = 2 * PI*SourceRadar[idn]->FSK_FC[0] / SimData->Fs;
				FcPhase0[1] = 2 * PI*SourceRadar[idn]->FSK_FC[3] / SimData->Fs;
				FcPhase0[2] = 2 * PI*SourceRadar[idn]->FSK_FC[1] / SimData->Fs;
				FcPhase0[3] = 2 * PI*SourceRadar[idn]->FSK_FC[2] / SimData->Fs;
				double FcPhase = FcPhase0[0];
				int CodeNum = (int)(SimData->Fs*SourceRadar[idn]->CodeTr + 0.5);	//一个码元的点数
				idy = 0;
				for (idx = 0; idx<CodeNum * 4; idx++)
				{
					if (idx%CodeNum == 0)
					{
						FcPhase = FcPhase0[idy % 4];
						idy++;
					}
					HysRe[idx] = SourceRadar[idn]->Amp*cos(idx*FcPhase);
					HysIm[idx] = SourceRadar[idn]->Amp*sin(idx*FcPhase);
				}
				for (idx = CodeNum * 4; idx<TrNum; idx++)
				{
					if (idx%CodeNum == 0)
					{
						FcPhase = FcPhase0[(int)(4 * (double)rand() / (double)RAND_MAX)];
					}
					HysRe[idx] = SourceRadar[idn]->Amp*cos(idx*FcPhase);
					HysIm[idx] = SourceRadar[idn]->Amp*sin(idx*FcPhase);
				}
			}
			break;
		}
		case BPSK:			//BPSK
		{
			double FcPhase = 2 * PI*SourceRadar[idn]->Fc / SimData->Fs;
			int CodeNum = (int)(SimData->Fs*SourceRadar[idn]->CodeTr + 0.5);	//一个码元的点数
			int CodeSymbol;
			for (idx = 0; idx<TrNum; idx++)
			{
				if (idx%CodeNum == 0)
				{
					CodeSymbol = (int)(2 * (double)rand() / (double)RAND_MAX) * 2 - 1;
				}
				HysRe[idx] = CodeSymbol*SourceRadar[idn]->Amp*cos(idx*FcPhase);
				HysIm[idx] = CodeSymbol*SourceRadar[idn]->Amp*sin(idx*FcPhase);
			}
			break;
		}
		case QPSK:		//QPSK
		{
			double FcPhase = 2 * PI*SourceRadar[idn]->Fc / SimData->Fs;
			int CodeNum = (int)(SimData->Fs*SourceRadar[idn]->CodeTr + 0.5);	//一个码元的点数
			int CodeSymbol;
			for (idx = 0; idx<TrNum; idx++)
			{
				if (idx%CodeNum == 0)
				{
					CodeSymbol = (int)(4 * (double)rand() / (double)RAND_MAX);
				}
				HysRe[idx] = SourceRadar[idn]->Amp*(cos(idx*FcPhase)*cos(PI*CodeSymbol / 2.0) + sin(idx*FcPhase)*sin(PI*CodeSymbol / 2.0));
				HysIm[idx] = SourceRadar[idn]->Amp*(sin(idx*FcPhase)*cos(PI*CodeSymbol / 2.0) + cos(idx*FcPhase)*sin(PI*CodeSymbol / 2.0));
			}
			break;
		}
		}

		for (idy = 0; idy< SourceRadar[idn]->Na; idy++)
		{
			PRTDelayID = (int)(DelayID + PRTTime* SimData->Fs + 0.5);
			if (PRTDelayID + TrNum < (int)(SimData->Fs * SimData->SimTime))
			{
				for (idz = 0; idz<TrNum; idz++)
				{
					SumSourcePolar1[PRTDelayID + idz].x += HysRe[idz];
					SumSourcePolar1[PRTDelayID + idz].y += HysIm[idz];
					AziSourcePolar1[PRTDelayID + idz].x += HysRe[idz];
					AziSourcePolar1[PRTDelayID + idz].y += HysIm[idz];
					EleSourcePolar1[PRTDelayID + idz].x += HysRe[idz];
					EleSourcePolar1[PRTDelayID + idz].y += HysIm[idz];
					ExSourcePolar1[PRTDelayID + idz].x += HysRe[idz];
					ExSourcePolar1[PRTDelayID + idz].y += HysIm[idz];
					SumSourcePolar2[PRTDelayID + idz].x += HysRe[idz];
					SumSourcePolar2[PRTDelayID + idz].y += HysIm[idz];
					AziSourcePolar2[PRTDelayID + idz].x += HysRe[idz];
					AziSourcePolar2[PRTDelayID + idz].y += HysIm[idz];
					EleSourcePolar2[PRTDelayID + idz].x += HysRe[idz];
					EleSourcePolar2[PRTDelayID + idz].y += HysIm[idz];
					ExSourcePolar2[PRTDelayID + idz].x += HysRe[idz];
					ExSourcePolar2[PRTDelayID + idz].y += HysIm[idz];
				}
			}
			PRTTime += PRI[idy];
		}
		free(PRI);
		free(HysRe);
		free(HysIm);
	}
	//DoulbeArrayToDisk2((double*)SourceData,"SourceData.bin",2*SimData->SimLength);
}
