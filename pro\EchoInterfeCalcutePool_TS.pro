# =========================================================================================
# FileProperties:
#     FileName: EchoInterfeCalcutePool.pro
#     SvnProperties: 
#         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/pro/EchoInterfeCalcutePool_TS.pro $
#         $Author: yening $
#         $Revision: 202 $
#         $Date: 2025-02-13 18:50:42 $
# =========================================================================================
#-------------------------------------------------
#
# Project created by QtCreator 2024-04-01T10:50:55
#
#-------------------------------------------------

QT       += core gui opengl

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = EchoInterfeCalcutePool
TEMPLATE = app

DESTDIR += ../bin

DEFINES += QT_DEPRECATED_WARNINGS  _STUMEMPOOL_LIBRARY _GLWidgetBase_LIBRARY _ECHO_KERNEL_LIBRARY

DEFINES += _ECHO_KERNEL_LIBRARY


INCLUDEPATH +=  ../inc \
                ../../../inc \
                ../../../inc/GLWidget \
                ../../../cuda/inc \
                ../../../inc/MemPool \
                ../inc/Thread \
                ../inc/Class \
                ../inc/public \
                ../inc/cuda \
                ../inc/GLWidget \
                ../../../inc/Extern

SOURCES +=  ../src/main.cpp\
            ../src/MainWindow.cpp \
            ../src/Thread/ThreadClassBase.cpp \
            ../src/Thread/ThreadResourceScheduling.cpp \
            ../src/Thread/ThreadSocket.cpp \
            ../src/Thread/ThreadClassGPUCalcute.cpp \
            ../src/Thread/ThreadClassSarEcho.cpp \
            ../src/Thread/ThreadClassChaffEcho.cpp \
            ../src/Thread/ThreadClassJFEcho.cpp \
            ../src/Thread/ThreadClassClutter.cpp \
            ../src/Thread/ThreadClassSarEchoClutter.cpp \
            ../../../inc/MemPool/MyMalloc.cpp \
            ../../../inc/MemPool/MyMallocCUDA.cpp \
            ../src/Thread/ThreadClassSendWaveGen.cpp \
            ../src/Thread/ThreadClassGenJAM.cpp \
            ../src/Thread/ThreadClassBlanketJamGen.cpp \
            ../src/Thread/ThreadClassDeceptionJamGen.cpp \
            ../src/public/WhiteCalGloable.cpp \
            ../src/Thread/ThreadClassAntennaGen.cpp \
#            ../inc/Spline/Spline.cpp \
            ../src/Thread/ThreadSocketTCP.cpp \
            ../src/Thread/ThreadSocketTCPExtern.cpp \
            ../src/Thread/ThreadEchoDataFormat.cpp \
            ../src/Thread/ThreadEchoDataSave.cpp \
            ../src/Class/ClassRcsRead.cpp \
            ../src/Class/ClassClutterRcsRead.cpp \
            ../src/Class/ClassCoorDinate.cpp \
            ../src/Class/ClassEchoWaveFd.cpp \
            ../src/GLWidget/GLWidgetInstanceBWFreq.cpp \
            ../src/GLWidget/GLWidgetWave.cpp \
            ../src/WidgetShow.cpp

HEADERS  += ../inc/MainWindow.h \
            ../../../inc/RedGloable.h \
            ../../../inc/globalJamStruct.h \
            ../../../inc/globalExternalStruct.h \
            ../inc/Thread/ThreadClassBase.h \
            ../inc/Thread/ThreadResourceScheduling.h \
            ../inc/Thread/ThreadSocket.h \
            ../../../inc/MemPool/_stumempool.h \
            ../inc/Thread/ThreadClassGPUCalcute.h \
            ../inc/Thread/ThreadClassSarEcho.h \
            ../inc/Thread/ThreadClassChaffEcho.h \
            ../inc/Thread/ThreadClassJFEcho.h \
            ../inc/Thread/ThreadClassSarGenImag.h \
            ../inc/Thread/ThreadClassClutter.h \
            ../inc/Thread/ThreadClassSarEchoClutter.h \
            ../../../cuda/inc/KernelSar.h \
            ../inc/MemPool/MyMalloc.h \
            ../inc/MemPool/MyMallocCUDA.h \
            ../inc/Thread/ThreadClassSendWaveGen.h \
            ../../../cuda/inc/KernelCultter.h \
            ../../../cuda/inc/KernelPublic.h \
            ../../../cuda/inc/KernelChaff.h \
            ../../../cuda/inc/KernelJAM.h \
            ../inc/Thread/ThreadClassGenJAM.h \
            ../inc/Thread/ThreadClassBlanketJamGen.h \
            ../inc/Thread/ThreadClassDeceptionJamGen.h \
            ../../../inc/WhiteCalGlobel.h \
            ../inc/Thread/ThreadClassAntennaGen.h \
            ../../../cuda/inc/KernelAntenna.h \
            ../../../cuda/inc/KernelSpline.h \
            ../../../cuda/inc/KernelCoorConver.h \
            ../../../cuda/inc/KernelTarHsys.h \
            ../../../cuda/inc/KernelTarHsysFd.h \
#            ../inc/Spline/Spline.h \
            ../inc/Thread/ThreadSocketTCP.h \
            ../inc/Thread/ThreadSocketTCPExtern.h \
            ../../../inc/globalRadarStruct.h \
            ../inc/Thread/ThreadEchoDataFormat.h \
            ../inc/Thread/ThreadEchoDataSave.h \
            ../inc/Class/ClassRcsRead.h \
            ../inc/Class/ClassClutterRcsRead.h \
            ../inc/Class/ClassCoorDinate.h \
            ../inc/Class/ClassEchoWaveFd.h \
            ../../../inc/GLWidget/_GLWidgetBase.h \
            ../inc/GLWidget/GLWidgetInstanceBWFreq.h \
            ../inc/GLWidget/GLWidgetWave.h \
            ../inc/WidgetShow.h \
            ../../../inc/Extern/wMemoryOperation.h \
            ../../../inc/Extern/float2.h

FORMS    += ../ui/MainWindow.ui \
            ../ui/WidgetShow.ui


#Cuda sources
CUDA_SOURCES += \
#                ../../../cuda/src/KernelSar.cu \
#               ../../../cuda/src/KernelChaff.cu \
#                ../../../cuda/src/KernelCultter.cu \
#                ../../../cuda/src/KernelJAM.cu \
#                ../../../cuda/src/KernelPublic.cu \
#                ../../../cuda/src/KernelAntenna.cu \
#                ../../../cuda/src/KernelSpline.cu \
#                ../../../cuda/src/KernelCoorConver.cu \
#                ../../../cuda/src/KernelTarHsys.cu \
#                ../../../cuda/src/KernelTarHsysFd.cu

#windows下配置CUDA环境依赖
win32{

    INCLUDEPATH +=  C:\cuda\v10.0\include \
                    C:\cuda\v10.0\common\inc

    LIBS += -LC:/CUDA/v10.0/lib/x64 -lcudart -lcublas -lcufft -lcurand

    CUDA_SDK ="C:/cuda/v10.0"
    CUDA_DIR ="C:/cuda/v10.0"
    QMAKE_LIBDIR += $$CUDA_DIR/lib/x64
    SYSTEM_TYPE = 64

    CUDA_ARCH = sm_61
    NVCCFLAGS = --use_fast_math
    CUDA_INC = $$join("C:/cuda/v10.0/include",'" -I"','-I"','"')

    MSVCRT_LINK_FLAG_DEBUG = "/MDd"
    MSVCRT_LINK_FLAG_RELEASE = "/MD"

    CUDA_OBJECTS_DIR = ./

    CONFIG(debug, debug|release){
        cuda_d.input = CUDA_SOURCES
        cuda_d.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}testcuda.obj
        cuda_d.commands = $$CUDA_DIR/bin/nvcc.exe -D_DEBUG $$NVCC_OPTIONS $$CUDA_INC $$CUDA_LIBS --machine $$SYSTEM_TYPE\
                        -arch=$$CUDA_ARCH -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME} -Xcompiler $$MSVCRT_LINK_FLAG_DEBUG
        cuda_d.dependency_type = TYPE_C
        QMAKE_EXTRA_COMPILERS += cuda_d
    }
    else{
        cuda.input = CUDA_SOURCES
        cuda.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}testcuda.obj
        cuda.commands = $$CUDA_DIR/bin/nvcc.exe $$NVCC_OPTIONS $$CUDA_INC $$CUDA_LIBS --machine $$SYSTEM_TYPE\
                        -arch=$$CUDA_ARCH -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME} -Xcompiler $$MSVCRT_LINK_FLAG_RELEASE
        cuda.dependency_type = TYPE_C
        QMAKE_EXTRA_COMPILERS += cuda
    }
    INCLUDEPATH+=$$CUDA_DIR



}
unix{

    LIBS += -lGL

    OBJECTS_DIR = ./debug__
    CUDA_OBJECTS_DIR = ./debug__

     LIBS += -L"/usr/local/lib" \
         -L"/usr/local/cuda/lib64" \
         -lcufft \
         -lcudart \
         -lcublas \
         -lcurand

        CUDA_SDK = "/usr/local/cuda"   # Path to cuda SDK install
        CUDA_DIR = "/usr/local/cuda"            # Path to cuda toolkit install

        # DO NOT EDIT BEYOND THIS UNLESS YOU KNOW WHAT YOU ARE DOING....
        SYSTEM_NAME = linux        # Depending on your system either 'Win32', 'x64', or 'Win64'
        SYSTEM_TYPE = 64            # '32' or '64', depending on your system
        CUDA_ARCH = sm_50           # Type of CUDA architecture,
                                    # for example 'compute_10', 'compute_11', 'sm_10'
        NVCC_OPTIONS = -g
        NVCC_FLAGS += -Xcompiler -fPIC

        # include paths
        INCLUDEPATH += $$CUDA_DIR/include

        # library directories
        QMAKE_LIBDIR += $$CUDA_DIR/lib64/
        QMAKE_LIBDIR += /usr/lib
        # Add the necessary libraries
        # The following makes sure all path names (which often include spaces)
        # are put between quotation marks
        CUDA_INC = $$join(INCLUDEPATH,'" -I"','-I"','"')
        # Configuration of the Cuda compiler
        CONFIG(debug, debug|release) {
            # Debug mode
            cuda.input  = CUDA_SOURCES
            cuda.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}_cuda.o
            cuda.commands = $$CUDA_DIR/bin/clang++ -D_DEBUG $$NVCC_FLAGS $$NVCC_OPTIONS \
                            $$CUDA_INC $$NVCC_LIBS \
                            -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME}
            cuda.dependency_type = TYPE_C
            QMAKE_EXTRA_COMPILERS += cuda
        }
        else {
            # Release mode
            cuda.input = CUDA_SOURCES
            cuda.output = $$CUDA_OBJECTS_DIR/${QMAKE_FILE_BASE}_cuda.o
            cuda.commands = $$CUDA_DIR/bin/clang++ $$NVCC_FLAGS $$NVCC_OPTIONS \
                            $$CUDA_INC $$NVCC_LIBS  \
                            -c -o ${QMAKE_FILE_OUT} ${QMAKE_FILE_NAME}
            cuda.dependency_type = TYPE_C
            QMAKE_EXTRA_COMPILERS += cuda
        }

}

win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../../../lib/ -l_stumempool -lGLWidgetBase
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../../../lib/ -l_stumempoold -lGLWidgetBased
else:unix:!macx: LIBS += -L$$PWD/../../../lib/ -l_stumempool -l_GLWidgetBase  -l_Echo_Kernel


INCLUDEPATH += $$PWD/../../../
DEPENDPATH += $$PWD/../../../


INCLUDEPATH += $$PWD/../
DEPENDPATH += $$PWD/../



