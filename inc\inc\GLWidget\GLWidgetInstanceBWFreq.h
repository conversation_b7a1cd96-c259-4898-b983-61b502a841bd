﻿/*******************************************************************************************
 * FileProperties: 
 *     FileName: gGLWidgetInstanceBWFreq.h
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/GLWidget/GLWidgetInstanceBWFreq.h $
 *         $Author: yening $
 *         $Revision: 19 $
 *         $Date: 2024-12-22 15:19:29 $
*******************************************************************************************/
#ifndef GLWIDGETINSTANCEBWFREQ_H
#define GLWIDGETINSTANCEBWFREQ_H

#include <QGLWidget>
#include <QTimer>
#include"_GLWidgetBase.h"
#include"_stumempool.h"


class GLWidgetInstanceBWFreq :public _GLWidgetBase,_StuMemPool
{
    Q_OBJECT
public:
    explicit GLWidgetInstanceBWFreq();
    ~GLWidgetInstanceBWFreq();
    //绘制收到的数据
    void glDrawSpec();
protected:
    void paintGL();
public:

    
    uint uBw,uFs;
    float gMax,gMin;
public:

    
public slots:
    void onSendInstanceFreShow(void *p);
};


#endif // GLWIDGETINSTANCEBWFREQ_H

