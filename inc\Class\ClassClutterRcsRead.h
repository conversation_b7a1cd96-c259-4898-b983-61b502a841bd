/*******************************************************************************************
 * FileProperties: 
 *     FileName: ClassClutterRcsRead.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Class/ClassClutterRcsRead.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef CLASSCLUTTERRCSREAD_H
#define CLASSCLUTTERRCSREAD_H

#include<QFile>
#include "_stumempool.h"
#include"globalRadarStruct.h"
#include"globalExternalStruct.h"

#pragma pack(1)//1字节对齐

typedef struct _stDateStruct
{
    float pBuffer[2048*2048 + 65536];
}stDateStruct;

typedef struct {
	float *x;
	float *y;
	float *z;
	float *rcs;
	float *phi;
} SeatClutterDataGPU;
//俯仰链表
typedef struct _stClutterPitLink
{
    int		iNodeCount;
    int		pointNum;//散射点总数
	int		SeaState;//海况
	float	pit;
    SeatClutterDataGPU pMissileDataGPU;
    _stClutterPitLink *prev;
    _stClutterPitLink *next;
    _stClutterPitLink *last;
public:
    void init() { memset(this, 0, sizeof(_stClutterPitLink)); prev = last = this; next = NULL; }
}stClutterPitLink;
//海况链表
typedef struct _stClutterSeaStateLink
{
    int		iNodeCount;
    int		iSeatState;			//海况

	float	pitStep;		//俯仰向角度间隔

    stClutterPitLink *pStClutterPitLink;

    _stClutterSeaStateLink *prev;
    _stClutterSeaStateLink *next;
    _stClutterSeaStateLink *last;
public:
    void init() { memset(this, 0, sizeof(_stClutterSeaStateLink)); prev = last = this; next = NULL; }
} stClutterSeaStateLink;


#pragma pack()//1字节对齐

class ClassClutterRcsRead : public _StuMemPool
{

public:
    explicit ClassClutterRcsRead();
    ~ClassClutterRcsRead();
    


private:
    void extractNumbers(char* filename3, double &pit, double &azi);
	

public:
    stClutterSeaStateLink* readClutterRCSFile(char* filename2);//根据海况数据文件存储路径读取海况数据
    stClutterPitLink* CalClutterRcs(int hk, float pit,stClutterSeaStateLink* pSt);//根据海况和擦地角信息获取对应面杂波散射系数信息

	
};

#endif // CLASSCLUTTERRCSREAD_H
