﻿/*******************************************************************************************
 * FileProperties: 
 *     FileName: _GLWidgetBase.h
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/GLWidget/_GLWidgetBase.h $
 *         $Author: xiayuhui $
 *         $Revision: 242 $
 *         $Date: 2025-02-26 08:24:34 $
*******************************************************************************************/
#ifndef _GLWIDGETBASE_H
#define _GLWIDGETBASE_H

#include <QObject>
#include <QGLWidget>
#include<QMenu>
#ifdef linux
#include<GL/gl.h>
#else
#include <GL/gl.h>
#endif // linux

//#include<GL/gl.h>
//#include "gl/GL.h"

#ifdef linux
    #define Q_DECL_EXPORT __attribute__((default))
    #define Q_DECL_IMPORT __attribute__((default))
#else
    #define Q_DECL_EXPORT __declspec(dllexport)
    #define Q_DECL_IMPORT __declspec(dllimport)
#endif

#if defined(_GLWidgetBase_LIBRARY)
#  define _GLWIDGETBASE_EXPORT Q_DECL_EXPORT
#else
#  define _GLWIDGETBASE_EXPORT Q_DECL_IMPORT
#endif

#ifndef UINT
#define UINT unsigned int
#endif

#ifndef DrawRatio
#define DrawRatio 1.f
#endif

#ifndef linux
#include <wingdi.h>
#endif

typedef unsigned short USHORT;
typedef int            INT;
typedef unsigned char BYTE;
typedef long long int64;

enum ERXCHANGEDMODE
{
    ERXCHANGED_NORMA            = 0,//缩放
    ERXCHANGED_TRANSREGION      = 1,//画标记透明区域
};
typedef struct _GLRegionXY
{
    GLfloat x;
    GLfloat y;
}GLRegionXY;

//存储鼠标点击事件信息结构体
typedef struct _ClassDrawMousePara
{
    QSize pSize;//子窗口尺寸
    QPoint mouseDownPoint;//鼠标按下的坐标
    QPoint mouseDownPointLast;//鼠标按下的坐标
    float decX;//子窗口鼠标点击时左右无效区域占比（左右总和）
    float decY;//子窗口鼠标点击时上下无效区域占比（上下总和）
    int maxValPozGlobal;//鼠标标记点在原全频带中的位置

    _ClassDrawMousePara()
    {
        mouseDownPoint.rx() = 0;
        mouseDownPoint.ry() = 0;
        mouseDownPointLast.rx() = 0;
        mouseDownPointLast.ry() = 0;
        decX = 0.005f;
        decY = 0.005f;
    }
}ClassDrawMousePara;

//绘制全频带时参数结构体
typedef struct _ClassDrawAllFreq
{
    float *pDrawAllFreq;
    unsigned int m_FS;//当前采样率
    unsigned int m_FreqRatio;//频率分辨率
    unsigned int m_ShowBW;//显示总带宽
    unsigned int m_InstantBW;//最大瞬时带宽
    unsigned int m_ShowCenterFreq;//显示的中心频率
    int m_ShowTotalDot;
    int m_DrawCurDot;
}ClassDrawAllFreq;

class _GLWIDGETBASE_EXPORT _GLWidgetBase : public QGLWidget
{
    Q_OBJECT
public:
    QMenu *menu;

public:
    explicit _GLWidgetBase(QWidget *parent = 0);
    ~_GLWidgetBase();
    //鼠标按键按下时
    virtual void MouseRightDown(QPoint pos);
    virtual void MouseLeftDown(QPoint pos);
    //鼠标按键抬起时
    //virtual void MouseRightUp(QPoint pos) = 0;
    virtual void MouseLeftUp(QPoint pos);
    //鼠标移动
    virtual void MouseMove(QPoint pos);
    //鼠标抬起
    virtual void MouseRelease(QPoint pos);
    //进入
    virtual void MouseEnter();
    //离开
    virtual void MouseLeave();

    //鼠标坐标转换为GL坐标
    GLRegionXY MousePosToGLPos(QPoint pos);
    //GL坐标转换为鼠标坐标
    QPoint GLPosToMousePos(GLRegionXY glXY);

public:
    virtual void glDrawLines(int Dot,float *pBuffer);
    //标志当前状态是否需要重绘
    void SetCurUpdate(bool state);
    //画坐标
    void drawGridLine(int rowNum = 5,int columNum = 10,float lineWidth = 1,GLenum cap = GL_LINE_STIPPLE);
    //pix piy
    void drawGridCorrd(float rowValStart = 0,float rowValStop = 0,float columValStart = 0,float columValStop = 0,int rowNum = 5,int columNum = 10,const QFont & fnt = QFont());
    void drawGridCorrd(float columValStart,float columValStop,int columNum,const QFont & fnt);
    void MaxMin(float &MaxVal,float &MinVal,short *pBuffer,int length);
public:
    uint DrawDot,DorwTotalDot;
    float *pBuffer;
    QString title;
    int m_width,m_higth;

    ERXCHANGEDMODE eRxChangedMode;
    bool bRxChangedShowFlag;
    bool m_bSymbFixFlag;
public:
    float *paint;
    float fCenterFre;
    uint uStar;
    uint FS;
    uint showBW;
    //当前状态标志
    bool m_bState;

    float rxStrt,rxEnd,rxStrtRegion,rxEndRegion;
    QSize pSize;//子窗口尺寸
    QPoint strtPoint,endPoint;
    QPoint strtPointDraw,endPointDraw;
    bool mouseLeftDown,mouseRightDown;
    ClassDrawAllFreq *pDrawAllFreqPara;
    ClassDrawMousePara *pDrawMousePara;
    int mKeyValue;
    float m_fAmpMin,m_fAmpMax;
    float m_iMaxMinFinal;
    uint uFreqDrawType;
//    short *pShowBuffer;

    uint m_uUAVNum;
    uint *buff;

protected:
    void initializeGL();
    void resizeGL(int w, int h);

signals:
    void sendRxZoomRangePara(float rxStrt,float rxEnd);//

public slots:
    void slotRxZoomRangePara(float _rxStrt,float _rxEnd);
    //鼠标进入事件
   void enterEvent(QEvent *);
   //鼠标离开事件
   void leaveEvent(QEvent *);
   //鼠标按下事件
   void mousePressEvent(QMouseEvent *event);
   //鼠标移动事件
   void mouseMoveEvent(QMouseEvent *event);
   //鼠标释放事件
   void mouseReleaseEvent(QMouseEvent *event);
   //鼠标双击事件
   void mouseDoubleClickEvent(QMouseEvent *event);
   //鼠标滚轮事件
   void wheelEvent(QWheelEvent *event);

   void keyPressEvent(QKeyEvent *event);

   //画鼠标标记矩形区域
   void drawRect(QPoint strtPointDraw, QPoint endPointDraw, QSize pSize);
   //画鼠标标记矩形区域
   void drawRect(double rxStart,double rxEnd,float Y_MAX,float Y_MIN);

   //计算标记点
   void PixPosCalcute(ClassDrawMousePara *pDrawMousePara,
                      ClassDrawAllFreq *pDrawAllFreqPara,
                      int drawTotalDot,         //绘图总点数
                      int drawDotOffset,        //绘图相对于总点数的偏移量
                      int &maxValPoz,
                      float &maxVal);
   //绘制鼠标标记点
   void PixPosDraw(ClassDrawAllFreq *pDrawAllFreqPara,
                     int maxValPoz,
                     float maxVal,
                     float hRatio,
                     float vRatio,
                     int StrtDrawIndex
                     );
   void SetWidgetTitle(double x, double y,QString str,const QFont & fnt = QFont());

signals:
    void sendMainWindowKeyVal(QKeyEvent *event);

protected:
    //弹出菜单
    void customContextMenuRequested(QPoint pos);

private:
    GLuint lists;
    QColor faceColors[4];

    //画背景
    void drawBackground();
public:
    QFont font,font2,font3,font4;

};

#endif // _GLWIDGETBASE_H
