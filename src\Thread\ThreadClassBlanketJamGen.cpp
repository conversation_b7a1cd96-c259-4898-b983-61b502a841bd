/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassInterference.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassBlanketJamGen.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassBlanketJamGen.h"
#include<QThread>
#include <QFile>
#include <QDebug>

#include "ThreadClassSendWaveGen.h"
#include <curand.h>
#include <cuda_runtime_api.h>
#include"KernelJAM.h"
#include"KernelPublic.h"

ThreadClassBlanketJamGen::ThreadClassBlanketJamGen(QObject *parent) : QObject(parent)
{
    _DefineMemPoolCUDA(stEchoData, 2);
	_DefineMemPool(stWaveShow, 10);

	//分配CPU伪内存池空间
    InitMyMalloc(64 * 1024 * 1024);

	//三角函数查表
	double CosValueSample = 1.0 / 4096;
	CosValueLen = 4100;
	m_CosValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_CosValue, 0, sizeof(float)*CosValueLen);
	m_SinValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_SinValue, 0, sizeof(float)*CosValueLen);
	for (int idd = 0; idd < CosValueLen; idd++)
	{
		if (CosValueSample*idd <= 1)
		{
			m_CosValue[idd] = cos(idd*CosValueSample * 2 * PI);
			m_SinValue[idd] = sin(idd*CosValueSample * 2 * PI);
		}
	}

	m_AntParam = new ANTPARAM; memset(m_AntParam, 0, sizeof(ANTPARAM));
	m_AntParamJam = new ANTPARAM; memset(m_AntParamJam, 0, sizeof(ANTPARAM));


    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassBlanketJamGen::~ThreadClassBlanketJamGen()
{
    
}
//初始化GPU设备工作参数
bool ThreadClassBlanketJamGen::InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic)
{
	BufferInit_Cur = 0;
	BufferInit_SizeTotal = (long long)32 * 1024 * 1024;
	if (d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
		m_DevID = DevID;
		d_BufferInit	= d_Buffer;
		d_BufferFather	= d_BufferPublic;
		//分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, 5.0 * 1024 * 1024 * 1024);
	}
	else{//作为独立的线程使用，单独使用一个GPU卡
		m_DevID = -1;
		d_BufferFather = nullptr;
		//分配GPU伪内存池空间
		cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
		cudaMalloc((void**)&d_Buffer, buffer_size);
		d_BufferFather = d_Buffer;
		InitMyMallocCUDA(d_BufferFather, buffer_size);
	}
	//三角函数查表
	long long Bytes = (CosValueLen*sizeof(float) + 16) / 16 * 16;
	dev_CosValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_CosValue, m_CosValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
	dev_SinValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_SinValue, m_SinValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;

	pSumAntennaFunction		= (float*)malloc(sizeof(float)*5000*5000);
	pAziSubAntennaFunction	= (float*)malloc(sizeof(float) * 5000 * 5000);
	pPitSubAntennaFunction	= (float*)malloc(sizeof(float) * 5000 * 5000);
	memset(pSumAntennaFunction, 0, sizeof(float) * 5000 * 5000);
	memset(pAziSubAntennaFunction, 0, sizeof(float) * 5000 * 5000);
	memset(pPitSubAntennaFunction, 0, sizeof(float) * 5000 * 5000);
    return true;
}
//初始化cuda参数
bool ThreadClassBlanketJamGen::InitCUDAPara()
{
	int    BATCH = 1;
	
	cufftPlan1d(&plan1, 16384, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_1k, 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_2k, 2*1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_4k, 4 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_8k, 8 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_16k, 16 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_32k, 32 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_64k, 64 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_128k, 128 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_256k, 256 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_512k, 512 * 1024, CUFFT_C2C, BATCH);

    curandCreateGenerator(&gen_curand, CURAND_RNG_PSEUDO_MRG32K3A);

	return true;
}
cufftHandle ThreadClassBlanketJamGen::CufftPlanCheck(int fftDot)
{
    switch (fftDot)
    {
    case 1024:return plan_1k;
    case 2*1024:return plan_2k;
    case 4*1024:return plan_4k;
    case 8*1024:return plan_8k;
    case 16*1024:return plan_16k;
    case 32*1024:return plan_32k;
    case 64 * 1024:return plan_64k;
    case 128 * 1024:return plan_128k;
    case 256 * 1024:return plan_256k;
    case 512 * 1024:return plan_512k;
    default:return -1;
    }
    return -1;
}
template <class T>
void _CufftExecC2C(cufftHandle plan, T *idata, T *odata, int direction)
{
	if (sizeof(T) > 8){
		cufftExecZ2Z(plan, (cuDoubleComplex*)idata, (cuDoubleComplex*)odata, direction);
	}
	else{
		cufftExecC2C(plan, (cufftComplex*)idata, (cufftComplex*)odata, direction);
	}
}
//压制噪声参数初始化
void ThreadClassBlanketJamGen::JamParamSet(JAM_SUPPRESS_PARAM *JamParam)
{
	//******************压制干扰*****************//
	//压制式噪声公用参数部分（连续，间断，扫频）
	//JamParam->DeltaFc = 0;// 100e6;		//载频偏置（连续，间断，扫频）
	//JamParam->GaussType = 0;		//0高斯，1高斯调频(连续，间断)
	//JamParam->Km		= 1e6;			//噪声调频的调频斜率（指定为高斯调频噪声时设置）
	JamParam->FreqVar	= 100e12;	//噪声调频的频率范围（指定为高斯调频噪声时设置）
	//JamParam->Br		= 200e6;			//带宽（指定为高斯时噪声设置）
	JamParam->AmpVar	= 4;			//高斯噪声方差（指定为高斯时噪声设）
	//扫频噪声干扰
	JamParam->SweepType = TriangleSweep; 	//0三角波TriangleSweep、1正弦波CosSweep、2锯齿波SawtoothSweep
	//间断噪声干扰
	JamParam->PulJamType	= RanPulse;  //0同步脉冲SynPulse，1杂乱脉冲RanPulse
	//JamParam->PRF			= 25e3;		//PRF, 200e3
	JamParam->DutyCycle		= 0.2;	//占空比,0.2
	//梳状谱噪声干扰
	JamParam->CombFreqNum=4;	//梳状谱频点个数
	JamParam->CombJamAmp = (double*)malloc(sizeof(double)*JamParam->CombFreqNum);		////梳状谱噪声系数序列
	JamParam->CombJamAmp[0] = 100;
	JamParam->CombJamAmp[1] = 50;
	JamParam->CombJamAmp[2] = 60;
	JamParam->CombJamAmp[3] = 40;

	JamParam->CombFreqSequency = (double*)malloc(sizeof(double)*JamParam->CombFreqNum);	//梳状谱频点序列
	JamParam->CombFreqSequency[0] = 10e6;		// 不建议超过fs/2
	JamParam->CombFreqSequency[1] = 20e6;
	JamParam->CombFreqSequency[2] = 50e6;
	JamParam->CombFreqSequency[3] = 60e6;

	JamParam->CombBrSequency = (double*)malloc(sizeof(double)*JamParam->CombFreqNum);		//梳状谱带宽序列
	JamParam->CombBrSequency[0] = 30e6;
	JamParam->CombBrSequency[1] = 30e6;
	JamParam->CombBrSequency[2] = 30e6;
	JamParam->CombBrSequency[3] = 30e6;

	//******************压制干扰*****************//
}

template<class T, class T2, class T3, class T4>
void ThreadClassBlanketJamGen::CalAntWeight(int DYT,ANTPARAM *AntParam, RADAR *ParamR, T *Radar_Pos, T *JamDev_Pos, T2 *AntFuction, T2 *AziAntFuction, T2 *PitAntFuction, T3 *AntGain, T4 *Range)
{


	float DeltaAzi		= AntParam->AntAziBound / AntParam->AntFuncAziNum;
	float DeltaEle		= AntParam->AntEleBound / AntParam->AntFuncEleNum;
	float AziBeamLen	= AntParam->AntFuncAziNum;
	float EleBeamLen	= AntParam->AntFuncEleNum;
	float AziSpace		= AntParam->AziSpace;
	float EleSpace		= AntParam->EleSpace;
	float Fc			= ParamR->Fc;
	float AziSlope		= AntParam->AziSlope;
	float EleSlope		= AntParam->EleSlope;
	float AziOverlap	= AntParam->AziOverlap;
	float EleOverlap	= AntParam->EleOverlap;
	float AziAngle0		= AntParam->AziAngle;
	float PitAngle0		= AntParam->PitAngle;

	int AziOverlapLen	= (int)(AziOverlap / DeltaAzi / 2 + 0.5);		//方位向半重叠波束长度
	int EleOverlapLen	= (int)(EleOverlap / DeltaEle / 2 + 0.5);		//俯仰向半重叠波束长度
	int AziStartID, AziEndID, EleStartID, EleEndID;
	AziStartID		= int(-AziOverlapLen + AziBeamLen / 2.0);
	AziEndID		= int(AziOverlapLen + AziBeamLen / 2.0);
	EleStartID		= int(-EleOverlapLen + EleBeamLen / 2.0);
	EleEndID		= int(EleOverlapLen + EleBeamLen / 2.0);

	//计算DYT和干扰机矢量
	float JamDev2Radar[3];
	JamDev2Radar[0] = Radar_Pos[0] - JamDev_Pos[0];
	JamDev2Radar[1] = Radar_Pos[1] - JamDev_Pos[1];
	JamDev2Radar[2] = Radar_Pos[2] - JamDev_Pos[2];

	*Range		= sqrt(JamDev2Radar[0] * JamDev2Radar[0] + JamDev2Radar[1] * JamDev2Radar[1] + JamDev2Radar[2] * JamDev2Radar[2]);
	float temp1 = atan(JamDev2Radar[2] / JamDev2Radar[0]);
	float AziAngle; // 方位角
	if (JamDev2Radar[2] > 0 && JamDev2Radar[0] < 0) AziAngle = temp1 + 2 * PI;
	else if (JamDev2Radar[0] < 0) AziAngle = temp1+PI;
	else AziAngle = temp1;

	float EleAngle	= asin(JamDev2Radar[1] / (*Range));			// 俯仰角
	//相对于波束指向的偏移量
	AziAngle = AziAngle*57.296f - AziAngle0;//57.296f = 180*PI(变成角度值)
	EleAngle = EleAngle*57.296f - PitAngle0;

	int AziID = (int)(AziAngle / DeltaAzi + AziBeamLen / 2.0 - 0.5f);	//方位向由负到正
	int EleID = (int)(EleAngle / DeltaEle + EleBeamLen / 2.0 - 0.5f);	//俯仰向由正到负

	if (AziID == 2400) AziID = 2401;
	if (EleID == 2401) EleID = 2400;

	if (DYT)
	{
		AntGain[0] = (float)(AntFuction[(int)((EleID - 1)*AziBeamLen + AziID)]);
		AntGain[1] = (float)(AziAntFuction[(int)((EleID - 1)*AziBeamLen + AziID)]);
		AntGain[2] = (float)(PitAntFuction[(int)((EleID - 1)*AziBeamLen + AziID)]);
	}
	else
	{
		if (AziID >= AziStartID && AziID < AziEndID && EleID >= EleStartID && EleID < EleEndID){
			AntGain[0] = (float)(AntFuction[(int)((EleID - 1)*AziBeamLen + AziID)] / 4);
		}
		else{
			AntGain[0] = (float)(AntFuction[0] / 4);
		}
	}
}

//扫频模式频点计算
template <class T, class T2, class T3, class T4>
void ThreadClassBlanketJamGen::SweepFreGen(int SweepType, T SweepFmin, T2 SweepFmax, T3 SweepVel, int SweepCyclePRTNum, int SweepPRTNum, T4 *SweepFCur)
{
	//// SweepType：扫频类型：0锯齿波扫描 1三角波扫描 2正弦扫描
	//// SweepFmin：扫频最小频率
	//// SweepFmax：扫频最大频率
	//// SweepVel：扫频速度
	//// SweepCyclePRTNum：扫频周期内PRT个数
	//// SweepPRTNum：扫频时间内PRT个数
	//// PRT：脉冲重复周期

	int idx;
	if (SweepType == SawtoothSweep)
	{
		//锯齿波扫频
		for (idx = 0; idx < SweepPRTNum; idx++)
		{
			SweepFCur[idx] = SweepFmin + SweepVel*(idx % SweepCyclePRTNum);
		}
	}
	else if (SweepType == TriangleSweep)
	{
		//三角波扫频
		int SweepTriMod;
		for (idx = 0; idx < SweepPRTNum; idx++)
		{
			SweepTriMod = idx % SweepCyclePRTNum;
			if (SweepTriMod < round(SweepCyclePRTNum / 2.0))
			{
				SweepFCur[idx] = SweepFmin + 2 * SweepVel*SweepTriMod;
			}
			else
			{
				SweepFCur[idx] = SweepFmax + 2 * SweepVel*(round(SweepCyclePRTNum / 2) - 1 - SweepTriMod);
			}
		}
	}
	else if (SweepType == CosSweep)
	{
		///正弦扫频
		int PhaseTime;
		for (idx = 0; idx < SweepPRTNum; idx++)
		{
			PhaseTime = idx % SweepCyclePRTNum;
			SweepFCur[idx] = (SweepFmin + SweepFmax) / 2.0 + (SweepFmax - SweepFmin) / 2.0 * sin(2 * PI * PhaseTime / SweepCyclePRTNum);
		}
	}
}

//噪声生成
template <class T,class T2,class T3,class T4,class T5,class T6,class T7,class T8>
void ThreadClassBlanketJamGen::NoiseModelGen(int NoiseSigLen, T MeanValue, T2 StdValue, T3 NoiseFc, T4 NoiseBr, T5 NoiseAmp, T6 Fs, T7 *dev_NoiseModel, T8 *dev_RandomComplexf)
{
    //// NoiseSigLen：干扰信号长度
    //// MeanValue：高斯白噪声均值
    //// StdValue：高斯白噪声方差
    //// NoiseFc：干扰信号载频
    //// NoiseBr：干扰信号带宽
    //// NoiseAmp：干扰信号幅度
    //// Fs：采样率

	cudaError_t Err = cudaGetLastError();

    int idx, idy, idz;
    T7 *dev_GaussNoise = (T7 *)_MallocCUDA(sizeof(T7)*NoiseSigLen);
    dim3 ThreadsPerBlock(512,1);
    dim3 BlockNum((NoiseSigLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
	CUDA_Noise_Gauss(0, ThreadsPerBlock, BlockNum, NoiseSigLen, StdValue, MeanValue, dev_CosValue, dev_SinValue, dev_RandomComplexf, dev_GaussNoise);


    ///生成噪声干扰频响曲线，ifft转换到时域得到噪声干扰信号
	int NoiseFcID = round(NoiseFc / Fs*NoiseSigLen) + NoiseSigLen/2;        //载频采样位置
    int NoiseBrLen = round(NoiseBr / Fs*NoiseSigLen);       //带宽采样点数
    if (NoiseBrLen > NoiseSigLen)
        NoiseBrLen = NoiseSigLen;

    T7 *dev_NoiseSigF = (T7 *)_MallocCUDA(sizeof(T7)*NoiseSigLen);
	cudaMemset(dev_NoiseSigF, 0, sizeof(T7)*NoiseSigLen);

	int NoisePosStartID = (int(NoiseFcID - floor(NoiseBrLen / 2.0) + NoiseSigLen)) % NoiseSigLen;
	int NoisePosStopID = (int(NoiseFcID + floor(NoiseBrLen / 2.0) + NoiseSigLen)) % NoiseSigLen;
	
	if (NoiseBrLen == NoiseSigLen)
	{
		NoisePosStartID = 0;
		NoisePosStopID = NoiseBrLen;
	}
    if (NoisePosStartID <= NoisePosStopID)
    {
        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum(((NoisePosStopID - NoisePosStartID + 1) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        int dot = (NoisePosStopID - NoisePosStartID + 1);
		CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, dot, dev_GaussNoise, dev_NoiseSigF + NoisePosStartID, 1.f / NoiseBrLen*NoiseSigLen);
    }
    else
    {
        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum(((NoiseSigLen - NoisePosStartID) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        int dot = (NoiseSigLen - NoisePosStartID);
		CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, dot, dev_GaussNoise, dev_NoiseSigF + NoisePosStartID, 1.f / NoiseBrLen*NoiseSigLen);

        dim3 ThreadsPerBlock2(512,1);
        dim3 BlockNum2(((NoisePosStopID + 1) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        dot = (NoisePosStopID + 1);
		CUDA_ComplexMultCoef(0, ThreadsPerBlock2, BlockNum2, dot, dev_GaussNoise, dev_NoiseSigF + NoisePosStartID, 1.f / NoiseBrLen*NoiseSigLen);
    }
    T7 *dev_NoiseSigIFFT = (T7 *)_MallocCUDA(sizeof(T7)*NoiseSigLen);
    cudaMemset(dev_NoiseSigIFFT, 0, sizeof(T7)*NoiseSigLen);

    cufftHandle cufftPlan = CufftPlanCheck(NoiseSigLen);
    _CufftExecC2C(cufftPlan,dev_NoiseSigF,dev_NoiseSigIFFT,CUFFT_INVERSE);

	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, NoiseSigLen, dev_NoiseSigIFFT, dev_NoiseModel, 1.f / NoiseSigLen*NoiseAmp);
}

//连续噪声
template <class T,class T2>
void ThreadClassBlanketJamGen::BlockJamGen(JAM_SUPPRESS_PARAM *JamParam, double Fs, int PRFID, int PRTLen, T *BlanketJamSig, T2 *dev_RandomComplexf)
{
	NoiseModelGen(PRTLen, 0, JamParam->AmpVar, JamParam->DeltaFc, JamParam->Br, JamParam->JamPower, Fs, BlanketJamSig, dev_RandomComplexf);
}

//间断噪声
//JamSigLen:回波流信号总长度
template <class T,class T2>
void ThreadClassBlanketJamGen::PulseJamGen(JAM_SUPPRESS_PARAM *JamParam, double Fs, int JamSigLen, int PRFID, int PRTLen, T *BlanketJamSig, T2 *dev_RandomComplexf)
{
    int idx = PRFID;
    int PulseTrLen1 = round(JamParam->DutyCycle*PRTLen);
	int PulseTrLen = pow(2, (int)(log2(PulseTrLen1) + 0.5f));

	T *dev_BlanketJamSig = (T *)_MallocCUDA(sizeof(T)*PRTLen);
	cudaMemset(dev_BlanketJamSig, 0, sizeof(T)*PRTLen);
	NoiseModelGen(PulseTrLen, 0, JamParam->AmpVar, JamParam->DeltaFc, JamParam->Br, 1, Fs, dev_BlanketJamSig, dev_RandomComplexf);
	cudaMemcpy(BlanketJamSig, dev_BlanketJamSig, sizeof(T2)*PulseTrLen1, cudaMemcpyDeviceToDevice);
}

//扫频噪声
template <class T,class T2>
void ThreadClassBlanketJamGen::SweepJamGen(JAM_SUPPRESS_PARAM *JamParam, double Fs, int JamSigLen, int PRFID, int PRTLen,T *BlanketJamSig, T2 *dev_RandomComplexf)
{
	int idx = PRFID;

	Complexf *pBlanketJamSig = (Complexf *)_Malloc(sizeof(Complexf) * PRTLen);
	memset(pBlanketJamSig, 0, sizeof(Complexf) * PRTLen);

	switch (JamParam->SweepType)
	{
	case 0:		//三角波
	{
		for (int idz = 0; idz < PRTLen/2; idz++)
		{
			double FcPhase = 2 * PI * (2 * JamParam->Br * JamParam->PRF / Fs*idz + JamParam->DeltaFc) / Fs;
			pBlanketJamSig[idz].x = cos(idz*FcPhase);
			pBlanketJamSig[idz].y = sin(idz*FcPhase);
		}
		for (int idz = PRTLen / 2 + 1; idz < PRTLen; idz++)
		{
			double FcPhase = 2 * PI * (2 * JamParam->Br - 2 * JamParam->Br * JamParam->PRF / Fs*idz + JamParam->DeltaFc) / Fs;
			pBlanketJamSig[idz].x = cos(idz*FcPhase);
			pBlanketJamSig[idz].y = sin(idz*FcPhase);
		}
		break;
	}
	case 1:			//正弦波
	{
		for (int idz = 0; idz < PRTLen; idz++)
		{
			pBlanketJamSig[idz].x = cos((2 * PI * JamParam->DeltaFc*idz / Fs - JamParam->Br / 2 / JamParam->PRF * cos(2 * PI*idz / Fs * JamParam->PRF)));
			pBlanketJamSig[idz].y = sin((2 * PI * JamParam->DeltaFc*idz / Fs - JamParam->Br / 2 / JamParam->PRF * cos(2 * PI*idz / Fs * JamParam->PRF)));
		}
		break;
	}
	case 2:			//锯齿波
	{
		for (int idz = 0; idz < PRTLen; idz++)
		{
			double FcPhase = 2 * PI * (2 * JamParam->Br * JamParam->PRF / Fs*idz + JamParam->DeltaFc) / Fs;
			pBlanketJamSig[idz].x = cos(idz*FcPhase);
			pBlanketJamSig[idz].y = sin(idz*FcPhase);
		}
		break;
	}
	default:
		break;
	}

	cudaMemcpy(BlanketJamSig, pBlanketJamSig, sizeof(T2)*PRTLen, cudaMemcpyHostToDevice);
}

//梳状谱
template <class T, class T2>
void ThreadClassBlanketJamGen::CombSpecJamGen(JAM_SUPPRESS_PARAM *JamParam, double Fs, int JamSigLen, int PRFID, int PRTLen, T *BlanketJamSig, T2 *dev_RandomComplexf)
{
	int idx, idy, idz;
	idx = PRFID;

	Complexf *pBlanketJamSig = (Complexf *)_Malloc(sizeof(Complexf) * PRTLen);
	memset(pBlanketJamSig, 0, sizeof(Complexf) * PRTLen);

	T *dev_BlanketJamSig = (T *)_MallocCUDA(sizeof(T)*PRTLen);

	for (idz = 0; idz < JamParam->CombFreqNum; idz++)
	{
		double FcPhase = 2 * PI*(JamParam->CombFreqSequency[idz] + JamParam->DeltaFc) / Fs;
		for (idy = 0; idy < PRTLen; idy++)
		{
			pBlanketJamSig[idy].x = JamParam->CombJamAmp[idz] * cos(idy*FcPhase);
			pBlanketJamSig[idy].y = JamParam->CombJamAmp[idz] * sin(idy*FcPhase);
		}
		cudaMemcpy(dev_BlanketJamSig, pBlanketJamSig, sizeof(Complexf)*PRTLen, cudaMemcpyHostToDevice);

		dim3 ThreadsPerBlock(512, 1);
		dim3 BlockNum((PRTLen + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
		CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, PRTLen, dev_BlanketJamSig, BlanketJamSig, BlanketJamSig);
	}
	
}
//压制干扰主函数
void ThreadClassBlanketJamGen::slotBlanketJamGen(void *p)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	

	JAM_SUPPRESS_PARAM *JamParam;


}
//压制干扰主函数
void ThreadClassBlanketJamGen::GenBlanketJam(void *p, stEchoData *pStEchoData)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	

	stJamEchoPara *pStJamEchoPara = (stJamEchoPara*)p;

	int FFT_Dot = pow(2,(int)(log2(pStJamEchoPara->SimData.SaveFlie_Nr) + 0.5f));

	JAM_SUPPRESS_PARAM *pJAM_SUPPRESS_PARAM = pStJamEchoPara->pJAM_SUPPRESS_PARAM;
	Complexf *dev_BlanketJamSig = (Complexf*)_MallocCUDA(sizeof(Complexf)*pStJamEchoPara->SimData.SaveFlie_Nr*pStJamEchoPara->SimData.SaveFlie_Na);

	float Lambda = C_ / pStJamEchoPara->ParamR.Fc;

	Complexf *dev_BlanketJamSigSum = (Complexf*)_MallocCUDA(sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
	Complexf *dev_BlanketJamSigAzi = (Complexf*)_MallocCUDA(sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
	Complexf *dev_BlanketJamSigPit = (Complexf*)_MallocCUDA(sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
	cudaMemset(dev_BlanketJamSigSum, 0, sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
	cudaMemset(dev_BlanketJamSigAzi, 0, sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
	cudaMemset(dev_BlanketJamSigPit, 0, sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);


	double *SweepFCur = NULL;
	for (int i = 0; i < pStJamEchoPara->JamNum; i++)
	{
		if (pJAM_SUPPRESS_PARAM[i].BalJamType < 4)
		{

			dev_RandomComplexf = (Complexf*)_MallocCUDA(sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
			//高斯白噪声生成
			curandSetPseudoRandomGeneratorSeed(gen_curand, (time(NULL) + rand()));
			curandGenerateUniform(gen_curand, (float*)dev_RandomComplexf, FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na*2);
		}
		JamParamSet((JAM_SUPPRESS_PARAM*)&pJAM_SUPPRESS_PARAM[i]);
		for (int j = 0; j < pStJamEchoPara->SimData.SaveFlie_Na; j++)
		{
			switch (pJAM_SUPPRESS_PARAM[i].BalJamType)
			{
			case 0:			//连续噪声
			{
				BlockJamGen((JAM_SUPPRESS_PARAM*)&pJAM_SUPPRESS_PARAM[i], pStJamEchoPara->ParamR.Fs, j, FFT_Dot, dev_BlanketJamSig + j*FFT_Dot, dev_RandomComplexf + j*FFT_Dot);
				break;
			}
			case 1:			//间断噪声
			{
				PulseJamGen((JAM_SUPPRESS_PARAM*)&pJAM_SUPPRESS_PARAM[i], pStJamEchoPara->ParamR.Fs, pStJamEchoPara->SimData.SaveFlie_Nr, j, FFT_Dot, dev_BlanketJamSig + j*FFT_Dot, dev_RandomComplexf + j*FFT_Dot);
				break;
			}
			case 2:			//扫频噪声
			{
				SweepJamGen((JAM_SUPPRESS_PARAM*)&pJAM_SUPPRESS_PARAM[i], pStJamEchoPara->ParamR.Fs, pStJamEchoPara->SimData.SaveFlie_Nr, j, FFT_Dot, dev_BlanketJamSig + j*FFT_Dot, dev_RandomComplexf + j*FFT_Dot);
				break;
			}
			case 3:		//梳状谱
			{
				CombSpecJamGen((JAM_SUPPRESS_PARAM*)&pJAM_SUPPRESS_PARAM[i], pStJamEchoPara->ParamR.Fs, pStJamEchoPara->SimData.SaveFlie_Nr, j, FFT_Dot, dev_BlanketJamSig + j*FFT_Dot, dev_RandomComplexf + j*FFT_Dot);
				break;
			}
			default:break;
			}
			//计算DYT在干扰机波束位置的增益 第一步
			float Range = 0; float AntGain[3] = {0};
			double RadarPos[3];
			RadarPos[0] = pStJamEchoPara->RadarPos.NorthPos;
			RadarPos[1] = pStJamEchoPara->RadarPos.SkyPos;
			RadarPos[2] = pStJamEchoPara->RadarPos.EastPos;
			double JamDev_Pos[3];
			JamDev_Pos[0] = pJAM_SUPPRESS_PARAM[i].JamX;
			JamDev_Pos[1] = pJAM_SUPPRESS_PARAM[i].JamY;
			JamDev_Pos[2] = pJAM_SUPPRESS_PARAM[i].JamZ;

			m_AntParamJam->AziAngle = pJAM_SUPPRESS_PARAM[i].JamAzi;
			m_AntParamJam->PitAngle = pJAM_SUPPRESS_PARAM[i].JamPit;
			CalAntWeight(0, m_AntParamJam, (RADAR*)&pStJamEchoPara->ParamR, RadarPos, JamDev_Pos, pSumAntennaFunction, pAziSubAntennaFunction, pPitSubAntennaFunction, AntGain, &Range);
			float RadomeLoss = 1;//干扰机天线罩损耗
			float TranGain = sqrt((pJAM_SUPPRESS_PARAM[i].JamPower*RadomeLoss*Lambda*Lambda / ((16 *PI*PI)* (Range*Range))) * AntGain[0]);

			//计算干扰机在DYT波束指向下的和差差增益
			m_AntParam->AziAngle = pStJamEchoPara->AntParam.AziAngle;
			m_AntParam->PitAngle = pStJamEchoPara->AntParam.PitAngle;

			CalAntWeight(1, m_AntParam, (RADAR*)&pStJamEchoPara->ParamR, JamDev_Pos, RadarPos, pSumAntennaFunction, pAziSubAntennaFunction, pPitSubAntennaFunction, AntGain, &Range);

			float TranGainS = TranGain* sqrt(abs(AntGain[0]));
			float TranGainA = TranGain* sqrt(abs(AntGain[1]));
			float TranGainP = TranGain* sqrt(abs(AntGain[2]));
			dim3 ThreadsPerBlock(512, 1);
			dim3 BlockNum(((FFT_Dot + 1) + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
			CUDA_ComplexMultCoefAdd(0, ThreadsPerBlock, BlockNum, FFT_Dot, dev_BlanketJamSig + j*FFT_Dot, dev_BlanketJamSigSum + j*FFT_Dot, TranGainS);
			if (pStJamEchoPara->SimData.AziEleFalg > 0)
			{
				CUDA_ComplexMultCoefAdd(0, ThreadsPerBlock, BlockNum, FFT_Dot, dev_BlanketJamSig + j*FFT_Dot, dev_BlanketJamSigAzi + j*FFT_Dot, TranGainA);
				CUDA_ComplexMultCoefAdd(0, ThreadsPerBlock, BlockNum, FFT_Dot, dev_BlanketJamSig + j*FFT_Dot, dev_BlanketJamSigPit + j*FFT_Dot, TranGainP);
			}
		}			
	}

	Complexf *dev_SumEchoDataPolarFinal = pStEchoData->dev_SumEchoDataPolarFinal;
	Complexf *dev_AziEchoDataPolarFinal = pStEchoData->dev_AziEchoDataPolarFinal;
	Complexf *dev_EleEchoDataPolarFinal = pStEchoData->dev_EleEchoDataPolarFinal;

	dim3 ThreadsPerBlock(512, 1);
	dim3 BlockNum((pStJamEchoPara->SimData.SaveFlie_Nr*pStEchoData->Na_Save + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
	int dot = pStJamEchoPara->SimData.SaveFlie_Nr*pStEchoData->Na_Save;
	CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, dot, dev_BlanketJamSigSum, dev_SumEchoDataPolarFinal, dev_SumEchoDataPolarFinal);
	if (pStJamEchoPara->SimData.AziEleFalg > 0)
	{
		CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, dot, dev_BlanketJamSigAzi, dev_AziEchoDataPolarFinal, dev_AziEchoDataPolarFinal);
		CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, dot, dev_BlanketJamSigPit, dev_EleEchoDataPolarFinal, dev_EleEchoDataPolarFinal);
	}
	
	//if (m_DevID == 0)
	//{
	//	char *EchoTemp = (char*)_Malloc(sizeof(Complexf)*dot);
	//	cudaMemcpy(EchoTemp, dev_SumEchoDataPolarFinal, sizeof(Complexf)*dot, cudaMemcpyDeviceToHost);
	//	QFile *pQFile2 = new QFile;
	//	pQFile2->setFileName("..\\..\\..\\data\\EchoJAM1");
	//	pQFile2->open(QFile::WriteOnly | QFile::Append);
	//	pQFile2->write((char*)EchoTemp, sizeof(Complexf)*pStJamEchoPara->SimData.SaveFlie_Nr*pStJamEchoPara->SimData.SaveFlie_Na);
	//	pQFile2->close();
	//	delete pQFile2;
	//}

}

//天线
void ThreadClassBlanketJamGen::slotSettingAntPara(void *p)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	ANTPARAM        *AntParam = (ANTPARAM*)p;
	//***********天线加权gpu***********//
	cudaMemcpy(pSumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToHost);
	cudaMemcpy(pAziSubAntennaFunction, AntParam->AziSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToHost);
	cudaMemcpy(pPitSubAntennaFunction, AntParam->PitSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToHost);

	memcpy(m_AntParam, AntParam, sizeof(ANTPARAM));
	memcpy(m_AntParamJam, AntParam, sizeof(ANTPARAM));
}



