/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassInterference.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassDeceptionJamGen.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassDeceptionJamGen.h"
#include<QThread>
#include <QFile>
#include <QDebug>

#include "ThreadClassSendWaveGen.h"
#include <curand.h>
#include <cuda_runtime_api.h>
#include"KernelJAM.h"
#include"KernelPublic.h"

ThreadClassDeceptionJamGen::ThreadClassDeceptionJamGen(QObject *parent) : QObject(parent)
{
    _DefineMemPoolCUDA(stEchoData, 2);
	//分配CPU伪内存池空间
    InitMyMalloc(64 * 1024 * 1024);

	//三角函数查表
	double CosValueSample = 1.0 / 4096;
	CosValueLen = 4100;
	m_CosValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_CosValue, 0, sizeof(float)*CosValueLen);
	m_SinValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_SinValue, 0, sizeof(float)*CosValueLen);
	for (int idd = 0; idd < CosValueLen; idd++)
	{
		if (CosValueSample*idd <= 1)
		{
			m_CosValue[idd] = cos(idd*CosValueSample * 2 * PI);
			m_SinValue[idd] = sin(idd*CosValueSample * 2 * PI);
		}
	}

	m_AntParam = new ANTPARAM; memset(m_AntParam, 0, sizeof(ANTPARAM));
	m_AntParamJam = new ANTPARAM; memset(m_AntParamJam, 0, sizeof(ANTPARAM));

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassDeceptionJamGen::~ThreadClassDeceptionJamGen()
{
    
}
//初始化GPU设备工作参数
bool ThreadClassDeceptionJamGen::InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic)
{
	BufferInit_Cur = 0;
	BufferInit_SizeTotal = (long long)32 * 1024 * 1024;
	if (d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
		m_DevID = DevID;
		d_BufferInit = d_Buffer;
		d_BufferFather = d_BufferPublic;
		//分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, buffer_size);
	}
	else{//作为独立的线程使用，单独使用一个GPU卡
		m_DevID = -1;
		d_BufferFather = nullptr;
		//分配GPU伪内存池空间
		cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
		cudaMalloc((void**)&d_Buffer, buffer_size);
		d_BufferFather = d_Buffer;
		InitMyMallocCUDA(d_BufferFather, 5.0 * 1024 * 1024 * 1024);
	}
	//三角函数查表
	long long Bytes = (CosValueLen*sizeof(float) + 16) / 16 * 16;
	dev_CosValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_CosValue, m_CosValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
	dev_SinValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_SinValue, m_SinValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;

	pSumAntennaFunction = (float*)malloc(sizeof(float) * 5000 * 5000);
	pAziSubAntennaFunction = (float*)malloc(sizeof(float) * 5000 * 5000);
	pPitSubAntennaFunction = (float*)malloc(sizeof(float) * 5000 * 5000);
	memset(pSumAntennaFunction, 0, sizeof(float) * 5000 * 5000);
	memset(pAziSubAntennaFunction, 0, sizeof(float) * 5000 * 5000);
	memset(pPitSubAntennaFunction, 0, sizeof(float) * 5000 * 5000);
    return true;
}
//初始化cuda参数
bool ThreadClassDeceptionJamGen::InitCUDAPara()
{
	int    BATCH = 1;
	
	cufftPlan1d(&plan, 16384, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_1k, 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_2k, 2*1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_4k, 4 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_8k, 8 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_16k, 16 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_32k, 32 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_64k, 64 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_128k, 128 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_256k, 256 * 1024, CUFFT_C2C, BATCH);
    cufftPlan1d(&plan_512k, 512 * 1024, CUFFT_C2C, BATCH);

    curandCreateGenerator(&gen_curand, CURAND_RNG_PSEUDO_MRG32K3A);

	return true;
}
cufftHandle ThreadClassDeceptionJamGen::CufftPlanCheck(int fftDot)
{
    switch (fftDot)
    {
    case 1024:return plan_1k;
    case 2*1024:return plan_2k;
    case 4*1024:return plan_4k;
    case 8*1024:return plan_8k;
    case 16*1024:return plan_16k;
    case 32*1024:return plan_32k;
    case 64 * 1024:return plan_64k;
    case 128 * 1024:return plan_128k;
    case 256 * 1024:return plan_256k;
    case 512 * 1024:return plan_512k;
    default:return -1;
    }
    return -1;
}

//压制噪声参数初始化
void ThreadClassDeceptionJamGen::JamParamSet(JAM_DECEPTION_PARAM *JamParam)
{
	//******************欺骗干扰*****************//
	JamParam->StartTime = 0;				//欺骗干扰仿真开始时间
	JamParam->StopTime = 1;					//欺骗干扰仿真结束时间
	//距离拖引相关设置参数
	JamParam->PullOffRan = 10;				//拖引目标初始斜距，10
	JamParam->PullOffVel = 100000;				//拖引目标速度，100，100000仿真时效果明显
	JamParam->PullOffAcc = 0;				//拖引目标加速度，0
	//速度拖引相关设置参数
	JamParam->DeltaDop = 400;				//固定多普勒拖引速度，400
	JamParam->DeltaRate = 100000;					//多普勒拖引变化率，100000仿真时效果明显
	//密集多假目标设置参数
	JamParam->MulTarNum = 4;				//密集多目标个数
	JamParam->MulPullOffRan = (double*)malloc(sizeof(double)*JamParam->MulTarNum);				//初始拖引目标距离序列
	JamParam->MulPullOffVel = (double*)malloc(sizeof(double)*JamParam->MulTarNum);				//拖引目标速度序列
	JamParam->MulPullOffAcc = (double*)malloc(sizeof(double)*JamParam->MulTarNum);				//拖引目标加速度序列
	JamParam->MulDeltaDop = (double*)malloc(sizeof(double)*JamParam->MulTarNum);				//多普勒拖引速度序列,11,22
	JamParam->MulDeltaRate = (double*)malloc(sizeof(double)*JamParam->MulTarNum);				//多普勒拖引率序列,500,300
	for (int idzzz = 0; idzzz < JamParam->MulTarNum; idzzz++)
	{
		JamParam->MulPullOffRan[idzzz]	= 10 + idzzz * 15;//113
		JamParam->MulPullOffVel[idzzz]	= 100000 + ((double)rand()) / RAND_MAX * 3;//2
		JamParam->MulPullOffAcc[idzzz]	= 0;
		JamParam->MulDeltaDop[idzzz]	= 400;
		JamParam->MulDeltaRate[idzzz]	= 100000;
	}
	//频移干扰设置参数
	JamParam->FreqShift = 10e6;				//频移干扰频移量
	//前沿复制+切片复制干扰
	JamParam->CopyType = 4;
	JamParam->FrontCopyTimes = 3;			//转发复制信号次数,前沿预设为3，若为脉内等间隔采样则为6
	JamParam->FrontDelay = 10e-6;			//干扰机转发延时
	JamParam->FrontSpace = 1e-6;
	JamParam->FrontTp = 1e-6;
	//Jammer[idx].FrontLen		= 1e-6;		//脉冲切片宽度,若为脉内等间隔采样则为1e-6，限制FrontLen<=Tp
	//切片复制干扰
	JamParam->CutType = 1;
	JamParam->CopyTimes = 4;				//转发复制信号次数
	JamParam->CopySpace = 1e-6;
	JamParam->CutDelay = 0;					//干扰机转发延时
	//Jammer[idx].CutLen			= 1e-6;	//脉冲切片宽度Tw
	JamParam->CutEdge = 1e-6;				//切片前沿起始时刻（s）
	JamParam->CutPRT = 5e-6;				//切片周期，限制：CutLen*(CopyTimes+1)<=CutPRT,Ts
	//多普勒闪烁设置参数
	JamParam->FreqDopJam[0] = 1e3;			//多普勒闪烁频率，仅可设置2个
	JamParam->FreqDopJam[1] = 2e3;

	//灵巧噪声设置参数
	JamParam->SmartNoiseBr = 40e6;			//视频噪声带宽(大于信号Br)
	JamParam->SmartNoiseTp = 2e-6;			//视频噪声时宽（小于信号Tp）
	JamParam->SmartNoiseVar = 100;			//视频噪声方差
	//随机假目标设置参数
	JamParam->RandMulTargetNum = 50;
	JamParam->RandRangeVar = 100;
	JamParam->RandDopplerVar = 50;
	JamParam->RandRangeSeq = (double*)malloc(sizeof(double)*JamParam->RandMulTargetNum);
	JamParam->RandDopplerSeq = (double*)malloc(sizeof(double)*JamParam->RandMulTargetNum);
	for (int idy = 0; idy < JamParam->RandMulTargetNum; idy++)
	{
		JamParam->RandRangeSeq[idy] = randn(0, 1)*JamParam->RandRangeVar;
		JamParam->RandDopplerSeq[idy] = randn(0, 1)*JamParam->RandDopplerVar;
	}
	//多普勒噪声干扰设置参数
	JamParam->Kpm = 1;						//多普勒噪声系数
	JamParam->PhaVar = 1;					//相位方差
	//******************欺骗干扰*****************//
}


template<class T, class T2, class T3, class T4>
void ThreadClassDeceptionJamGen::CalAntWeight(int DYT, ANTPARAM *AntParam, RADAR *ParamR, T *Radar_Pos, T *JamDev_Pos, T2 *AntFuction, T2 *AziAntFuction, T2 *PitAntFuction, T3 *AntGain, T4 *Range)
{

	float DeltaAzi = AntParam->AntAziBound / AntParam->AntFuncAziNum;
	float DeltaEle = AntParam->AntEleBound / AntParam->AntFuncEleNum;
	float AziBeamLen = AntParam->AntFuncAziNum;
	float EleBeamLen = AntParam->AntFuncEleNum;
	float AziSpace = AntParam->AziSpace;
	float EleSpace = AntParam->EleSpace;
	float Fc = ParamR->Fc;
	float AziSlope = AntParam->AziSlope;
	float EleSlope = AntParam->EleSlope;
	float AziOverlap = AntParam->AziOverlap;
	float EleOverlap = AntParam->EleOverlap;
	float AziAngle0 = AntParam->AziAngle;
	float PitAngle0 = AntParam->PitAngle;

	int AziOverlapLen = (int)(AziOverlap / DeltaAzi / 2 + 0.5);		//方位向半重叠波束长度
	int EleOverlapLen = (int)(EleOverlap / DeltaEle / 2 + 0.5);		//俯仰向半重叠波束长度
	int AziStartID, AziEndID, EleStartID, EleEndID;
	AziStartID = int(-AziOverlapLen + AziBeamLen / 2.0);
	AziEndID = int(AziOverlapLen + AziBeamLen / 2.0);
	EleStartID = int(-EleOverlapLen + EleBeamLen / 2.0);
	EleEndID = int(EleOverlapLen + EleBeamLen / 2.0);

	//计算DYT和干扰机矢量
	float JamDev2Radar[3];
	JamDev2Radar[0] = Radar_Pos[0] - JamDev_Pos[0];
	JamDev2Radar[1] = Radar_Pos[1] - JamDev_Pos[1];
	JamDev2Radar[2] = Radar_Pos[2] - JamDev_Pos[2];

	*Range = sqrt(JamDev2Radar[0] * JamDev2Radar[0] + JamDev2Radar[1] * JamDev2Radar[1] + JamDev2Radar[2] * JamDev2Radar[2]);
	float temp1 = atan(JamDev2Radar[2] / JamDev2Radar[0]);
	float AziAngle; // 方位角
	if (JamDev2Radar[2] > 0 && JamDev2Radar[0] < 0) AziAngle = temp1 + 2 * PI;
	else if (JamDev2Radar[0] < 0) AziAngle = temp1 + PI;
	else AziAngle = temp1;

	float EleAngle = asin(JamDev2Radar[1] / (*Range));			// 俯仰角
	//相对于波束指向的偏移量
	AziAngle = AziAngle*57.296f - AziAngle0;//57.296f = 180*PI(变成角度值)
	EleAngle = EleAngle*57.296f - PitAngle0;

	int AziID = (int)(AziAngle / DeltaAzi + AziBeamLen / 2.0 - 0.5f);	//方位向由负到正
	int EleID = (int)(EleAngle / DeltaEle + EleBeamLen / 2.0 - 0.5f);	//俯仰向由正到负

	if (AziID == 2400) AziID = 2401;
	if (EleID == 2401) EleID = 2400;

	if (DYT)
	{
		AntGain[0] = (float)(AntFuction[(int)((EleID - 1)*AziBeamLen + AziID)]);
		AntGain[1] = (float)(AziAntFuction[(int)((EleID - 1)*AziBeamLen + AziID)]);
		AntGain[2] = (float)(PitAntFuction[(int)((EleID - 1)*AziBeamLen + AziID)]);
	}
	else
	{
		if (AziID >= AziStartID && AziID < AziEndID && EleID >= EleStartID && EleID < EleEndID){
			AntGain[0] = (float)(AntFuction[(int)((EleID - 1)*AziBeamLen + AziID)] / 4);
		}
		else{
			AntGain[0] = (float)(AntFuction[0] / 4);
		}
	}
}

//噪声生成
template <class T,class T2,class T3,class T4,class T5,class T6,class T7>
void ThreadClassDeceptionJamGen::NoiseModelGen(int NoiseSigLen, T MeanValue, T2 StdValue, T3 NoiseFc, T4 NoiseBr, T5 NoiseAmp, T6 Fs, T7 *dev_NoiseModel)
{
    //// NoiseSigLen：干扰信号长度
    //// MeanValue：高斯白噪声均值
    //// StdValue：高斯白噪声方差
    //// NoiseFc：干扰信号载频
    //// NoiseBr：干扰信号带宽
    //// NoiseAmp：干扰信号幅度
    //// Fs：采样率

    int idx, idy, idz;
    //高斯白噪声生成
    curandSetPseudoRandomGeneratorSeed(gen_curand,(time(NULL) + rand()));
    curandGenerateUniform(gen_curand,(float*)dev_RandomComplexf,NoiseSigLen*2);
//    double d1, d2;
//    T7 *GaussNoise = (fftw_complex*)fftw_malloc(sizeof(fftw_complex)*NoiseSigLen);
//    srand(time(NULL));
//    for (idx = 0; idx < NoiseSigLen; idx++)
//    {
//        d1 = double(rand()) / RAND_MAX;
//        d2 = double(rand()) / RAND_MAX;
//        GaussNoise[idx][0] = StdValue*sqrt(-2.0 * log(d1))*cos(2.0 * PI*d2) + MeanValue;
//        GaussNoise[idx][1] = StdValue*sqrt(-2.0 * log(d2))*sin(2.0 * PI*d1) + MeanValue;
//    }
    T7 *dev_GaussNoise = (T7 *)_MallocCUDA(sizeof(T7)*NoiseSigLen);
    dim3 ThreadsPerBlock(512,1);
    dim3 BlockNum((NoiseSigLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
	CUDA_Noise_Gauss(0, ThreadsPerBlock, BlockNum, NoiseSigLen, StdValue, MeanValue, dev_CosValue, dev_SinValue, dev_RandomComplexf, dev_GaussNoise);

    ///生成噪声干扰频响曲线，ifft转换到时域得到噪声干扰信号
    int NoiseFcID = round(NoiseFc / Fs*NoiseSigLen);        //载频采样位置
    int NoiseBrLen = round(NoiseBr / Fs*NoiseSigLen);       //带宽采样点数
    if (NoiseBrLen > NoiseSigLen)
        NoiseBrLen = NoiseSigLen;

    T7 *dev_NoiseSigF = (T7 *)_MallocCUDA(sizeof(T7)*NoiseSigLen);

//    fftw_complex *NoiseSigF = (fftw_complex*)fftw_malloc(sizeof(fftw_complex)*NoiseSigLen);  //噪声干扰频响曲线（内部是高斯白噪声）
//    memset(NoiseSigF, 0, sizeof(fftw_complex)*NoiseSigLen);
    int NoisePosStartID = (int(NoiseFcID - floor(NoiseBrLen / 2.0) + NoiseSigLen)) % NoiseSigLen;
    int NoisePosStopID = (int(NoiseFcID + floor(NoiseBrLen / 2.0) + NoiseSigLen)) % NoiseSigLen;
    if (NoisePosStartID <= NoisePosStopID)
    {
//        for (idy = NoisePosStartID; idy <= NoisePosStopID; idy++)
//        {
//            NoiseSigF[idy][0] = GaussNoise[idy][0] / NoiseBrLen*NoiseSigLen;
//            NoiseSigF[idy][1] = GaussNoise[idy][1] / NoiseBrLen*NoiseSigLen;
//        }
        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum(((NoisePosStopID - NoisePosStartID + 1) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        int dot = (NoisePosStopID - NoisePosStartID + 1);
		CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, dot, dev_GaussNoise, dev_NoiseSigF, 1.f / NoiseBrLen*NoiseSigLen);
    }
    else
    {
//        for (idy = NoisePosStartID; idy < NoiseSigLen; idy++)
//        {
//            NoiseSigF[idy][0] = GaussNoise[idy][0] / NoiseBrLen*NoiseSigLen;
//            NoiseSigF[idy][1] = GaussNoise[idy][1] / NoiseBrLen*NoiseSigLen;
//        }
        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum(((NoiseSigLen - NoisePosStartID) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        int dot = (NoiseSigLen - NoisePosStartID);
		CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, dot, dev_GaussNoise, dev_NoiseSigF, 1.f / NoiseBrLen*NoiseSigLen);

//        for (idy = 0; idy <= NoisePosStopID; idy++)
//        {
//            NoiseSigF[idy][0] = GaussNoise[idy][0] / NoiseBrLen*NoiseSigLen;
//            NoiseSigF[idy][1] = GaussNoise[idy][1] / NoiseBrLen*NoiseSigLen;
//        }
        dim3 ThreadsPerBlock2(512,1);
        dim3 BlockNum2(((NoisePosStopID + 1) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        dot = (NoisePosStopID + 1);
		CUDA_ComplexMultCoef(0, ThreadsPerBlock2, BlockNum2, dot, dev_GaussNoise, dev_NoiseSigF, 1.f / NoiseBrLen*NoiseSigLen);
    }

    T7 *dev_NoiseSigIFFT = (T7 *)_MallocCUDA(sizeof(T7)*NoiseSigLen);
    memset(dev_NoiseSigIFFT, 0, sizeof(T7)*NoiseSigLen);

//    fftw_complex *NoiseSigIFFT = (fftw_complex*)fftw_malloc(sizeof(fftw_complex)*NoiseSigLen);  //噪声干扰频响曲线（内部是高斯白噪声）
//    memset(NoiseSigIFFT, 0, sizeof(fftw_complex)*NoiseSigLen);
//    IFFTComplex(NoiseSigLen, NoiseSigF, NoiseSigIFFT);			//ifft
    cufftHandle cufftPlan = CufftPlanCheck(NoiseSigLen);
    _CufftExecC2C(cufftPlan,dev_NoiseSigF,dev_NoiseSigIFFT,CUFFT_INVERSE);

//    for (idz = 0; idz < NoiseSigLen; idz++)
//    {
//        NoiseModel[idz][0] = NoiseAmp * NoiseSigIFFT[idz][0];
//        NoiseModel[idz][1] = NoiseAmp * NoiseSigIFFT[idz][1];
//    }

	CUDA_ComplexMultCoef(0, ThreadsPerBlock, BlockNum, NoiseSigLen, dev_NoiseSigIFFT, dev_NoiseModel, 1.f / NoiseSigLen*NoiseAmp);
}

//距离拖引
template<class T,class T2>
void ThreadClassDeceptionJamGen::RangePull(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, double Tr, int JamSigLen, double SimTime,
											double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    int TrLen = round(Tr*Fs);
    double RangeNew;
    int DelayID;

	if (SimTime >= pJAM_DECEPTION_PARAM->StartTime & SimTime <= pJAM_DECEPTION_PARAM->StopTime)
    {
		RangeNew = Range + pJAM_DECEPTION_PARAM->PullOffRan + pJAM_DECEPTION_PARAM->PullOffVel*SimTime + 0.5* pJAM_DECEPTION_PARAM->PullOffAcc*SimTime*SimTime;        //每个PRT时刻距离
        DelayID = round((2 * RangeNew / SPEEDLIGHT)*Fs);

        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
		double phase0 = -2 * RangeNew / Lambda;
		double phase1Coef = (2 * (pJAM_DECEPTION_PARAM->PullOffVel + pJAM_DECEPTION_PARAM->PullOffAcc*SimTime) / Lambda) / Fs;
        CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);
    }
}


//速度拖引
template<class T,class T2>
void ThreadClassDeceptionJamGen::VelocityPull(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, double Tr, int JamSigLen, double SimTime,
                                        double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    int TrLen = round(Tr*Fs);
    double RangeNew;
    int DelayID;

	if (SimTime >= pJAM_DECEPTION_PARAM->StartTime & SimTime <= pJAM_DECEPTION_PARAM->StopTime)
    {
		RangeNew = Range + (pJAM_DECEPTION_PARAM->DeltaDop + pJAM_DECEPTION_PARAM->DeltaRate*SimTime)*Lambda / 2 * SimTime;		//每个PRT时刻距离
        DelayID = round((2 * RangeNew / SPEEDLIGHT)*Fs);

        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        double phase0 = -2*RangeNew / Lambda;
		double phase1Coef = (pJAM_DECEPTION_PARAM->DeltaDop + pJAM_DECEPTION_PARAM->DeltaRate*SimTime) / Fs;
        CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
                        dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);
    }

}

//距离速度同步拖引
template<class T,class T2>
void ThreadClassDeceptionJamGen::RangeVelocityPull(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, double Tr, int JamSigLen, double SimTime,
                                        double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    int TrLen = round(Tr*Fs);
    double RangeNew;
    int idz;
    int DelayID;


	if (SimTime >= pJAM_DECEPTION_PARAM->StartTime & SimTime <= pJAM_DECEPTION_PARAM->StopTime)
    {
		//for (idz = 0; idz < pJAM_DECEPTION_PARAM->MulTarNum; idz++)
  //      {
		//	RangeNew = Range + pJAM_DECEPTION_PARAM->MulPullOffRan[idz] + pJAM_DECEPTION_PARAM->MulPullOffVel[idz] * SimTime + 0.5*pJAM_DECEPTION_PARAM->MulPullOffAcc[idz] * SimTime*SimTime
		//		+ (pJAM_DECEPTION_PARAM->MulDeltaDop[idz] + pJAM_DECEPTION_PARAM->MulDeltaRate[idz] * SimTime)*Lambda / 2 * SimTime;		//每个PRT时刻距离
  //          DelayID = round((2 * RangeNew / SPEEDLIGHT)*Fs);

  //          dim3 ThreadsPerBlock(512,1);
  //          dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
  //          double phase0 = -2*RangeNew / Lambda;
		//	double phase1Coef = (2 * (pJAM_DECEPTION_PARAM->MulPullOffVel[idz] + pJAM_DECEPTION_PARAM->MulPullOffAcc[idz] * SimTime) / Lambda) / Fs
		//		+ (pJAM_DECEPTION_PARAM->MulDeltaDop[idz] + pJAM_DECEPTION_PARAM->MulDeltaRate[idz] * SimTime) / Fs;       //每个PRT时刻多普勒
  //          CUDA_JAM_PullAdd(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
  //                          dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);
  //      }
		RangeNew = Range + (pJAM_DECEPTION_PARAM->DeltaDop + pJAM_DECEPTION_PARAM->DeltaRate*SimTime)*Lambda / 2 * SimTime+ pJAM_DECEPTION_PARAM->PullOffRan 
					+ pJAM_DECEPTION_PARAM->PullOffVel*SimTime + 0.5* pJAM_DECEPTION_PARAM->PullOffAcc*SimTime*SimTime;		//每个PRT时刻距离
		DelayID = round((2 * RangeNew / SPEEDLIGHT)*Fs);

		dim3 ThreadsPerBlock(512, 1);
		dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
		double phase0 = -2 * RangeNew / Lambda;
		double phase1Coef = (pJAM_DECEPTION_PARAM->DeltaDop + pJAM_DECEPTION_PARAM->DeltaRate*SimTime) / Fs 
							+ (2 * (pJAM_DECEPTION_PARAM->PullOffVel + pJAM_DECEPTION_PARAM->PullOffAcc*SimTime) / Lambda) / Fs;
		CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
			dev_SourceSignal, dev_CosValue, dev_SinValue, dev_DeceptionJamSig);

    }
}

//频移干扰
template<class T,class T2>
void ThreadClassDeceptionJamGen::FreqShiftJam(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, double Tr, int JamSigLen, double SimTime,
                                        double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    int TrLen = round(Tr*Fs);
    int DelayID;

	if (SimTime >= pJAM_DECEPTION_PARAM->StartTime & SimTime <= pJAM_DECEPTION_PARAM->StopTime)
    {
        DelayID = round((2 * Range / SPEEDLIGHT)*Fs);

        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        double phase0 = -2*Range / Lambda;
		double phase1Coef = pJAM_DECEPTION_PARAM->FreqShift / Fs;
        CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
                        dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);

    }
}

//密集复制+重复转发干扰
template<class T,class T2>
void ThreadClassDeceptionJamGen::CopyTransmitJam(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, int JamSigLen, double SimTime, int PRTLen,
                                        double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    double PhaseValue;
    T2 DopPhase;
    int idx, idz;
    int DelayID;

	if (SimTime >= pJAM_DECEPTION_PARAM->StartTime & SimTime <= pJAM_DECEPTION_PARAM->StopTime)
    {
		if (pJAM_DECEPTION_PARAM->CopyType == 1)					//密集复制干扰
        {
			DelayID = round((2 * Range / SPEEDLIGHT + pJAM_DECEPTION_PARAM->FrontDelay)*Fs);
			int DelaySpace = round(pJAM_DECEPTION_PARAM->FrontSpace*Fs);       //密集复制信号之间的间隔
			int	FrontLen = round(pJAM_DECEPTION_PARAM->FrontTp*Fs);       //复制信号切片的长度

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((FrontLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);

            PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
            DopPhase.x = cos(PhaseValue);
            DopPhase.y = sin(PhaseValue);
			for (idz = 0; idz < pJAM_DECEPTION_PARAM->FrontCopyTimes; idz++)
            {
                CUDA_JAM_PullAddCopy(0, ThreadsPerBlock, BlockNum, FrontLen, JamSigLen, DelayID + DelaySpace*idz, dev_SourceSignal,
                                     DopPhase.x, DopPhase.y,dev_DeceptionJamSig);

            }
        }

		else if (pJAM_DECEPTION_PARAM->CopyType == 2)					//重复转发干扰
        {
			DelayID = round((2 * Range / SPEEDLIGHT + pJAM_DECEPTION_PARAM->FrontDelay)*Fs);
			int DelaySpace = round((pJAM_DECEPTION_PARAM->CopySpace + pJAM_DECEPTION_PARAM->FrontTp)*Fs);       //转发信号之间的间隔
			int	FrontLen = round(pJAM_DECEPTION_PARAM->FrontTp*Fs);       //复制信号切片的长度
            PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
            DopPhase.x = cos(PhaseValue);
            DopPhase.y = sin(PhaseValue);

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((FrontLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
			for (idz = 0; idz < pJAM_DECEPTION_PARAM->CopyTimes; idz++)
            {
                CUDA_JAM_PullAddCopy(0, ThreadsPerBlock, BlockNum, FrontLen, JamSigLen, DelayID + DelaySpace*idz, dev_SourceSignal,
                                     DopPhase.x, DopPhase.y,dev_DeceptionJamSig);

            }
        }

		else if (pJAM_DECEPTION_PARAM->CopyType == 3)					//先密集复制再重复转发干扰
        {
			DelayID = round((2 * Range / SPEEDLIGHT + pJAM_DECEPTION_PARAM->FrontDelay)*Fs);
			int DelaySpace = round(pJAM_DECEPTION_PARAM->FrontSpace*Fs);       //复制信号之间的间隔
			int	FrontLen = round(pJAM_DECEPTION_PARAM->FrontTp*Fs);       //复制信号切片的长度
            PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
            DopPhase.x = cos(PhaseValue);
            DopPhase.y = sin(PhaseValue);
            T2 *dev_JamCopyTemp = (T2*)_MallocCUDA(sizeof(T2)*PRTLen);
            cudaMemset(dev_JamCopyTemp, 0, sizeof(T2)*PRTLen);

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((FrontLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
			for (idz = 0; idz < pJAM_DECEPTION_PARAM->FrontCopyTimes; idz++)
            {
                CUDA_JAM_PullAddCopy(0, ThreadsPerBlock, BlockNum, FrontLen, PRTLen, DelayID + DelaySpace*idz, dev_SourceSignal,
                                     DopPhase.x, DopPhase.y,dev_DeceptionJamSig);
                cudaMemcpy(&dev_JamCopyTemp[DelaySpace*idz],&dev_DeceptionJamSig[DelayID + DelaySpace*idz],sizeof(T2)*PRTLen,cudaMemcpyDeviceToDevice);
            }

			int CopyLen = round((pJAM_DECEPTION_PARAM->FrontTp + pJAM_DECEPTION_PARAM->FrontSpace*(pJAM_DECEPTION_PARAM->FrontCopyTimes - 1))*Fs);       //转发信号切片的长度
			int DelaySpaceCopy = round(pJAM_DECEPTION_PARAM->CopySpace*Fs) + CopyLen;       //转发信号之间的间隔
            BlockNum.x = ((JamSigLen - (DelayID + DelaySpaceCopy*idz )) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x;
			for (idz = 0; idz < pJAM_DECEPTION_PARAM->CopyTimes; idz++)
            {
                CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, JamSigLen - (DelayID + DelaySpaceCopy*idz), dev_JamCopyTemp,
                                (T2*)&dev_DeceptionJamSig[DelayID + DelaySpaceCopy*idz], (T2*)&dev_DeceptionJamSig[DelayID + DelaySpaceCopy*idz]);
            }
        }//else if (JamParam.CopyType == 3)

		else if (pJAM_DECEPTION_PARAM->CopyType == 4)		//先重复转发再密集复制干扰
        {
			DelayID = round((2 * Range / SPEEDLIGHT + pJAM_DECEPTION_PARAM->FrontDelay)*Fs);
			int DelaySpaceCopy = round((pJAM_DECEPTION_PARAM->CopySpace + pJAM_DECEPTION_PARAM->FrontTp)*Fs);       //转发信号之间的间隔
			int	FrontLen = round(pJAM_DECEPTION_PARAM->FrontTp*Fs);       //复制信号切片的长度
            PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
            DopPhase.x = cos(PhaseValue);
            DopPhase.y = sin(PhaseValue);
            T2 *dev_JamCopyTemp = (T2*)_MallocCUDA(sizeof(T2)*PRTLen);
            cudaMemset(dev_JamCopyTemp, 0, sizeof(T2)*PRTLen);

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((FrontLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
			for (idz = 0; idz < pJAM_DECEPTION_PARAM->CopyTimes; idz++)
            {
                CUDA_JAM_PullAddCopy(0, ThreadsPerBlock, BlockNum, FrontLen, PRTLen, DelaySpaceCopy*idz, dev_SourceSignal,
                                     DopPhase.x, DopPhase.y,dev_JamCopyTemp);
            }

			int DelaySpace = round(pJAM_DECEPTION_PARAM->FrontSpace*Fs);       //复制信号之间的间隔
            BlockNum.x = ((JamSigLen - (DelayID + DelaySpace*idz )) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x;
			for (idz = 0; idz < pJAM_DECEPTION_PARAM->FrontCopyTimes; idz++)
            {
                CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, JamSigLen - (DelayID + DelaySpace*idz), dev_JamCopyTemp,
                                (T2*)&dev_DeceptionJamSig[DelayID + DelaySpace*idz], (T2*)&dev_DeceptionJamSig[DelayID + DelaySpace*idz]);

            }
        }//else if (JamParam.CopyType == 4)
    }
}

//切片干扰
template<class T,class T2>
void ThreadClassDeceptionJamGen::CutJam(JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM, double Fs, double Tr, int JamSigLen, int PRTLen, double SimTime,
                                        double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    int TrLen = round(Tr*Fs);
    double PhaseValue;
    T2 DopPhase;
    int idx, idz;
    int DelayID;

	if (SimTime >= pJAM_DECEPTION_PARAM->StartTime & SimTime <= pJAM_DECEPTION_PARAM->StopTime)
    {
		if (pJAM_DECEPTION_PARAM->CutType == 1)			//只切片
        {
			DelayID = round((2 * Range / SPEEDLIGHT + SimTime + pJAM_DECEPTION_PARAM->CutDelay + pJAM_DECEPTION_PARAM->CutEdge)*Fs);
			int	CutPRTLen = round(pJAM_DECEPTION_PARAM->CutPRT*Fs);       //切片周期长度
			int	CutEdgeLen = round(pJAM_DECEPTION_PARAM->CutEdge*Fs);       //切片前沿起始长度
            PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
            DopPhase.x = cos(PhaseValue);
            DopPhase.y = sin(PhaseValue);


            int Dot1 = JamSigLen - (DelayID + CutEdgeLen + CutPRTLen*idz);
            int Dot2 = TrLen -(CutEdgeLen + CutPRTLen*idz);
            int Dot = Dot1 > Dot2 ? Dot2:Dot1;
            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((Dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
            for (idz = 0; idz < CutPRTLen; idz++)
            {
                CUDA_JAM_PullAddCut(0, ThreadsPerBlock, BlockNum, Dot, (T*)&dev_SourceSignal[CutEdgeLen + CutPRTLen*idz],
                                     DopPhase.x, DopPhase.y,(T2*)&dev_DeceptionJamSig[DelayID + CutEdgeLen + CutPRTLen*idz]);

            }
        }//if (JamParam.CutType == 1)

		else if (pJAM_DECEPTION_PARAM->CutType == 2)			//先切片再重复转发
        {
			DelayID = round((2 * Range / SPEEDLIGHT + SimTime + pJAM_DECEPTION_PARAM->CutDelay + pJAM_DECEPTION_PARAM->CutEdge)*Fs);
			int	CutPRTLen = round(pJAM_DECEPTION_PARAM->CutPRT*Fs);       //切片周期长度
			int	CutEdgeLen = round(pJAM_DECEPTION_PARAM->CutEdge*Fs);       //切片前沿起始长度
            PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
            DopPhase.x = cos(PhaseValue);
            DopPhase.y = sin(PhaseValue);

            T2 *dev_JamCopyTemp = (T2*)_MallocCUDA(sizeof(T2)*PRTLen);
            cudaMemset(dev_JamCopyTemp, 0, sizeof(T2)*PRTLen);
            int dot = TrLen - (CutEdgeLen + CutPRTLen*idz);

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
            for (idz = 0; idz < CutPRTLen; idz++)
            {
                CUDA_JAM_PullAddCut(0, ThreadsPerBlock, BlockNum, dot, (T*)&dev_SourceSignal[CutEdgeLen + CutPRTLen*idz],
                                     DopPhase.x, DopPhase.y,(T2*)&dev_JamCopyTemp[CutEdgeLen + CutPRTLen*idz]);
            }

			int DelaySpaceCopy = round((pJAM_DECEPTION_PARAM->CopySpace + Tr)*Fs);       //转发信号之间的间隔
            BlockNum.x = ((JamSigLen - (DelayID + DelaySpaceCopy*idz )) + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x;
			for (idz = 0; idz < pJAM_DECEPTION_PARAM->CopyTimes; idz++)
            {
                CUDA_ComplexAdd(0, ThreadsPerBlock, BlockNum, JamSigLen - (DelayID + DelaySpaceCopy*idz), dev_JamCopyTemp,
                                (T2*)&dev_DeceptionJamSig[DelayID + DelaySpaceCopy*idz], (T2*)&dev_DeceptionJamSig[DelayID + DelaySpaceCopy*idz]);

            }
        }//else if (JamParam.CutType == 2)
    }

}

// 多普勒闪烁干扰
template<class T,class T2>
void ThreadClassDeceptionJamGen::DopplerJamGen(JAM_PARAM JamParam, double Fs, double Tr, int JamSigLen, int PRFID, double SimTime,
                                        double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    int TrLen = round(Tr*Fs);

    int idx = PRFID;

    int DelayID;


    if (SimTime >= JamParam.StartTime & SimTime <= JamParam.StopTime)
    {
        DelayID = round((2 * Range / SPEEDLIGHT + SimTime)*Fs);
        int FreqDopJamPos = idx % JamParam.FreqDopNum;
        double DopFreqShift = JamParam.FreqDopJam[FreqDopJamPos];

        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        double phase0 = -2*Range / Lambda;
        double phase1Coef = DopFreqShift/ Fs;
        CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
                        dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);
    }
}

// 灵巧噪声干扰
template<class T,class T2>
void ThreadClassDeceptionJamGen::NoiseJamGen(JAM_PARAM JamParam, double Fs, int JamSigLen, int PRFID, int PRTLen, double SimTime,
                                        double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    double  PhaseValue;
    T2 DopPhase;
    int DelayID;


    if (SimTime >= JamParam.StartTime & SimTime <= JamParam.StopTime)
    {
        DelayID = round((2 * Range / SPEEDLIGHT + SimTime)*Fs);       //每个PRT时刻多普勒
        PhaseValue = -4 * PI*Range / Lambda;       //每个PRT时刻多普勒
        DopPhase.x = cos(PhaseValue);
        DopPhase.y = sin(PhaseValue);
        T2 *dev_SmartNoisePhase = (T2*)_MallocCUDA(sizeof(T2)*PRTLen);

        T2 *dev_SmartNoise0 = (T2*)_MallocCUDA(sizeof(T2)*PRTLen);
        cudaMemset(dev_SmartNoise0, 0, sizeof(T2)*PRTLen);
        NoiseModelGen(PRTLen, 0, JamParam.SmartNoiseVar, 0, JamParam.SmartNoiseBr, 1, Fs, dev_SmartNoise0);
        int dot = JamSigLen - DelayID;
        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum((dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, dev_SourceSignal, dev_SmartNoise0, dev_SmartNoisePhase, 1);

        CUDA_JAM_PullAddCut(0, ThreadsPerBlock, BlockNum, dot, dev_SmartNoisePhase, DopPhase.x, DopPhase.y, (T2*)&dev_DeceptionJamSig[DelayID]);

    }

}


// 随机假目标干扰
template<class T,class T2>
void ThreadClassDeceptionJamGen::RandMulTargetJamGen(JAM_PARAM JamParam, double Fs, double Tr, int JamSigLen, int PRFID, double SimTime,
                                        double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    int TrLen = round(Tr*Fs);
    double RangeNew;
    int idx, idz;
    int DelayID;

    idx = PRFID;

    if (SimTime >= JamParam.StartTime & SimTime <= JamParam.StopTime)
    {
        for (idz = 0; idz < JamParam.RandMulTargetNum; idz++)
        {
            RangeNew = Range + JamParam.RandRangeSeq[idz];        //每个PRT时刻距离
            DelayID = round((2 * RangeNew / SPEEDLIGHT + SimTime)*Fs);       //每个PRT时刻多普勒

            dim3 ThreadsPerBlock(512,1);
            dim3 BlockNum((TrLen + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
            double phase0 = -2*RangeNew / Lambda;
            double phase1Coef = JamParam.RandDopplerSeq[idz]/ Fs;
            CUDA_JAM_Pull(0, ThreadsPerBlock, BlockNum, TrLen, JamSigLen, DelayID, phase0, phase1Coef,
                            dev_SourceSignal,dev_CosValue, dev_SinValue,dev_DeceptionJamSig);
        }
    }
}

// 多普勒噪声干扰
template<class T,class T2>
void ThreadClassDeceptionJamGen::DopplerNoiseJamGen(JAM_PARAM JamParam, double Fs, int JamSigLen, double SimTime,
                                        double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig)
{
    double PhaseValue;
    T2 DopPhase;
    int DelayID;


    if (SimTime >= JamParam.StartTime & SimTime <= JamParam.StopTime)
    {
        DelayID = round((2 * Range / SPEEDLIGHT + SimTime)*Fs);       //每个PRT时刻多普勒
        PhaseValue = -4 * PI*Range / Lambda + 2 * PI*JamParam.Kpm*sqrt(JamParam.PhaVar)*double(rand() / RAND_MAX);       //每个PRT时刻多普勒
        DopPhase.x = cos(PhaseValue);
        DopPhase.y = sin(PhaseValue);

        int dot = JamSigLen - DelayID;
        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum((dot + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        CUDA_JAM_PullAddCut(0, ThreadsPerBlock, BlockNum, dot, dev_SourceSignal, DopPhase.x, DopPhase.y, (T2*)&dev_DeceptionJamSig[DelayID]);
    }
}


//欺骗干扰主函数
void ThreadClassDeceptionJamGen::GenDeceptionJam(void *p, stEchoData *pStEchoData)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	stJamEchoPara *pStJamEchoPara = (stJamEchoPara*)p;

	int FFT_Dot = pow(2, (int)(log2(pStJamEchoPara->SimData.SaveFlie_Nr) + 0.5f));


	//发射信号生成
	Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) * pStJamEchoPara->ParamR.Nr);
	memset(BaseSignal, 0, sizeof(Complexf) * pStJamEchoPara->ParamR.Nr);
	int TrNum = (int)(pStJamEchoPara->ParamR.Fs*pStJamEchoPara->ParamR.Tp + 0.5);		//一个脉宽的点数
	pStJamEchoPara->ParamR.Kr = pStJamEchoPara->ParamR.Br / pStJamEchoPara->ParamR.Tp;
	ThreadClassSendWaveGen::Instance()->TransSignalSim((RADAR*)&pStJamEchoPara->ParamR, (SimStruct*)&pStJamEchoPara->SimData, TrNum, BaseSignal);
	Complexf *dev_SourceSignal = (Complexf *)_MallocCUDA(sizeof(Complexf) * pStJamEchoPara->ParamR.Nr);
	cudaMemset(dev_SourceSignal, 0, sizeof(Complexf) * pStJamEchoPara->ParamR.Nr);
	cudaMemcpy(dev_SourceSignal, BaseSignal, sizeof(Complexf) * pStJamEchoPara->ParamR.Nr,cudaMemcpyHostToDevice);

	JAM_DECEPTION_PARAM *pJAM_DECEPTION_PARAM = pStJamEchoPara->pJAM_DECEPTION_PARAM;
	Complexf *dev_DeceptionJamSig = (Complexf*)_MallocCUDA(sizeof(Complexf)*pStJamEchoPara->SimData.SaveFlie_Nr*pStJamEchoPara->SimData.SaveFlie_Na);

	double Lambda = C_ / pStJamEchoPara->ParamR.Fc;
	double PRT = 1.0 / pStJamEchoPara->ParamR.Prf;
	double Range =sqrt((pStJamEchoPara->Target[0].NorthPos - pStJamEchoPara->RadarPos.NorthPos)*(pStJamEchoPara->Target[0].NorthPos - pStJamEchoPara->RadarPos.NorthPos) + 
						(pStJamEchoPara->Target[0].SkyPos - pStJamEchoPara->RadarPos.SkyPos)*(pStJamEchoPara->Target[0].SkyPos - pStJamEchoPara->RadarPos.SkyPos) +
						(pStJamEchoPara->Target[0].EastPos - pStJamEchoPara->RadarPos.EastPos)*(pStJamEchoPara->Target[0].EastPos - pStJamEchoPara->RadarPos.EastPos));
	Range = Range - pStJamEchoPara->SimData.Rmin;
	int PRTLen =round(PRT*pStJamEchoPara->ParamR.Fs);

	Complexf *dev_DeceptionJamSigSum = (Complexf*)_MallocCUDA(sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
	Complexf *dev_DeceptionJamSigAzi = (Complexf*)_MallocCUDA(sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
	Complexf *dev_DeceptionJamSigPit = (Complexf*)_MallocCUDA(sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
	cudaMemset(dev_DeceptionJamSigSum, 0, sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
	cudaMemset(dev_DeceptionJamSigAzi, 0, sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);
	cudaMemset(dev_DeceptionJamSigPit, 0, sizeof(Complexf)*FFT_Dot*pStJamEchoPara->SimData.SaveFlie_Na);


	double *SweepFCur = NULL;
	for (int i = 0; i < pStJamEchoPara->JamNum; i++)
	{
		JamParamSet((JAM_DECEPTION_PARAM*)&pJAM_DECEPTION_PARAM[i]);
		for (int j = 0; j < pStJamEchoPara->SimData.SaveFlie_Na; j++)
		{
			switch (pJAM_DECEPTION_PARAM[i].PullOffType)
			{
			case 0:			//距离拖引
			{
				RangePull((JAM_DECEPTION_PARAM*)&pJAM_DECEPTION_PARAM[i], pStJamEchoPara->ParamR.Fs, pStJamEchoPara->ParamR.Tp, FFT_Dot, j*PRT, Range, Lambda, dev_SourceSignal, dev_DeceptionJamSig + j*FFT_Dot);
				break;
			}
			case 1:			//速度拖引
			{
				VelocityPull((JAM_DECEPTION_PARAM*)&pJAM_DECEPTION_PARAM[i], pStJamEchoPara->ParamR.Fs, pStJamEchoPara->ParamR.Tp, FFT_Dot, j*PRT, Range, Lambda, dev_SourceSignal, dev_DeceptionJamSig + j*FFT_Dot);
				break;
			}
			case 2:			//距离速度同步拖引
			{
				RangeVelocityPull((JAM_DECEPTION_PARAM*)&pJAM_DECEPTION_PARAM[i], pStJamEchoPara->ParamR.Fs, pStJamEchoPara->ParamR.Tp, FFT_Dot, j*PRT, Range, Lambda, dev_SourceSignal, dev_DeceptionJamSig + j*FFT_Dot);
				break;
			}
			case 3:			//频移干扰
			{
				FreqShiftJam((JAM_DECEPTION_PARAM*)&pJAM_DECEPTION_PARAM[i], pStJamEchoPara->ParamR.Fs, pStJamEchoPara->ParamR.Tp, FFT_Dot, j*PRT, Range, Lambda, dev_SourceSignal, dev_DeceptionJamSig + j*FFT_Dot);
				break;
			}
			case 4:			//密集复制+重复转发干扰
			{
				CopyTransmitJam((JAM_DECEPTION_PARAM*)&pJAM_DECEPTION_PARAM[i], pStJamEchoPara->ParamR.Fs, FFT_Dot, j*PRT, PRTLen, Range, Lambda, dev_SourceSignal, dev_DeceptionJamSig + j*FFT_Dot);
				break;
			}
			case 5:			//密集复制+重复转发干扰
			{
				CutJam((JAM_DECEPTION_PARAM*)&pJAM_DECEPTION_PARAM[i], pStJamEchoPara->ParamR.Fs, pStJamEchoPara->ParamR.Tp, FFT_Dot, PRTLen, j*PRT, Range, Lambda, dev_SourceSignal, dev_DeceptionJamSig + j*FFT_Dot);
				break;
			}
			default:break;
			}

			//计算DYT在干扰机波束位置的增益 第一步
			float Range = 0; float AntGain[3] = { 0 };
			double RadarPos[3];
			RadarPos[0] = pStJamEchoPara->RadarPos.NorthPos;
			RadarPos[1] = pStJamEchoPara->RadarPos.SkyPos;
			RadarPos[2] = pStJamEchoPara->RadarPos.EastPos;
			double JamDev_Pos[3];
			JamDev_Pos[0] = pJAM_DECEPTION_PARAM[i].JamX;
			JamDev_Pos[1] = pJAM_DECEPTION_PARAM[i].JamY;
			JamDev_Pos[2] = pJAM_DECEPTION_PARAM[i].JamZ;

			m_AntParamJam->AziAngle = pJAM_DECEPTION_PARAM[i].JamAzi;
			m_AntParamJam->PitAngle = pJAM_DECEPTION_PARAM[i].JamPit;
			CalAntWeight(0, m_AntParamJam, (RADAR*)&pStJamEchoPara->ParamR, RadarPos, JamDev_Pos, pSumAntennaFunction, pAziSubAntennaFunction, pPitSubAntennaFunction, AntGain, &Range);
			float RadomeLoss = 1;//干扰机天线罩损耗
			float TranGain = sqrt((pJAM_DECEPTION_PARAM[i].JamPower*RadomeLoss*Lambda*Lambda / (64 * PI*PI*PI) / (Range*Range*Range*Range)) * AntGain[0]);

			//计算干扰机在DYT波束指向下的和差差增益
			m_AntParam->AziAngle = pStJamEchoPara->AntParam.AziAngle;
			m_AntParam->PitAngle = pStJamEchoPara->AntParam.PitAngle;
			CalAntWeight(1, m_AntParam, (RADAR*)&pStJamEchoPara->ParamR, JamDev_Pos, RadarPos, pSumAntennaFunction, pAziSubAntennaFunction, pPitSubAntennaFunction, AntGain, &Range);
			float TranGainS = TranGain*sqrt(AntGain[0]);
			float TranGainA = TranGain*sqrt(AntGain[1]);
			float TranGainP = TranGain*sqrt(AntGain[2]);
			dim3 ThreadsPerBlock(512, 1);
			dim3 BlockNum(((FFT_Dot + 1) + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
			CUDA_ComplexMultCoefAdd(0, ThreadsPerBlock, BlockNum, FFT_Dot, dev_DeceptionJamSig, dev_DeceptionJamSigSum + j*FFT_Dot, TranGainS);
			CUDA_ComplexMultCoefAdd(0, ThreadsPerBlock, BlockNum, FFT_Dot, dev_DeceptionJamSig, dev_DeceptionJamSigAzi + j*FFT_Dot, TranGainA);
			CUDA_ComplexMultCoefAdd(0, ThreadsPerBlock, BlockNum, FFT_Dot, dev_DeceptionJamSig, dev_DeceptionJamSigPit + j*FFT_Dot, TranGainP);
		}

	}
	Complexf *SumEchoDataPolar1 = pStEchoData->SumEchoDataPolar1;
	Complexf *AziEchoDataPolar1 = pStEchoData->AziEchoDataPolar1;
	Complexf *EleEchoDataPolar1 = pStEchoData->EleEchoDataPolar1;

	int Nr = pStJamEchoPara->SimData.SaveFlie_Nr;
	cudaMemcpy(SumEchoDataPolar1, dev_DeceptionJamSigSum, sizeof(Complexf)*Nr*pStJamEchoPara->SimData.SaveFlie_Na, cudaMemcpyDeviceToHost);
	cudaMemcpy(AziEchoDataPolar1, dev_DeceptionJamSigAzi, sizeof(Complexf)*Nr*pStJamEchoPara->SimData.SaveFlie_Na, cudaMemcpyDeviceToHost);
	cudaMemcpy(EleEchoDataPolar1, dev_DeceptionJamSigPit, sizeof(Complexf)*Nr*pStJamEchoPara->SimData.SaveFlie_Na, cudaMemcpyDeviceToHost);

	if (m_DevID == 0)
	{
		//for (int k = 0; k < pStJamEchoPara->SimData.SaveFlie_Na; k++)
		//{
		//	char *EchoTemp1 = (char*)_Malloc(sizeof(Complexf)*FFT_Dot);
		//	cudaMemcpy(EchoTemp1, dev_SourceSignal, sizeof(Complexf)*FFT_Dot, cudaMemcpyDeviceToHost);
		//	QFile *pQFile = new QFile;
		//	pQFile->setFileName("..\\..\\..\\data\\EchoSignal");
		//	pQFile->open(QFile::WriteOnly | QFile::Append);
		//	pQFile->write((char*)EchoTemp1, sizeof(Complexf)*pStJamEchoPara->SimData.SaveFlie_Nr);
		//	//pQFile2->write((char*)AziEchoDataPolar1, sizeof(Complexf)*Nr);
		//	//pQFile2->write((char*)EleEchoDataPolar1, sizeof(Complexf)*Nr);
		//	pQFile->close();
		//	delete pQFile;
		//}


		char *EchoTemp = (char*)_Malloc(sizeof(Complexf)*FFT_Dot);
		//cudaMemcpy(EchoTemp, dev_BlanketJamSig, sizeof(Complexf)*FFT_Dot, cudaMemcpyDeviceToHost);
		QFile *pQFile2 = new QFile;
		pQFile2->setFileName("..\\..\\..\\data\\EchoJAM");
		pQFile2->open(QFile::WriteOnly);
		pQFile2->write((char*)SumEchoDataPolar1, sizeof(Complexf)*pStJamEchoPara->SimData.SaveFlie_Nr * pStJamEchoPara->SimData.SaveFlie_Na);
		//pQFile2->write((char*)AziEchoDataPolar1, sizeof(Complexf)*Nr);
		//pQFile2->write((char*)EleEchoDataPolar1, sizeof(Complexf)*Nr);
		pQFile2->close();
		delete pQFile2;
	}

}

//天线
void ThreadClassDeceptionJamGen::slotSettingAntPara(void *p)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	ANTPARAM        *AntParam = (ANTPARAM*)p;
	//***********天线加权gpu***********//
	cudaMemcpy(pSumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToHost);
	cudaMemcpy(pAziSubAntennaFunction, AntParam->AziSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToHost);
	cudaMemcpy(pPitSubAntennaFunction, AntParam->PitSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToHost);

	memcpy(m_AntParam, AntParam, sizeof(ANTPARAM));
	memcpy(m_AntParamJam, AntParam, sizeof(ANTPARAM));
}


