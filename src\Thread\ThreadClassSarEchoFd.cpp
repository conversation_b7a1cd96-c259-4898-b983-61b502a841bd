/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassSarEcho.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassSarEchoFd.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassSarEcho.h"
#include<QThread>
#include <QDateTime>

#include <cuda_runtime_api.h>
#include<cuda_runtime.h>
#include<cuda_device_runtime_api.h>
#include<cufft.h>
#include<curand.h>

#include <QFile>
#include <QDebug>

#include "KernelSar.h"
#include "KernelPublic.h"

#include "ThreadClassSendWaveGen.h"

ThreadClassSarEcho::ThreadClassSarEcho(int DevID,int buffer_size,char *d_Buffer,QObject *parent) : QObject(parent)
{
	SetMemPoolSrcFileName((char*)"ThreadClassSarEcho", sizeof("ThreadClassSarEcho"));
	_DefineMemPool(stEchoData, 20);
    _DefineMemPool(stWaveShow,10);
	_DefineMemPool(stTarLink, 50);
	//_DefineMemPool(stTarPitLink, 2000);
	//_DefineMemPool(stTarAziLink, 10000);
    //分配CPU伪内存池空间
    InitMyMalloc(1000*1024*1024);
	m_DevID				= -1;
    m_bWaveDataShowFlag = false;

	m_StTarLink = (stTarLink*)_MallocMemObj(stTarLink); m_StTarLink->init();

    pClassCoorDinate = new ClassCoorDinate;

	//三角函数查表
	double CosValueSample = 1.0 / 4096;
	CosValueLen = 4100;
	m_CosValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_CosValue, 0, sizeof(float)*CosValueLen);
	m_SinValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_SinValue, 0, sizeof(float)*CosValueLen);
	for (int idd = 0; idd < CosValueLen; idd++)
	{
		if (CosValueSample*idd <= 1)
		{
			m_CosValue[idd] = cos(idd*CosValueSample * 2 * PI);
			m_SinValue[idd] = sin(idd*CosValueSample * 2 * PI);
		}
	}
	m_Fs = m_Br = m_Tp = 0;
	

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassSarEcho::~ThreadClassSarEcho()
{

}
//初始化GPU设备工作参数
bool ThreadClassSarEcho::InitDeviceWorkPara(int DevID,long long buffer_size,char *d_Buffer)
{
	m_BytesOffsetTarRCS		= 0;
	BufferInit_Cur			= 0;
	BufferInit_SizeTotal	= (long long)256 * 1024 * 1024;
	BufferInitTarRcs_Size	= (long long)1024 * 1024 * 1024;

	m_BytesOffsetTarRCS		= 0;
    if(d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
        m_DevID				= DevID;
		d_BufferInit		= d_Buffer;
		m_d_BufferTarRCS	= d_Buffer + BufferInit_SizeTotal;
		d_BufferFather		= d_Buffer + BufferInit_SizeTotal + BufferInitTarRcs_Size;
        //分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, buffer_size - BufferInit_SizeTotal);
    }
    else{//作为独立的线程使用，单独使用一个GPU卡
        m_DevID         = -1;
        d_BufferFather  = nullptr;
        //分配GPU伪内存池空间
		cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
		cudaMalloc((void**)&m_d_BufferTarRCS, BufferInitTarRcs_Size);
        cudaMalloc((void**)&d_Buffer,buffer_size);
		d_BufferFather = d_Buffer;
		InitMyMallocCUDA(d_BufferFather, buffer_size);
    }
	//发射信号缓存区
	dev_Signal_Send = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += 128*1024*sizeof(float);
	//三角函数查表
	long long Bytes = (CosValueLen*sizeof(float) + 16) / 16 * 16;
	dev_CosValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_CosValue, m_CosValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
	dev_SinValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_SinValue, m_SinValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
	
	Bytes = 2048.f * 2048.f * sizeof(float);
	dev_Target_ALL1 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	dev_Target_ALL2 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	dev_Target_ALL3 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	dev_Target_ALL4 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	dev_Target_ALL5 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;

    dev_DMCTarget_ALL1 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_DMCTarget_ALL2 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_DMCTarget_ALL3 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_DMCTarget_ALL4 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_DMCTarget_ALL5 = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;

    
    dev_AziSubAntennaFunction = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	dev_SumAntennaFunction = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_PitSubAntennaFunction = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    int AntBufferLength = 2048;
    dev_Matrix          = (double*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += AntBufferLength;

	m_AntParam = (ANTPARAM*)malloc(sizeof(ANTPARAM));

	cudaEventCreate(&e_start);
	cudaEventCreate(&e_stop);
	cudaEventCreate(&e_start2);
	cudaEventCreate(&e_stop2);

    return true;
}
//初始化cuda参数
bool ThreadClassSarEcho::InitCUDAPara()
{
	int    BATCH = 1;
	for (int i = 0; i < CUDAStreamMaxNum; i++)
	{
		cudaStreamCreate(&cudaStream[i]);
		cufftPlan1d(&plan[i], 16384, CUFFT_C2C, BATCH);
		cufftSetStream(plan[i], cudaStream[i]);
	}
    //随机数句柄产生
    curandCreateGenerator(&gen_curand,CURAND_RNG_PSEUDO_MRG32K3A);
    
    return true;
}
//加载目标RCS系数
void ThreadClassSarEcho::InitTarRcsPara(void *p)
{
	stTarPitLink *pStTarPitLinkRecv = (stTarPitLink*)p;

	stTarPitLink *pStTarPitLinkHead = (stTarPitLink*)_MallocMemObj(stTarPitLink); pStTarPitLinkHead->init();

	stTarPitLink *pStTarPitLinkRecvNode = pStTarPitLinkRecv;
	while (pStTarPitLinkRecvNode->next)
	{
		pStTarPitLinkRecvNode = pStTarPitLinkRecvNode->next;
		stTarPitLink *pStTarPitLinkNode		= (stTarPitLink*)_MallocMemObj(stTarPitLink); pStTarPitLinkNode->init();
		stTarAziLink *pStTarAziLinkHead		= (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkHead->init();
		pStTarPitLinkNode->pStTarAziLink	= pStTarAziLinkHead;

		stTarAziLink *pStTarAziLinkRecv		= pStTarPitLinkRecvNode->pStTarAziLink;
		while (pStTarAziLinkRecv->next)
		{
			pStTarAziLinkRecv = pStTarAziLinkRecv->next;
			stTarAziLink *pStTarAziLinkNode = (stTarAziLink*)_MallocMemObj(stTarAziLink); pStTarAziLinkNode->init();

			int Bytes = (pStTarAziLinkRecv->pointNum*sizeof(float)) / 16 * 16;

			pStTarAziLinkNode->pMissileData = (MissileData*)_MallocMemObj(MissileData);
			memcpy(pStTarAziLinkNode->pMissileData, pStTarAziLinkRecv->pMissileData, sizeof(MissileData));

			pStTarAziLinkNode->pMissileDataGPU.x = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.x, pStTarAziLinkRecv->pMissileData->x, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.y = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.y, pStTarAziLinkRecv->pMissileData->y, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.z = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.z, pStTarAziLinkRecv->pMissileData->z, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.rcs = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.rcs, pStTarAziLinkRecv->pMissileData->rcs, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);
			pStTarAziLinkNode->pMissileDataGPU.phi = (float*)(m_d_BufferTarRCS + m_BytesOffsetTarRCS); m_BytesOffsetTarRCS += Bytes;
			cudaMemcpy(pStTarAziLinkNode->pMissileDataGPU.phi, pStTarAziLinkRecv->pMissileData->phi, sizeof(float)*pStTarAziLinkRecv->pointNum, cudaMemcpyHostToDevice);

			pStTarAziLinkNode->pointNum = pStTarAziLinkRecv->pointNum;
			pStTarAziLinkNode->pit = pStTarAziLinkRecv->pit;
			pStTarAziLinkNode->azi = pStTarAziLinkRecv->azi;
			AddLink(pStTarAziLinkHead, pStTarAziLinkNode);
		}
		AddLink(pStTarPitLinkHead, pStTarPitLinkNode);
	}
	stTarLink *pStTarLink = (stTarLink*)_MallocMemObj(stTarLink); pStTarLink->init();
	pStTarLink->pStTarPitLink = pStTarPitLinkHead;
	AddLink(m_StTarLink, pStTarLink);
}
//产生发射信号
void ThreadClassSarEcho::RadarSendWaveGen(RADAR *ParamR,Complex *BaseSignal)
{

}
template <class T>
void _CufftExecC2C(cufftHandle plan,T *idata,T *odata,int direction)
{
	if (sizeof(T) > 8){
		cufftExecZ2Z(plan, (cuDoubleComplex*)idata, (cuDoubleComplex*)odata, direction);
	}
	else{
		cufftExecC2C(plan, (cufftComplex*)idata, (cufftComplex*)odata, direction);
	}
}
template<class T>
void HSYS_CALCUTE(int SenceNum, double Rbin_min, double *pTarget_ALL, double *RadarPos, T *hsys)
{
	double Lambda = C_ / 300.0e6;
	for (int i = 0; i < SenceNum; i++)
	{
		float Ptarget[3];
		double Radar2Rcs[3];

		double R_XY;
		float need_phase;
		float sigma_re, sigma_im, dem_i;

		double *dev_Target_All = pTarget_ALL + i*TARGET_LEN;

		Ptarget[0] = dev_Target_All[0];
		Ptarget[1] = dev_Target_All[1];
		Ptarget[2] = dev_Target_All[2];
		dev_Target_All[4] = 0.7485;
		sigma_re = (float)(dev_Target_All[3]) *cos(2 * PI*(float)(dev_Target_All[4]));
		sigma_im = (float)(dev_Target_All[3]) *sin(2 * PI*(float)(dev_Target_All[4]));

		Radar2Rcs[0] = Ptarget[0] - RadarPos[0];	//北
		Radar2Rcs[1] = Ptarget[1] - RadarPos[1];	//天
		Radar2Rcs[2] = Ptarget[2] - RadarPos[2];	//东

		R_XY = sqrt(Radar2Rcs[0] * Radar2Rcs[0] + Radar2Rcs[1] * Radar2Rcs[1] + Radar2Rcs[2] * Radar2Rcs[2]);       //弹目距

		int id = (int)((R_XY - Rbin_min)*(2.0*300.0e6 / C_) + 0.5);
        need_phase = -4.0*PI*R_XY / Lambda;						//相位
		qDebug() << "id" << id;

		double need_re = sigma_re*cos(need_phase) - sigma_im*sin(need_phase);
		double need_im = sigma_re*sin(need_phase) + sigma_im*cos(need_phase);

		hsys[id].x += need_re;
		hsys[id].y += need_im;
	}
}
void cal_chirp_ref_tiaodai(Complexf *s0, double Kr, double Tr, int Nr, double fs)
{
	int i;
	double pha;
	double t;
	for (i = 0; i<Nr; i++)
	{
		//----------------------------------------//
		t = (double)(i) / (fs);
		pha = PI*(Kr)*(t - Tr / 2.0)*(t - Tr / 2.0);
		if (fabs(t) <= Tr)
		{
			s0[i].x = cos(pha);
			s0[i].y = sin(pha);
		}
		else
		{
			s0[i].x = 0.0;
			s0[i].y = 0.0;
		}
	}
}
//************面目标回波************//
template <class T, class T2, class T3, class T4>
void ThreadClassSarEcho::GenSenceEcho(RADAR *ParamR,ANTPARAM *AntParam,SimStruct *SimData,T *RadarAPC,T2 *TargetAll, T3 *BaseSignal,
                                      T4 *SumCluDataPolar1, T4 *AziCluDataPolar1, T4 *EleCluDataPolar1, T4 *ExCluDataPolar1)
{
	//qDebug()<<"-------------------------------------------------------------------------------------------------------------------Thread ID:"<<this->thread()->currentThreadId();
	//**************************
	//cudaEventRecord(e_start, 0);

	cudaError_t Err = cudaGetLastError();
	Err = cudaGetLastError();
	if (Err != cudaSuccess){
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err;
	}

	unsigned int TarNum		= SimData->SenceTarNum;
	unsigned	TarNumFFT	= 1536 * 1536;
	unsigned	TrNum		= (int)(ParamR->Fs*ParamR->Tp + 0.5f);		//一个脉宽的点数
    
	int Nr			= ParamR->Nr;	//原ParamR->Nr
    int SaveNr		= SimData->SaveFlie_Nr;
	int Na			= ParamR->Na;	//原ParamR->Na
    
    //-------------------------------------------------------------------------//
	double *param = (double*)_Malloc(sizeof(double)*20);//GPU计算时需要的双精度参数，内存
	param[0] = SimData->Rmin;
	param[1] = ParamR->Fs;
	param[2] = 2.0*PI*ParamR->Fc / C_;
	param[3] = 1.0/(AntParam->AntAziBound/AntParam->AntFuncAziNum);
	param[4] = 1.0 / (AntParam->AntEleBound / AntParam->AntFuncEleNum);
	param[5] = AntParam->AziSpace;		//方位方向间距
	param[6] = AntParam->EleSpace;		//俯仰方向间距
	param[7] = ParamR->Amp;             //发射信号功率
	param[8] = AntParam->AziSlope;		//方位向差斜率
	param[9] = AntParam->EleSlope;		//俯仰方向差斜率(比幅)
	param[10] = AntParam->AziOverlap;	//方位方向重叠波束（比幅）
	param[11] = AntParam->EleOverlap;	//俯仰方向重叠波束（比幅）
	param[12] = ParamR->RadomeLoss;		//天线罩损耗


	double *param2 = (double*)_Malloc(sizeof(double) * 8);
	param2[0] = SimData->Rmin;
	param2[1] = 2.0*ParamR->Fs / C_;
	param2[2] = -2.0 / (C_ / ParamR->Fc);//-4.0*PI / (C_ / ParamR->Fc)
	param2[3] = ParamR->Amp*ParamR->RadomeLoss*(C_ / ParamR->Fc)*(C_ / ParamR->Fc) / (64.0 * PI*PI*PI);
	param2[4] = 2 * PI;
	param2[5] = 1.0/(2.0 * PI);
	
	int *else_param;//GPU计算时需要的整形参数，内存
	else_param = (int*)_Malloc(sizeof(int)*20);
	else_param[0] = TarNum;
	else_param[1] = Nr;
	else_param[2] = AntParam->AntFuncAziNum;
	else_param[3] = AntParam->AntFuncEleNum;
	else_param[4] = SimData->AziEleFalg;
	else_param[5] = SimData->TransLossFlag;
    //-------------------------------------------------------------------------------//
	double	*Ps_i		= (double*)_Malloc(16 * sizeof(double)*Na);
	
    if(m_bWaveDataShowFlag == true)//抽取波形去界面显示
    {
        m_bWaveDataShowFlag = false;
        stWaveShow *pStWaveShow = _MallocMemObj(stWaveShow);pStWaveShow->init();
        memcpy(pStWaveShow->Buffer,BaseSignal,TrNum*sizeof(T4));
        pStWaveShow->bDecodeType = DecodeType_SendWave;
        pStWaveShow->uSigDot = TrNum;
        pStWaveShow->uDataType = sizeof(T4);
        pStWaveShow->ullSample = (unsigned long long)ParamR->Fs;
        emit sendWaveShow(pStWaveShow);
    }

	double	*dev_Ps			= (double*)_MallocCUDA(sizeof(double) * 32 * Na);//雷达的位置信息，显存
	double	*dev_param		= (double*)_MallocCUDA(sizeof(double) * 32 * Na);//GPU计算时需要的双精度参数，显存
	double	*dev_param2		= (double*)_MallocCUDA(sizeof(double) * 32 * Na);//GPU计算时需要的双精度参数，显存
	int		*dev_else_param = (int*)_MallocCUDA(sizeof(int)*32 * Na);//GPU计算时需要的整形参数，显存

	T4		*dev_hsys		= (T4*)_MallocCUDA(sizeof(T4)*Nr* Na);//产生系统响应函数
	T4		*dev_Echo		= (T4*)_MallocCUDA(sizeof(T4)*Nr*Na);


	Err = cudaGetLastError();
	if (Err != cudaSuccess){
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err;
	}

	float	*dev_ant_factor = (float*)_MallocCUDA(TarNumFFT*sizeof(float)*Na);
	float	*dev_Range		= (float*)_MallocCUDA(TarNumFFT*sizeof(float)*Na);
	float	*dev_Range_2	= (float*)_MallocCUDA(TarNumFFT*sizeof(float)*Na);

	T4		*dev_Signal = (T4*)dev_Signal_Send;

	
	Err = cudaGetLastError();
	if (Err != cudaSuccess){
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err;
	}

	if (abs(m_Fs - ParamR->Fs) > 1 || abs(m_Br - ParamR->Br) > 1 || abs(m_Tp*1e6 - ParamR->Tp*1e6) > 0.1f)
	{
		m_Fs = ParamR->Fs; m_Br = ParamR->Br; m_Tp = ParamR->Tp;
		cudaMemset(dev_Signal, 0, Nr*sizeof(T4));
		cudaMemcpy(dev_Signal, BaseSignal, TrNum*sizeof(T4), cudaMemcpyHostToDevice);
		_CufftExecC2C(plan[0], dev_Signal, dev_Signal, CUFFT_FORWARD);
	}

	Err = cudaGetLastError();
	qint64 t1 = QDateTime::currentMSecsSinceEpoch();
	//qDebug() << "GPU:" << m_DevID << "-------------------------------------------------------------------------------------------start  " << t1;
    for (int frame = 0; frame < 1; frame++)
	{
		int cntPs = 0;
		for(int idx = 0;idx<Na;idx++)
		{
			int cudaStreamNO = idx;
			Ps_i[cntPs++] = RadarAPC[3 * idx + 0];
			Ps_i[cntPs++] = RadarAPC[3 * idx + 1];
			Ps_i[cntPs++] = RadarAPC[3 * idx + 2];
			cntPs++;
			cudaMemcpyAsync(dev_Ps + 32 * idx, Ps_i + 4 * idx, sizeof(double) * 3, cudaMemcpyHostToDevice, cudaStream[cudaStreamNO]);
			cudaMemcpyAsync(dev_param + 32 * idx, param, 16 * sizeof(double), cudaMemcpyHostToDevice, cudaStream[cudaStreamNO]);
			cudaMemcpyAsync(dev_param2 + 32 * idx, param2, 8 * sizeof(double), cudaMemcpyHostToDevice, cudaStream[cudaStreamNO]);
			cudaMemcpyAsync(dev_else_param + 32 * idx, else_param, 8 * sizeof(int), cudaMemcpyHostToDevice, cudaStream[cudaStreamNO]);

			Err = cudaGetLastError();
			if (Err != cudaSuccess){
				qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
			}
			cudaMemsetAsync(dev_hsys + Nr*idx, 0, sizeof(T4)*Nr, cudaStream[cudaStreamNO]);
			Err = cudaGetLastError();
			if (Err != cudaSuccess){
				qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
			}

			dim3 ThreadsPerBlock(512,1);
			dim3 BlockNum((TarNum + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
			CUDA_cal_ant_all_kernel_jushu2(cudaStream[cudaStreamNO], ThreadsPerBlock, BlockNum, dev_Ps + 32 * idx, dev_Target_ALL1, dev_Target_ALL2, dev_Target_ALL3, dev_SumAntennaFunction,
											dev_Matrix, dev_param + 32 * idx, dev_else_param + 32 * idx, dev_ant_factor + TarNumFFT*idx, nullptr, nullptr,
											dev_Range + TarNumFFT*idx, dev_Range_2 + TarNumFFT*idx);  //计算每个目标的天线加权(固定距离门)
			Err = cudaGetLastError();
			if (Err != cudaSuccess){
				qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
			}
			//目前耗时8ms以下
			CUDA_cal_hsys_all_kernel_jushu(cudaStream[cudaStreamNO], ThreadsPerBlock, BlockNum, dev_Ps + 32 * idx, dev_Target_ALL4,
											dev_Target_ALL5, dev_param2 + 32 * idx, dev_else_param + 32 * idx, dev_CosValue, dev_SinValue,
											dev_ant_factor + TarNumFFT*idx, nullptr, nullptr, dev_Range + TarNumFFT*idx,
											dev_Range_2 + TarNumFFT*idx, dev_hsys + Nr*idx, nullptr, nullptr);

			Err = cudaGetLastError();
			if (Err != cudaSuccess){
				qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
			}

			_CufftExecC2C(plan[cudaStreamNO], dev_hsys + Nr*idx, dev_hsys + Nr*idx, CUFFT_FORWARD);
			BlockNum.x = ((Nr + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x);
			BlockNum.y = 1;
			CUDA_ComplexMutiple(cudaStream[cudaStreamNO], ThreadsPerBlock, BlockNum, (T4*)dev_Signal, (T4*)dev_hsys + Nr*idx, (T4*)dev_Echo + Nr*idx, Nr);
			_CufftExecC2C(plan[cudaStreamNO], dev_Echo + Nr*idx, dev_Echo + Nr*idx, CUFFT_INVERSE);
			Err = cudaGetLastError();
			if (Err != cudaSuccess){
				qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
			}
			cudaMemcpyAsync(&SumCluDataPolar1[idx*SaveNr], dev_Echo + Nr*idx, sizeof(T4)*SaveNr, cudaMemcpyDeviceToHost, cudaStream[cudaStreamNO]);
			Err = cudaGetLastError();
			if (Err != cudaSuccess){
				qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
			}
		}
	}
	//qint64 t2 = QDateTime::currentMSecsSinceEpoch();
	////qDebug() << "GPU:" << m_DevID << "---------------------------------------------------------------------------------------------stop  " << t2;
	//cudaEventRecord(e_stop, 0);
	//cudaEventSynchronize(e_stop);
	//float elapsedTime = 0,elapsedTime2 = 0;
	//cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
	//cudaEventElapsedTime(&elapsedTime2, e_start2, e_stop);
	//qDebug() << "GPU:" << m_DevID << "============  CUDA Cost Time is  " << (elapsedTime2) << " ms";
	//qDebug() << "GPU:" << m_DevID << "============  ThreadClassSarEcho Cost Time is  " << (elapsedTime) << " ms";

	if (m_DevID == 0)
	{
		//cudaEventRecord(e_stop, 0);
		//cudaEventSynchronize(e_stop);
		//char *EchoTemp = (char*)_Malloc(TarNumFFT*sizeof(float));
		//cudaMemcpy(EchoTemp, dev_ant_factor + TarNumFFT*idx, TarNumFFT*sizeof(float), cudaMemcpyDeviceToHost);
		//QFile *pQFile2 = new QFile;
		//pQFile2->setFileName("D:\\yening\\PRJ\\PR2401_063\\5_from_yening\\EchoInterfeCalcutePool\\doc\\echo4");
		//pQFile2->open(QFile::WriteOnly);
		//pQFile2->write((char*)&SumCluDataPolar1[0 * SaveNr], sizeof(T4)*SaveNr*Na);
		//pQFile2->close();
		//delete pQFile2;
	}
     
}
//单脉冲模式生成回波
template <class T, class T2, class T3, class T4, class T5>
void ThreadClassSarEcho::GenDMCEcho(Target *pTarget, PLATFORM *pPlatForm,RADAR *ParamR, ANTPARAM *AntParam, SimStruct *SimData, T *RadarAPC, T2 *RadarVel, T3 *TargetAll, T4 *BaseSignal,
									T5 *SumCluDataPolar1, T5 *AziCluDataPolar1, T5 *EleCluDataPolar1, T5 *ExCluDataPolar1)
{
	cudaError_t Err;
	//**************************
	cudaEvent_t e_start, e_stop;
	cudaEventCreate(&e_start);
	cudaEventCreate(&e_stop);
	cudaEventRecord(e_start, 0);

	int TarNum	= 8192;// 此值用来预分配内存和显存
	int TrNum	= (int)(ParamR->Fs*ParamR->Tp + 0.5f);		//一个脉宽的点数

	int Nr = SimData->SaveFlie_Nr * 2;//ParamR->Nr;				//原ParamR->Nr
	int SaveNr	= SimData->SaveFlie_Nr;
	int Na		= ParamR->Na;	//原ParamR->Na

	//to do 
	ParamR->RadomeLoss = 1;

	//-------------------------------------------------------------------------//
	double *param = (double*)_Malloc(sizeof(double) * 20);//GPU计算时需要的双精度参数，内存
	param[0] = SimData->Rmin;
	param[1] = 1.0/ParamR->Fs;
    param[2] = ParamR->Fc/C_;//1/lambda
	param[3] = 1.0 / (AntParam->AntAziBound / AntParam->AntFuncAziNum);
	param[4] = 1.0 / (AntParam->AntEleBound / AntParam->AntFuncEleNum);
    param[5] = 2.0*PI*ParamR->Fc / SPEEDLIGHT*AntParam->AziSpace;		//方位方向间距
    param[6] = 2.0*PI*ParamR->Fc / SPEEDLIGHT*AntParam->EleSpace;		//俯仰方向间距
	param[7] = 1.0 / (2.0*PI);// ParamR->Amp;             //发射信号功率
	param[8] = AntParam->AziSlope;		//方位向差斜率
	param[9] = AntParam->EleSlope;		//俯仰方向差斜率(比幅)
	param[10] = AntParam->AziOverlap;	//方位方向重叠波束（比幅）
	param[11] = AntParam->EleOverlap;	//俯仰方向重叠波束（比幅）
	param[12] = ParamR->RadomeLoss;		//天线罩损耗

	double *dev_param = (double*)_MallocCUDA(sizeof(double) * 20);//GPU计算时需要的双精度参数，显存
	cudaMemcpy(dev_param, param, 20 * sizeof(double), cudaMemcpyHostToDevice);

	double *param2 = (double*)_Malloc(sizeof(double) * 8);
	param2[0] = SimData->Rmin;
	param2[1] = 2.0*ParamR->Fs / C_;
	param2[2] = -2.0 / (C_ / ParamR->Fc);//-4.0*PI / (C_ / ParamR->Fc)
	param2[3] = ParamR->Amp*ParamR->RadomeLoss*(C_ / ParamR->Fc)*(C_ / ParamR->Fc) / (64.0 * PI*PI*PI);
	param2[4] = 2 * PI;
	param2[5] = 1.0 / (2.0 * PI);
	double *dev_param2 = (double*)_MallocCUDA(sizeof(double) * 8);//GPU计算时需要的双精度参数，显存
	cudaMemcpy(dev_param2, param2, 8 * sizeof(double), cudaMemcpyHostToDevice);

	int *else_param;//GPU计算时需要的整形参数，内存
	else_param = (int*)_Malloc(sizeof(int) * 20);
	else_param[0] = TarNum;
	else_param[1] = TrNum;//发射信号的实际点数
	else_param[2] = AntParam->AntFuncAziNum;
	else_param[3] = AntParam->AntFuncEleNum;
	else_param[4] = SimData->AziEleFalg;
	else_param[5] = SimData->TransLossFlag;

	int *dev_else_param;//GPU计算时需要的整形参数，显存
	dev_else_param = (int*)_MallocCUDA(15 * sizeof(int));
	//
	Err = cudaGetLastError();
	//-------------------------------------------------------------------------------//

	double *Ps_i = (double*)_Malloc(3 * sizeof(double));
	memset(Ps_i, 0, sizeof(double) * 3);
	double *RadarVel_All = (double*)_Malloc(3 * sizeof(double));
	memset(RadarVel_All, 0, sizeof(double) * 3);
	double *dev_Ps = (double*)_MallocCUDA(sizeof(double) * 3);//雷达的位置信息，显存
	cudaMemset(dev_Ps, 0, sizeof(double) * 3);
	double *dev_RadarVel_ALL = (double*)_MallocCUDA(sizeof(double) * 3);
	cudaMemset(dev_RadarVel_ALL, 0, sizeof(double) * 3);
	//-------------------------------------------------------------------------------//
	T4 *hsys;//产生系统响应函数，内存
	hsys = (T4*)_Malloc(Nr*sizeof(T4));
	memset(hsys, 0, sizeof(T4)*Nr);
	Err = cudaGetLastError();
	T4 *dev_hsys, *dev_Azihsys, *dev_Elehsys;//产生系统响应函数，显存dev_Azihsys,dev_Elehsys
    dev_hsys    = (T4*)_MallocCUDA(Nr*sizeof(T4));
	dev_Azihsys = (T4*)_MallocCUDA(Nr*sizeof(T4));
	dev_Elehsys = (T4*)_MallocCUDA(Nr*sizeof(T4));
	cudaMemset(dev_hsys, 0, sizeof(T4)*Nr);
	cudaMemset(dev_Azihsys, 0, sizeof(T4)*Nr);
	cudaMemset(dev_Elehsys, 0, sizeof(T4)*Nr);

	T4 *dev_Sumecho, *dev_Aziecho, *dev_Eleecho;//产生系统响应函数，显存dev_Azihsys,dev_Elehsys
	dev_Sumecho = (T4*)_MallocCUDA(Nr*sizeof(T4));
	dev_Aziecho = (T4*)_MallocCUDA(Nr*sizeof(T4));
	dev_Eleecho = (T4*)_MallocCUDA(Nr*sizeof(T4));

	
	Err = cudaGetLastError();
	float *dev_ant_factor, *dev_AziAnt_factor, *dev_EleAnt_factor;
	dev_ant_factor		= (float*)_MallocCUDA(TarNum*sizeof(float));
	dev_AziAnt_factor	= (float*)_MallocCUDA(TarNum*sizeof(float));
	dev_EleAnt_factor	= (float*)_MallocCUDA(TarNum*sizeof(float));
	cudaMemset(dev_ant_factor, 0, TarNum*sizeof(float));  //之前double
	cudaMemset(dev_AziAnt_factor, 0, TarNum*sizeof(float));
	cudaMemset(dev_EleAnt_factor, 0, TarNum*sizeof(float));

	float *dev_Range	= (float*)_MallocCUDA(TarNum*sizeof(float));
	float *dev_Range_2	= (float*)_MallocCUDA(TarNum*sizeof(float));
	float *dev_Fd		= (float*)_MallocCUDA(TarNum*sizeof(float));
	float *dev_need_re	= (float*)_MallocCUDA(TarNum*sizeof(float));
	float *dev_need_im	= (float*)_MallocCUDA(TarNum*sizeof(float));
    float *dev_need_re_Azi	= (float*)_MallocCUDA(TarNum*sizeof(float));
    float *dev_need_im_Azi	= (float*)_MallocCUDA(TarNum*sizeof(float));
    float *dev_need_re_Ele	= (float*)_MallocCUDA(TarNum*sizeof(float));
    float *dev_need_im_Ele	= (float*)_MallocCUDA(TarNum*sizeof(float));
	int *dev_id			= (int*)_MallocCUDA(TarNum*sizeof(int));
	Err = cudaGetLastError();

	T4 *EchoLine = (T4*)_Malloc(sizeof(T4)*Nr);	//一个距离向的回波
	T4 *EchoLineTemp = (T4*)_Malloc(sizeof(T4)*Nr);
	T4 *dev_Echo = (T4*)_MallocCUDA(sizeof(T4)*Nr);

	cudaEvent_t e_start2, e_stop2;
	cudaEventCreate(&e_start2);
	cudaEventCreate(&e_stop2);
	cudaEventRecord(e_start2, 0);

	T4 *dev_Signal = (T4*)_MallocCUDA(Nr * sizeof(T4));
	if (abs(m_Fs - ParamR->Fs) > 1 || abs(m_Br - ParamR->Br) > 1 || abs(m_Tp*1e6 - ParamR->Tp*1e6) > 0.1f)
	{
		m_Fs = ParamR->Fs; m_Br = ParamR->Br; m_Tp = ParamR->Tp;
		cudaMemset(dev_Signal, 0, Nr*sizeof(T4));
		cudaMemcpy(dev_Signal, BaseSignal, TrNum*sizeof(T4), cudaMemcpyHostToDevice);
		//_CufftExecC2C(plan[0], dev_Signal, dev_Signal, CUFFT_FORWARD);
	}

	cudaMemset(dev_Sumecho, 0, sizeof(T4)*Nr);
	cudaMemset(dev_Aziecho, 0, sizeof(T4)*Nr);
	cudaMemset(dev_Eleecho, 0, sizeof(T4)*Nr);

	for (int frame = 0; frame < SimData->SenceTarNum; frame++)
	{
		//提取目标RCS链表
		int iTarNo = pTarget[frame].ModeNo;
		stTarPitLink *pStTarPitLink = nullptr;
		stTarLink *pStTarLinkTemp = m_StTarLink;
		bool IsTarExit = false;
		while (pStTarLinkTemp->next)
		{
			pStTarLinkTemp = pStTarLinkTemp->next;
			if (pStTarLinkTemp->iTarNo == iTarNo)
			{
				pStTarPitLink = pStTarLinkTemp->pStTarPitLink;
				IsTarExit = true;
				break;
			}
		}
		if (IsTarExit == false){
			qDebug() << "GPU:" << m_DevID<< __LINE__ << __FUNCTION__<<"-----------------------------------IsTarExit: false!";
			continue;
		}
		for (int idx = 0; idx<Na; idx++)
		{
			Ps_i[0] = pPlatForm[idx].NorthPos;
			Ps_i[1] = pPlatForm[idx].SkyPos;
			Ps_i[2] = pPlatForm[idx].EastPos;

			RadarVel_All[0] = pPlatForm[idx].NorthVel;
			RadarVel_All[1] = pPlatForm[idx].SkyVel;
			RadarVel_All[2] = pPlatForm[idx].EastVel;
			//取出对应角度上的散射系数
            float *pTarDYTRang =(float*)_Malloc(3*sizeof(float));
            pTarDYTRang[0] = pTarget[frame].TargetX - Ps_i[0];
            pTarDYTRang[1] = pTarget[frame].TargetY - Ps_i[1];
            pTarDYTRang[2] = pTarget[frame].TargetZ - Ps_i[2];
            //计算目标旋转矩阵
			float temp = acos(RadarVel_All[0] / (sqrt(RadarVel_All[0] * RadarVel_All[0] + RadarVel_All[2] * RadarVel_All[2])));
			float alpha = RadarVel_All[2] >= 0 ? (temp) : (PI + temp);
            float *pMatixG = (float*)_Malloc(9*sizeof(float));
            pClassCoorDinate->CoorMatrixG(alpha,0,0,pMatixG);
            //入射角
            float *pBoat = (float*)_Malloc(9*sizeof(float));//本体坐标系下弹目距位置
            pClassCoorDinate->CoorBtdToBoat(1, pMatixG,(float*)&pTarget[frame].TargetX,pTarDYTRang,pBoat);
            float Azi = atan(pBoat[1]/ pBoat[0]);
            float RadarTar = sqrt(pBoat[0]*pBoat[0] + pBoat[1]*pBoat[1] + pBoat[2]*pBoat[2]);
            float pit = asin(pBoat[2]/RadarTar);
			stTarAziLink *pStTarAziLinkNode = CalTarRcs(abs(pit * 180 / PI), abs(Azi * 180 / PI), pStTarPitLink);
            //散射点从本体转到北天东
            float *pBoatTemp = (float*)_Malloc(pStTarAziLinkNode->pointNum*sizeof(float)*5);
			int offset = 0;
			char *pBf = (char*)pBoatTemp;
			cudaMemcpy(pBf + offset, pStTarAziLinkNode->pMissileData->x, pStTarAziLinkNode->pointNum*sizeof(float), cudaMemcpyHostToHost); offset += pStTarAziLinkNode->pointNum*sizeof(float);
			cudaMemcpy(pBf + offset, pStTarAziLinkNode->pMissileData->y, pStTarAziLinkNode->pointNum*sizeof(float), cudaMemcpyHostToHost); offset += pStTarAziLinkNode->pointNum*sizeof(float);
			cudaMemcpy(pBf + offset, pStTarAziLinkNode->pMissileData->z, pStTarAziLinkNode->pointNum*sizeof(float), cudaMemcpyHostToHost); offset += pStTarAziLinkNode->pointNum*sizeof(float);
			cudaMemcpy(pBf + offset, pStTarAziLinkNode->pMissileData->rcs, pStTarAziLinkNode->pointNum*sizeof(float), cudaMemcpyHostToHost); offset += pStTarAziLinkNode->pointNum*sizeof(float);
			cudaMemcpy(pBf + offset, pStTarAziLinkNode->pMissileData->phi, pStTarAziLinkNode->pointNum*sizeof(float), cudaMemcpyHostToHost); offset += pStTarAziLinkNode->pointNum*sizeof(float);
			
            float *pBoat2 = (float*)_Malloc(pStTarAziLinkNode->pointNum*sizeof(float)*5);

            for(int i = 0; i < pStTarAziLinkNode->pointNum;i++)
            {
                pBoat2[i*3 + 0] = pBoatTemp[i + 0];
                pBoat2[i*3 + 1] = pBoatTemp[i + pStTarAziLinkNode->pointNum];
                pBoat2[i*3 + 2] = pBoatTemp[i + pStTarAziLinkNode->pointNum*2];
            }

            float *pBTD = (float*)_Malloc(pStTarAziLinkNode->pointNum*sizeof(float)*5);
            pClassCoorDinate->CoorBoatToBtd(pStTarAziLinkNode->pointNum, pMatixG,(float*)&pTarget[frame].TargetX,pBoat2,pBTD);

            float *pDMCTarget1 = (float*)_Malloc(pStTarAziLinkNode->pointNum*sizeof(float));
            float *pDMCTarget2 = (float*)_Malloc(pStTarAziLinkNode->pointNum*sizeof(float));
            float *pDMCTarget3 = (float*)_Malloc(pStTarAziLinkNode->pointNum*sizeof(float));
            float *pDMCTarget4 = (float*)_Malloc(pStTarAziLinkNode->pointNum*sizeof(float));
            float *pDMCTarget5 = (float*)_Malloc(pStTarAziLinkNode->pointNum*sizeof(float));
            for(int i = 0; i < pStTarAziLinkNode->pointNum;i++)
            {
                pDMCTarget1[i] = pBTD[i*3 + 0];
                pDMCTarget2[i] = pBTD[i*3 + 1];
                pDMCTarget3[i] = pBTD[i*3 + 2];
                pDMCTarget4[i] = pBoatTemp[i + pStTarAziLinkNode->pointNum*3];
                pDMCTarget5[i] = pBoatTemp[i + pStTarAziLinkNode->pointNum*4];
            }
            cudaMemcpy(dev_DMCTarget_ALL1,pDMCTarget1,pStTarAziLinkNode->pointNum*sizeof(float),cudaMemcpyHostToDevice);
            cudaMemcpy(dev_DMCTarget_ALL2,pDMCTarget2,pStTarAziLinkNode->pointNum*sizeof(float),cudaMemcpyHostToDevice);
            cudaMemcpy(dev_DMCTarget_ALL3,pDMCTarget3,pStTarAziLinkNode->pointNum*sizeof(float),cudaMemcpyHostToDevice);
            cudaMemcpy(dev_DMCTarget_ALL4,pDMCTarget4,pStTarAziLinkNode->pointNum*sizeof(float),cudaMemcpyHostToDevice);
            cudaMemcpy(dev_DMCTarget_ALL5,pDMCTarget5,pStTarAziLinkNode->pointNum*sizeof(float),cudaMemcpyHostToDevice);

//			dev_DMCTarget_ALL1 = pStTarAziLinkNode->pMissileDataGPU.x;
//			dev_DMCTarget_ALL2 = pStTarAziLinkNode->pMissileDataGPU.y;
//			dev_DMCTarget_ALL3 = pStTarAziLinkNode->pMissileDataGPU.z;
//			dev_DMCTarget_ALL4 = pStTarAziLinkNode->pMissileDataGPU.rcs;
//			dev_DMCTarget_ALL5 = pStTarAziLinkNode->pMissileDataGPU.phi;
			TarNum = pStTarAziLinkNode->pointNum;
			else_param[0] = TarNum;
			cudaMemcpy(dev_else_param, else_param, 15 * sizeof(int), cudaMemcpyHostToDevice);
			cudaMemcpy(dev_Ps, Ps_i, sizeof(double) * 3, cudaMemcpyHostToDevice);
			cudaMemcpy(dev_RadarVel_ALL, RadarVel_All, sizeof(double) * 3, cudaMemcpyHostToDevice);
			//cudaMemset(dev_Sumecho, 0, sizeof(T4)*Nr);
			//cudaMemset(dev_Aziecho, 0, sizeof(T4)*Nr);
			//cudaMemset(dev_Eleecho, 0, sizeof(T4)*Nr);
			Err = cudaGetLastError();
			dim3 ThreadsPerBlock(512, 1);
			dim3 BlockNum((TarNum + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
			CUDA_cal_ant_all_kernel_DMC(0, ThreadsPerBlock, BlockNum, dev_Ps, dev_DMCTarget_ALL1, dev_DMCTarget_ALL2, dev_DMCTarget_ALL3,
                                        dev_RadarVel_ALL,dev_SumAntennaFunction,dev_AziSubAntennaFunction,dev_PitSubAntennaFunction,
                                        dev_Matrix, dev_param, dev_else_param, dev_CosValue,dev_SinValue,dev_ant_factor, dev_AziAnt_factor, dev_EleAnt_factor,
                                        dev_Range, dev_Range_2,dev_Fd);  //计算每个目标的天线加权(固定距离门)

			Err = cudaGetLastError();
			int *pT = (int*)_Malloc(1024);
			cudaMemcpy(pT, dev_ant_factor, 15 * sizeof(int), cudaMemcpyDeviceToHost);

			int FdFlag = 0;
			if (FdFlag == 1)
			{
				CUDA_cal_hsys_all_kernel_jushu(0, ThreadsPerBlock, BlockNum, dev_Ps, dev_DMCTarget_ALL4, dev_DMCTarget_ALL5, dev_RadarVel_ALL,
					dev_param2, dev_else_param, dev_CosValue, dev_SinValue,
					dev_ant_factor, dev_AziAnt_factor, dev_EleAnt_factor, dev_Range, dev_Range_2, dev_need_re, dev_need_im,
					dev_need_re_Azi, dev_need_im_Azi, dev_need_re_Ele, dev_need_im_Ele, dev_id);
				Err = cudaGetLastError();
				cudaMemcpy(pT, dev_id, 15 * sizeof(int), cudaMemcpyDeviceToHost);
				cudaMemcpy(pT, dev_id, 15 * sizeof(int), cudaMemcpyDeviceToHost);

				ThreadsPerBlock.x = 512;
				BlockNum.x = (TrNum*TarNum + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x;
				CUDA_cal_Mode_Echo(0, ThreadsPerBlock, BlockNum, dev_param, dev_else_param, dev_CosValue, dev_SinValue,
					dev_ant_factor, dev_AziAnt_factor, dev_EleAnt_factor, dev_Fd, dev_need_re, dev_need_im,
					dev_need_re_Azi, dev_need_im_Azi, dev_need_re_Ele, dev_need_im_Ele, dev_id,
					dev_Signal, dev_Sumecho, dev_Aziecho, dev_Eleecho);
				Err = cudaGetLastError();
			}
			else
			{
				CUDA_cal_hsys_all_kernel_jushuDMC(0, ThreadsPerBlock, BlockNum, dev_Ps, dev_DMCTarget_ALL4, dev_DMCTarget_ALL5, dev_RadarVel_ALL,
												dev_param2, dev_else_param, dev_CosValue, dev_SinValue,
				                                dev_ant_factor, dev_AziAnt_factor, dev_EleAnt_factor, dev_Range, dev_Range_2, 
												dev_Sumecho, dev_Aziecho, dev_Eleecho);

				_CufftExecC2C(plan[0], dev_hsys + Nr*idx, dev_hsys + Nr*idx, CUFFT_FORWARD);
				BlockNum.x = ((Nr + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x);
				BlockNum.y = 1;
				CUDA_ComplexMutiple(cudaStream[0], ThreadsPerBlock, BlockNum, (T4*)dev_Signal, (T4*)dev_hsys + Nr*idx, (T4*)dev_Echo + Nr*idx, Nr);
				_CufftExecC2C(plan[0], dev_Echo + Nr*idx, dev_Echo + Nr*idx, CUFFT_INVERSE);
				Err = cudaGetLastError();
			}


			




		}
	}

	cudaMemcpy(EchoLine, dev_Sumecho, sizeof(T4)*SaveNr, cudaMemcpyDeviceToHost);
	memcpy(&SumCluDataPolar1[0*SaveNr], EchoLine, sizeof(T4)*SaveNr);

	if (SimData->AziEleFalg == 2)
	{
		cudaMemcpy(EchoLine, dev_Aziecho, sizeof(T4)*SaveNr, cudaMemcpyDeviceToHost);
		memcpy(&AziCluDataPolar1[0*SaveNr], EchoLine, sizeof(T4)*SaveNr);

		cudaMemcpy(EchoLine, dev_Eleecho, sizeof(T4)*SaveNr, cudaMemcpyDeviceToHost);
		memcpy(&EleCluDataPolar1[0*SaveNr], EchoLine, sizeof(T4)*SaveNr);
		//memcpy(&EleCluDataPolar2[idx*SaveNr], EchoLine, sizeof(T4)*SaveNr);
	}
	if (m_DevID == 0)
	{
		char *pppp = (char*)_Malloc(Na*Nr*sizeof(T4));
		QFile *pQFile = new QFile;
		pQFile->setFileName("D:\\yjy\\files\\HQ\\HQRD\\CalcutePool\\data\\EchoDMC");
		pQFile->open(QFile::WriteOnly);
		pQFile->write((char*)SumCluDataPolar1, sizeof(T4) * SaveNr);
		pQFile->write((char*)AziCluDataPolar1, sizeof(T4) * SaveNr);
		pQFile->write((char*)EleCluDataPolar1, sizeof(T4) * SaveNr);
		pQFile->close();
		delete pQFile;
	}

	cudaEventRecord(e_stop, 0);
	cudaEventSynchronize(e_stop);
	float elapsedTime = 0, elapsedTime2 = 0;
	cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
	cudaEventElapsedTime(&elapsedTime2, e_start2, e_stop);
	//qDebug() << "GPU:" << m_DevID << "============  CUDA Cost Time is  " << (elapsedTime2) << " ms";
	//qDebug() << "GPU:" << m_DevID << "============  ThreadClassSarEcho Cost Time is  " << (elapsedTime) << " ms";


}
//回波生成槽函数
void ThreadClassSarEcho::slotGenSenceEcho(void *p)
{
	//if (m_DevID == 0)return;
	int ret = cudaSetDevice(m_DevID);

	qDebug() << "-------------------------------------------------------------------------------------------------------------------Thread ID:" << this->thread()->currentThreadId();

    _Release_Malloc();      //初始化伪内存池
    _Release_MallocCUDA();

	cudaEventRecord(e_start, 0);
    
    stSarEchoPara *pStSarEchoPara = (stSarEchoPara*)p;

    RADAR *ParamR       = pStSarEchoPara->ParamR;
    ANTPARAM *AntParam  = pStSarEchoPara->AntParam;
    SimStruct *SimData  = (SimStruct*)&pStSarEchoPara->SimData;
	PLATFORM *pPlatForm = pStSarEchoPara->PlatForm;
	Target *pTarget		= pStSarEchoPara->pTarget;
    double *RadarAPC    = pStSarEchoPara->RadarAPC;
	double	*RadarVel	= pStSarEchoPara->RadarVel;
    double *SenceTarget = pStSarEchoPara->SenceTarget;
    //发射信号生成
	Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) * ParamR->Nr);
	memset(BaseSignal, 0, sizeof(Complexf) * ParamR->Nr);
	int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数
	ThreadClassSendWaveGen::Instance()->TransSignalSim(ParamR, SimData, TrNum, BaseSignal);
    
	stEchoData *pStEchoData		= _MallocMemObj(stEchoData);
	Complexf *SumEchoDataPolar1	= pStEchoData->SumEchoDataPolar1;
	Complexf *AziEchoDataPolar1 = pStEchoData->AziEchoDataPolar1;
	Complexf *EleEchoDataPolar1 = pStEchoData->EleEchoDataPolar1;
	Complexf *ExEchoDataPolar1	= pStEchoData->ExEchoDataPolar1;
	if (pStSarEchoPara->EchoType == 0)//单脉冲
	{
		//单脉冲模式生成回波
		GenDMCEcho(pTarget, pPlatForm, ParamR, AntParam, SimData, RadarAPC, RadarVel, SenceTarget, BaseSignal,
					SumEchoDataPolar1, AziEchoDataPolar1, EleEchoDataPolar1, ExEchoDataPolar1);
		pStEchoData->SumEchoData1Dot	= SimData->SaveFlie_Nr;
		pStEchoData->ExEchoData1Dot		= 0;
		if (SimData->AziEleFalg > 0){
			pStEchoData->AziEchoData1Dot = SimData->SaveFlie_Nr;
			pStEchoData->EleEchoData1Dot = SimData->SaveFlie_Nr;
		}

	}
	else
	{
		//面目标回波生成
		GenSenceEcho(ParamR, AntParam, SimData, RadarAPC, SenceTarget, BaseSignal,
					SumEchoDataPolar1, AziEchoDataPolar1, EleEchoDataPolar1, ExEchoDataPolar1);
		pStEchoData->SumEchoData1Dot = SimData->SaveFlie_Nr;
	}
    
	pStEchoData->Nr_Save = SimData->SaveFlie_Nr;
	pStEchoData->Na_Save = ParamR->Na;
	memcpy(pStEchoData->PlatForm, pStSarEchoPara->PlatForm, sizeof(PLATFORM)*ParamR->Na);
    
	pStEchoData->FrameSerial		= pStSarEchoPara->FrameSerial;
	pStEchoData->childFrameSerial	= pStSarEchoPara->childFrameSerial;
	pStEchoData->childFrameNum		= pStSarEchoPara->childFrameNum;
	pStEchoData->Rmin = pStSarEchoPara->SimData.Rmin;
    //生成的回波发送到回波整合线程进行数据格式转换
	emit sendEchoScheduling(pStEchoData);
    
	if (pStSarEchoPara->RadarAPC)
		_ReleaseMemObj(stRadarAPC, pStSarEchoPara->RadarAPC);
	if (pStSarEchoPara->RadarVel)
		_ReleaseMemObj(stRadarAPC, pStSarEchoPara->RadarVel);
    _ReleaseMemObj(stSarEchoPara,p);

	cudaEventRecord(e_stop, 0);
	cudaEventSynchronize(e_stop);
	float elapsedTime = 0;
	cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
	if (elapsedTime > 9.95f)
		qDebug() << "GPU:" << m_DevID << "============  ThreadClassSarEcho Cost Time is  " << (elapsedTime) << " ms";
}
//波形
void ThreadClassSarEcho::slotASKWaveDate()
{
    m_bWaveDataShowFlag = true;
}
//天线
void ThreadClassSarEcho::slotSettingAntPara(void *p)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	ANTPARAM        *AntParam = (ANTPARAM*)p;
	//***********天线加权gpu***********//
	cudaMemcpy(dev_SumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
	cudaMemcpy(dev_AziSubAntennaFunction, AntParam->AziSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
	cudaMemcpy(dev_PitSubAntennaFunction, AntParam->PitSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
	cudaMemcpy(dev_Matrix, AntParam->G_Matrix, sizeof(double) * 9, cudaMemcpyHostToDevice);

	memcpy(m_AntParam, AntParam, sizeof(ANTPARAM));
}
//场景
void ThreadClassSarEcho::slotSettingSarSence(void *p)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();
    stSarEchoPara   *pStSarEchoPara	= (stSarEchoPara*)p;
    ANTPARAM        *AntParam       = (ANTPARAM*)&pStSarEchoPara->AntParam[0];
    //***********天线加权gpu***********//
    cudaMemcpy(dev_SumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_AziSubAntennaFunction, AntParam->AziSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_PitSubAntennaFunction, AntParam->PitSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_Matrix, AntParam->G_Matrix, sizeof(double) * 9, cudaMemcpyHostToDevice);
    //***********天线加权gpu***********//

    double *TargetAll = pStSarEchoPara->SenceTarget;
    int TarNum = pStSarEchoPara->TarNum;
    float *TargetAll1, *TargetAll2, *TargetAll3, *TargetAll4, *TargetAll5;
    TargetAll1 = (float *)_Malloc(sizeof(float)*TarNum);
    TargetAll2 = (float *)_Malloc(sizeof(float)*TarNum);
    TargetAll3 = (float *)_Malloc(sizeof(float)*TarNum);
    TargetAll4 = (float *)_Malloc(sizeof(float)*TarNum);
    TargetAll5 = (float *)_Malloc(sizeof(float)*TarNum);
    for (int i = 0; i < TarNum; i++)
    {
        TargetAll1[i] = TargetAll[i*TARGET_LEN];
        TargetAll2[i] = TargetAll[i*TARGET_LEN + 1];
        TargetAll3[i] = TargetAll[i*TARGET_LEN + 2];
        TargetAll4[i] = TargetAll[i*TARGET_LEN + 3];
        TargetAll5[i] = TargetAll[i*TARGET_LEN + 4];
    }
    //GPU数字高程总信息
    if(pStSarEchoPara->EchoType == 1)//SAR回波
    {
        cudaMemcpy(dev_Target_ALL1, TargetAll1, sizeof(float)*TarNum, cudaMemcpyHostToDevice);
        cudaMemcpy(dev_Target_ALL2, TargetAll2, sizeof(float)*TarNum, cudaMemcpyHostToDevice);
        cudaMemcpy(dev_Target_ALL3, TargetAll3, sizeof(float)*TarNum, cudaMemcpyHostToDevice);
        cudaMemcpy(dev_Target_ALL4, TargetAll4, sizeof(float)*TarNum, cudaMemcpyHostToDevice);
        cudaMemcpy(dev_Target_ALL5, TargetAll5, sizeof(float)*TarNum, cudaMemcpyHostToDevice);
    }
    else
    {
		//TargetAll1[0] = 0;
		//TargetAll2[0] = 0;
		//TargetAll3[0] = 0;

        cudaMemcpy(dev_DMCTarget_ALL1, TargetAll1, sizeof(float)*TarNum, cudaMemcpyHostToDevice);
        cudaMemcpy(dev_DMCTarget_ALL2, TargetAll2, sizeof(float)*TarNum, cudaMemcpyHostToDevice);
        cudaMemcpy(dev_DMCTarget_ALL3, TargetAll3, sizeof(float)*TarNum, cudaMemcpyHostToDevice);
        cudaMemcpy(dev_DMCTarget_ALL4, TargetAll4, sizeof(float)*TarNum, cudaMemcpyHostToDevice);
        cudaMemcpy(dev_DMCTarget_ALL5, TargetAll5, sizeof(float)*TarNum, cudaMemcpyHostToDevice);
    }

}

void ThreadClassSarEcho::GenSenceEcho(void *p,stEchoData *pStEchoData)
{
	cudaSetDevice(m_DevID);
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();

	stSarEchoPara *pStSarEchoPara = (stSarEchoPara*)p;

	RADAR *ParamR = pStSarEchoPara->ParamR;
	ANTPARAM *AntParam	= m_AntParam;
	SimStruct *SimData	= (SimStruct*)&pStSarEchoPara->SimData;
	PLATFORM *pPlatForm = pStSarEchoPara->PlatForm;
	Target	*pTarget	= pStSarEchoPara->pTarget;
	double *RadarAPC = pStSarEchoPara->RadarAPC;
	double	*RadarVel = pStSarEchoPara->RadarVel;
	//发射信号生成
	Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) * ParamR->Nr);
	memset(BaseSignal, 0, sizeof(Complexf) * ParamR->Nr);
	int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数
	ParamR->Kr = ParamR->Br / ParamR->Tp;
	ThreadClassSendWaveGen::Instance()->TransSignalSim(ParamR, SimData, TrNum, BaseSignal);

	Complexf *SumEchoDataPolar1 = pStEchoData->SumEchoDataPolar1;
	Complexf *AziEchoDataPolar1 = pStEchoData->AziEchoDataPolar1;
	Complexf *EleEchoDataPolar1 = pStEchoData->EleEchoDataPolar1;
	Complexf *ExEchoDataPolar1 = pStEchoData->ExEchoDataPolar1;
	if (pStSarEchoPara->EchoType == 0)//单脉冲
	{
		//单脉冲模式生成回波
		GenDMCEcho(pTarget, pPlatForm,ParamR, AntParam, SimData, RadarAPC, RadarVel, (double*)nullptr, BaseSignal,
			SumEchoDataPolar1, AziEchoDataPolar1, EleEchoDataPolar1, ExEchoDataPolar1);
		pStEchoData->SumEchoData1Dot	= SimData->SaveFlie_Nr;
		pStEchoData->ExEchoData1Dot		= 0;
		if (SimData->AziEleFalg > 0){
			pStEchoData->AziEchoData1Dot = SimData->SaveFlie_Nr;
			pStEchoData->EleEchoData1Dot = SimData->SaveFlie_Nr;
		}
	}
	else
	{
		//面目标回波生成
		GenSenceEcho(ParamR, AntParam, SimData, RadarAPC, (double*)nullptr, BaseSignal,
			SumEchoDataPolar1, AziEchoDataPolar1, EleEchoDataPolar1, ExEchoDataPolar1);
		pStEchoData->SumEchoData1Dot = SimData->SaveFlie_Nr;
	}

	pStEchoData->Nr_Save = SimData->SaveFlie_Nr;
	pStEchoData->Na_Save = ParamR->Na;
	pStEchoData->Rmin = SimData->Rmin;
	memcpy(pStEchoData->PlatForm, pStSarEchoPara->PlatForm, sizeof(PLATFORM)*ParamR->Na);

}




