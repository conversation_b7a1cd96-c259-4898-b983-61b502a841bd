/*******************************************************************************************
 * FileProperties: 
 *     FileName: _stumempool.h
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/MemPool/_stumempool.h $
 *         $Author: yening $
 *         $Revision: 19 $
 *         $Date: 2024-12-22 15:19:29 $
*******************************************************************************************/
#ifndef _STUMEMPOOL_H
#define _STUMEMPOOL_H

#ifdef linux
    #define Q_DECL_EXPORT __attribute__((visibility("default")))
    #define Q_DECL_IMPORT __attribute__((visibility("default")))
#else
    #define Q_DECL_EXPORT __declspec(dllexport)
    #define Q_DECL_IMPORT __declspec(dllimport)
#endif

#if defined(_STUMEMPOOL_LIBRARY)
#  define _STUMEMPOOL_EXPORT Q_DECL_EXPORT
#else
#  define _STUMEMPOOL_EXPORT Q_DECL_IMPORT
#endif

#include<string.h>

#define _DefineMemPool      _DefineMemPool_
#define _DefineMemPoolCUDA  _DefineMemPoolCUDA_
#define _MallocMemObj       _MallocMemObj_
#define _ReleaseMemObj      _ReleaseMemObj_

class _STUMEMPOOL_EXPORT _StuMemPool
{
public:
    explicit _StuMemPool();
    ~_StuMemPool();
    ///
    bool InitStuMemPool(int PoolNum = 10,int NodeSize = 512*1024);
    bool InitStuMemPoolCUDA(int PoolNum = 10,int NodeSize = 512*1024);
    ///
    void* _MallocMemBlock(int length);
    ///
	void _ReleassMemBlock(void* p);
    ///
	int GetAllowedNode(int bytes);
    ///
	void SetMemPoolSrcFileName(char *FileName, int bytes);
    
    #define _DefineMemPool_(type,num)  (InitStuMemPool(num,sizeof(type)))
    #define _DefineMemPoolCUDA_(type,num)  (InitStuMemPoolCUDA(num,sizeof(type)))
    #define _MallocMemObj_(type)      ((type*)_MallocMemBlock(sizeof(type)))
    #define _ReleaseMemObj_(type,p)    _ReleassMemBlock(p)

private:
    void *ptrMem;
};

#endif // _STUMEMPOOL_H
