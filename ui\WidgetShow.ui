<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WidgetShow</class>
 <widget class="QWidget" name="WidgetShow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>993</width>
    <height>577</height>
   </rect>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16677</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QFrame" name="framePara">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>40</height>
        </size>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <widget class="QPushButton" name="pushButtonRefresh">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="text">
           <string>刷新波形</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QFrame" name="frameMain">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>100</height>
        </size>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
