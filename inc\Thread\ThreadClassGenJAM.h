/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassInterference.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassGenJAM.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef THREADCLASSGENJAM_H
#define THREADCLASSGENJAM_H

#include <QObject>

#include"MyMalloc.h"
#include"MyMallocCUDA.h"
#include "_stumempool.h"

#include "WhiteCalGlobel.h"

#include<cufft.h>
#include <curand.h>

class ThreadClassGenJAM : public QObject,MyMalloc,MyMallocCUDA,_StuMemPool
{
    Q_OBJECT
public:
    explicit ThreadClassGenJAM(QObject *parent = 0);
    ~ThreadClassGenJAM();
    
public:
    //初始化GPU设备工作参数
	bool InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer);
	//初始化cuda参数
	bool InitCUDAPara();
    //************生成干扰信号************//
	template <class T, class T2>
	void GenJAM(PLATFORM *Target[TARGET_NUM], JAM_PARAM Jammer[TARGET_NUM], RADAR *ParamR, ANTPARAM *AntParam, SimStruct *SimData, PLATFORM *RadarPos, T *BaseSignal,
				T2 *SumJamDataPolar1, T2 *AziJamDataPolar1, T2 *EleJamDataPolar1, T2 *ExJamDataPolar1);

signals:
	void sendJamDataFormat(void *p);

public slots:
    //干扰槽函数
    void slotGenJAM(void *p);

private:
    template<class T,class T2,class T3,class T4,class T5,class T6,class T7>
    void CalAntWeight(ThreeDimension Tar2Radar, T *G_Matrix, T2 *AntFuction, T3 *AziAntFuction,
                       T4 *PitAntFuction, T5 *Param, T6 *AntGain, T6 *AziGain, T6 *EleGain, T7 *Range, int AziEleFalg);
    //扫频模式频点计算
    template <class T, class T2, class T3, class T4>
    void SweepFreGen(int SweepType, T SweepFmin, T2 SweepFmax, T3 SweepVel, int SweepCyclePRTNum, int SweepPRTNum, T4 *SweepFCur);
    //噪声生成
    template <class T,class T2,class T3,class T4,class T5,class T6,class T7>
    void NoiseModelGen(int NoiseSigLen, T MeanValue, T2 StdValue, T3 NoiseFc, T4 NoiseBr, T5 NoiseAmp, T6 Fs, T7 *dev_NoiseModel);
    //压制干扰生成
    template <class T,class T2>
    void BlanketJamGen(JAM_PARAM JamParam, double Fs, int JamSigLen, int PRFID, int PRTLen, T *SweepFCur, T2 *BlanketJamSig);
    //欺骗干扰生成
    template<class T,class T2>
    void DeceptionJamGen(JAM_PARAM JamParam, double Fs, double Tr, int JamSigLen, int PRFID, int PRTLen, double SimTime,
                                            double Range, double Lambda, T *dev_SourceSignal, T2 *dev_DeceptionJamSig);
    void ActiveJamming(Complexf *JamData, Complexf *BaseSignal, RADAR *ParamR, JAM_PARAM Jammer, SimStruct *SimData,
                                          double PRTTime, double *Range, double *SweepFCur, int EchoLen, int PRFID);
    
private:
    //生成任意PRF的APC矩阵(仅用了Na-1个PRI序列的值，生成Na*3个APC序列)
	template <class T, class T2>
	void PRIGenAPC(int Na, T *PRI, PLATFORM *Target, ANTPARAM *AntParam, T2 *APC);
    //******************生成变PRT的序列************//
	template <class T>
	void GenPRI(RADAR *ParamR, T *PRI);
    //生成APC矩阵
	template <class T, class T2>
	void GenAPC(int Na, T Prf, PLATFORM *Target, T2 *APC);
	template <class T, class T2, class T3>
    void PassiveJamming(PLATFORM *PassJamPlat[TARGET_NUM], RCS Rcs[TARGET_NUM], RADAR *ParamR, ANTPARAM *AntParam, SimStruct *SimData,
                        T *RadarAPC[TARGET_NUM], T *PassJamPlatAPC[TARGET_NUM], T *PRI[TARGET_NUM], int PassJamNum, T2 *dev_BaseSignal,
                        T3 *dev_SumJamDataPolar1, T3 *dev_AziJamDataPolar1, T3 *dev_EleJamDataPolar1, T3 *dev_ExJamDataPolar1);
    //和差差波形生成
    void AziEleWaveGen(int AziEleFalg,int DelayID,int TrNum,Complex *JamData,Complex *AziJamData, Complex *EleJamData,double TransLoss,double *CosPhase,double *SinPhase,
           double CosDop,double SinDop,double *AziGain,double *EleGain,double DopPhase);

	template <class T, class T2, class T3, class T4>
	void GenChaffJam(SimStruct *SimData, JAM_PARAM *Jammer, RADAR *ParamR, T *RadarAPC, T2 *Matrix,
						ANTPARAM *AntParam, T *PRI, T3 *BaseSignal,
						T4 *SumJamDataPolar1, T4 *AziJamDataPolar1, T4 *EleJamDataPolar1, T4 *ExJamDataPolar1);
    
private:
    int m_DevID;
	char *d_BufferFather, *d_BufferInit;
	long long BufferInit_SizeTotal, BufferInit_Cur;

    curandGenerator_t	gen_curand;
	cufftHandle			plan;

    cufftHandle plan_1k;
    cufftHandle plan_2k;
    cufftHandle plan_4k;
    cufftHandle plan_8k;
    cufftHandle plan_16k;
    cufftHandle plan_32k;
    cufftHandle plan_64k;
    cufftHandle plan_128k;
    cufftHandle plan_256k;
    cufftHandle plan_512k;
    cufftHandle CufftPlanCheck(int fftDot);

	int CosValueLen;//三角函数表长
	float *dev_CosValue, *dev_SinValue;//三角函数查表显存
	float *m_CosValue, *m_SinValue;
    Complexf *dev_RandomComplexf;
    
};

#endif // THREADCLASSGENJAM_H
