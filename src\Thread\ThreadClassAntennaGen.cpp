/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassAntennaGen.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassAntennaGen.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassAntennaGen.h"
#include<QThread>
#include <cuda_runtime_api.h>
#include <cufft.h>
#include<qfile.h>

#include "KernelPublic.h"
#include "KernelAntenna.h"
#include"KernelJAM.h"
#include"KernelSar.h"

#define NORMALIZE 1
#define ANTSYMBOL 1
ThreadClassAntennaGen::ThreadClassAntennaGen(QObject *parent) : QObject(parent)
{



    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassAntennaGen::~ThreadClassAntennaGen()
{

}
//初始化GPU设备工作参数
bool ThreadClassAntennaGen::InitDeviceWorkPara(int DevID,qint64 buffer_size,char *d_Buffer)
{
    if(d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
        m_DevID         = DevID;
        d_BufferFather  = d_Buffer;
        //分配GPU伪内存池空间
        InitMyMallocCUDA(d_Buffer,buffer_size);
    }
    else{//作为独立的线程使用，单独使用一个GPU卡
		cudaSetDevice(DevID);
        //分配GPU伪内存池空间
        cudaMalloc((void**)&d_Buffer,buffer_size);
		d_BufferFather = d_Buffer;
        InitMyMallocCUDA(d_Buffer,buffer_size);
    }
    return true;
}

/************************************************************************
* @brief	//天线旋转矩阵初始化
* z
* @param[in]	RotAng:			俯仰、偏航、横滚
* @param[in]	GimbalsAngle:	内外框架角
* @param[in]	BeamAngle:      波束方位、俯仰角
* @param[out]	G_Matrix:       坐标转换旋转矩阵3*3
************************************************************************/
void ThreadClassAntennaGen::AntIni(double *RotAng, double *GimbalsAngle, double *BeamAngle, double *G_Matrix)
{
	double RotateMatrix[9];		//北天东->弹体坐标系矩阵
	double GimbalMatrix[9];		//弹体->天线坐标系
	double BeamMatrix[9];			//天线->波束指向坐标系

	RotateMatrix[0 * 3 + 0] = cos(RotAng[0])*cos(RotAng[1]);
	RotateMatrix[1 * 3 + 0] = sin(RotAng[0]);
	RotateMatrix[2 * 3 + 0] = -cos(RotAng[0])*sin(RotAng[1]);

	RotateMatrix[0 * 3 + 1] = -sin(RotAng[0])*cos(RotAng[1])*cos(RotAng[2]) + sin(RotAng[1])*sin(RotAng[2]);
	RotateMatrix[1 * 3 + 1] = cos(RotAng[0])*cos(RotAng[2]);
	RotateMatrix[2 * 3 + 1] = sin(RotAng[0])*sin(RotAng[1])*cos(RotAng[2]) + cos(RotAng[1])*sin(RotAng[2]);

	RotateMatrix[0 * 3 + 2] = sin(RotAng[0])*cos(RotAng[1])*sin(RotAng[2]) + sin(RotAng[1])*cos(RotAng[2]);
	RotateMatrix[1 * 3 + 2] = -cos(RotAng[0])*sin(RotAng[2]);
	RotateMatrix[2 * 3 + 2] = -sin(RotAng[0])*sin(RotAng[1])*sin(RotAng[2]) + cos(RotAng[1])*cos(RotAng[2]);

	GimbalMatrix[0 * 3 + 0] = cos(GimbalsAngle[0])*cos(GimbalsAngle[1]);
	GimbalMatrix[1 * 3 + 0] = sin(GimbalsAngle[1]);
	GimbalMatrix[2 * 3 + 0] = -cos(GimbalsAngle[1])*sin(GimbalsAngle[0]);

	GimbalMatrix[0 * 3 + 1] = -sin(GimbalsAngle[1])*cos(GimbalsAngle[0]);
	GimbalMatrix[1 * 3 + 1] = cos(GimbalsAngle[1]);
	GimbalMatrix[2 * 3 + 1] = sin(GimbalsAngle[0])*sin(GimbalsAngle[1]);

	GimbalMatrix[0 * 3 + 2] = sin(GimbalsAngle[0]);
	GimbalMatrix[1 * 3 + 2] = 0;
	GimbalMatrix[2 * 3 + 2] = cos(GimbalsAngle[0]);


	BeamMatrix[0 * 3 + 0] = cos(BeamAngle[1])*cos(BeamAngle[0]);
	BeamMatrix[1 * 3 + 0] = sin(BeamAngle[1]);
	BeamMatrix[2 * 3 + 0] = -cos(BeamAngle[1])*sin(BeamAngle[0]);

	BeamMatrix[0 * 3 + 1] = -sin(BeamAngle[1])*cos(BeamAngle[0]);
	BeamMatrix[1 * 3 + 1] = cos(BeamAngle[1]);
	BeamMatrix[2 * 3 + 1] = sin(BeamAngle[0])*sin(BeamAngle[1]);

	BeamMatrix[0 * 3 + 2] = sin(BeamAngle[0]);
	BeamMatrix[1 * 3 + 2] = 0;
	BeamMatrix[2 * 3 + 2] = cos(BeamAngle[0]);

	CoordMulTrans(BeamMatrix, GimbalMatrix, G_Matrix);	//矩阵乘，得到3个矩阵的乘积矩阵G_Matrix
	CoordMulTrans(G_Matrix, RotateMatrix, G_Matrix);

}

//线阵阵元 主函数
//*****输出******//
//SumAntennaFunction:方向图

//*****输入******//
//AntGain:天线增益
//ArrayNum:阵元数
//SimAntSpaceNumAzi:方向图-方位向仿真间隔
//SimAntSpaceNumPitch:方向图-俯仰向仿真间隔
//Omiga:各阵元加权
//Fc:载频
//AntSpace:天线阵元间距
//SimPhaseAzi:方向图-方位向仿真点数
//SimPhasePitch:方向图-俯仰向仿真点数
//InitialePhase:第一个阵元的初相
template <class T, class T1, class T2, class T3>
void ThreadClassAntennaGen::LineArrayCellSim(T3 *SumAntennaFunction, T1 AntGain, int ArrayNum, int SimAntSpaceNumAzi,
											int  SimAntSpaceNumPitch,T2 *Omiga,double Fc,T AntSpace,
											T SimPhaseAzi, T SimPhasePitch, T InitialePhase, T2* AntPos)
{
    double SimAntSpacePhaseAzi = SimPhaseAzi/(SimAntSpaceNumAzi - 1);//仿真天线方向图的步进
    double SimAntSpacePhasePitch = SimPhasePitch/(SimAntSpaceNumPitch - 1);//仿真天线方向图的步进
    
    //**********CUDA配置**************//
    dim3 ThreadsPerBlock(512,1);
    dim3 BlockNum((SimAntSpaceNumAzi*SimAntSpaceNumPitch + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
	T Param[10];
	T *dev_Param;
	T2 *dev_Omiga;
	T2 *dev_AntWeight;		//复数方向图
	T3 *dev_AntFuction;				//归一化实数方向图
    cudaMalloc((void**)&dev_Param,      sizeof(T)*10);
	cudaMalloc((void**)&dev_Omiga,		sizeof(T2)*ArrayNum);
	cudaMalloc((void**)&dev_AntWeight,	sizeof(T2)*SimAntSpaceNumPitch * SimAntSpaceNumAzi);
    cudaMalloc((void**)&dev_AntFuction,	sizeof(T3)*SimAntSpaceNumPitch * SimAntSpaceNumAzi);
	cudaMemset(dev_AntWeight, 0, SimAntSpaceNumPitch * SimAntSpaceNumAzi*sizeof(T2));
    cudaMemset(dev_Param,	0, 10*sizeof(double));
	
    Param[0] = AntGain;				//增益
    Param[1] = AntSpace;			//阵元间距
    Param[2] = SimPhaseAzi;			//方向图方位向范围
    Param[3] = SimPhasePitch;		//方向图俯仰向范围
    Param[4] = ArrayNum;			//阵元个数	
    Param[5] = SimAntSpaceNumAzi;	//方向图方位向点数
    Param[6] = SimAntSpaceNumPitch;	//方向图俯仰向点数
    Param[7] = Fc;					//载频
    Param[8] = SimAntSpacePhaseAzi;	//方向图范围向间隔（弧度）
    Param[9] = SimAntSpacePhasePitch;//方向图俯仰向间隔（弧度）
    cudaMemcpy(dev_Param,   Param,    10*sizeof(double), cudaMemcpyHostToDevice);
    cudaMemcpy(dev_Omiga,   Omiga,    ArrayNum*sizeof(T2), cudaMemcpyHostToDevice);
	CUDA_LineArrayCell(0, ThreadsPerBlock, BlockNum, dev_Param, (T2*)dev_Omiga, (T2*)dev_AntWeight);
	CUDA_ComplexAbs(0, ThreadsPerBlock, BlockNum, (T2*)dev_AntWeight, (T3*)dev_AntFuction);
    cudaMemcpy(SumAntennaFunction, dev_AntFuction,       SimAntSpaceNumPitch * SimAntSpaceNumAzi*sizeof(T3), cudaMemcpyDeviceToHost);
	if(ArrayNum%2 == 1)
	{
		for(int idz = 0;idz<ArrayNum;idz++)
		{
			AntPos[idz].x = (idz-ArrayNum/2)*AntSpace;	//横坐标
			AntPos[idz].y = 0;	//纵坐标
		}
	}
	else
	{
		for(int idz = 0;idz<ArrayNum;idz++)
		{
			AntPos[idz].x = (idz-ArrayNum/2+0.5)*AntSpace;	//横坐标
			AntPos[idz].y = 0;	//纵坐标
		}
	}
    //cudaMemcpy(AntPos, dev_AntPos, ArrayNum*sizeof(Complex), cudaMemcpyDeviceToHost);
#if NORMALIZE
	///***************归一化******************///
	int		MaxID[1];
	double	MaxData[1];
    GetMax(SumAntennaFunction,SimAntSpaceNumPitch * SimAntSpaceNumAzi,MaxData,MaxID);
    Param[0] = AntGain;
    Param[1] = *MaxData;
    cudaMemcpy(dev_Param,   Param,    10*sizeof(double), cudaMemcpyHostToDevice);
	CUDA_Normalize(0, ThreadsPerBlock, BlockNum, dev_Param, dev_AntFuction);	//归一化并取dB
    cudaMemcpy(SumAntennaFunction, dev_AntFuction,	SimAntSpaceNumPitch * SimAntSpaceNumAzi*sizeof(T), cudaMemcpyDeviceToHost);
	///***************归一化******************///
#endif
	
    cudaFree(dev_Param);
    cudaFree(dev_Omiga);
    cudaFree(dev_AntWeight);
    cudaFree(dev_AntFuction);
	//**********CUDA配置**************//
}

//线阵子阵 主函数
//*****输出******//
//SumAntennaFunction:方向图

//*****输入******//
//AntGain:天线增益
//ArrayNum:各子阵阵元数
//SubArrayNum:子阵数
//AntFuncAziNum:方向图-方位向仿真间隔
//AntFuncEleNum:方向图-俯仰向仿真间隔
//Omiga:各阵元加权
//SubOmiga:各子阵加权
//Fc:载频
//AntSpace:各阵元间距
//AntAziBound:方向图-方位向仿真点数
//AntEleBound:方向图-俯仰向仿真点数
//SubArraySpace:子阵间距
//InitialePhase:第一个阵元的初相
template <class T>
void  ThreadClassAntennaGen::LineArrayArraySim(T *SumAntennaFunction,double AntGain,int ArrayNum,int SubArrayNum,
					int AntFuncAziNum,int AntFuncEleNum,Complex *Omiga,Complex *SubOmiga,double Fc,
                    double AntSpace,double AntAziBound,double AntEleBound,double SubArraySpace,
					double InitialePhase,Complex *AntPos,Complex *SubAntPos)
{
	double SimAntSpacePhaseAzi = AntAziBound/(AntFuncAziNum - 1);		//仿真天线方向图的间距
    double SimAntSpacePhasePitch = AntEleBound/(AntFuncEleNum - 1);	//仿真天线方向图的间距

    //**********CUDA配置**************//
    dim3 ThreadsPerBlock(512,1);
    dim3 BlockNum((AntFuncAziNum*AntFuncEleNum + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
    double  Param[10];
    double *dev_Param;
	cuDoubleComplex *dev_AntWeight;
    cudaMalloc((void**)&dev_Param,      sizeof(double)*10);
    cudaMalloc((void**)&dev_AntWeight,	sizeof(cuDoubleComplex)*AntFuncAziNum * AntFuncEleNum);
    cudaMemset(dev_AntWeight,0, AntFuncAziNum * AntFuncEleNum*sizeof(cuDoubleComplex));
    cudaMemset(dev_Param,	 0, 10*sizeof(double));

	
	//*********子阵间**********//
	cuDoubleComplex *dev_SubAntWeight;
	cuDoubleComplex *dev_SubOmiga;
    Param[0] = AntGain;			//增益
    Param[1] = SubArraySpace;	//子阵间距
    Param[2] = AntAziBound;		//方向图方位向范围
    Param[3] = AntEleBound;	//方向图俯仰向范围
    Param[4] = SubArrayNum;		//子阵个数
    Param[5] = AntFuncAziNum;	//方向图方位向点数
    Param[6] = AntFuncEleNum;	//方向图俯仰向点数
    Param[7] = Fc;				//载频
    Param[8] = SimAntSpacePhaseAzi;		//方向图范围向间隔（弧度）
    Param[9] = SimAntSpacePhasePitch;	//方向图俯仰向间隔（弧度）
	

    cudaMalloc((void**)&dev_SubAntWeight,	sizeof(cuDoubleComplex)*AntFuncAziNum * AntFuncEleNum);
    cudaMalloc((void**)&dev_SubOmiga, sizeof(cuDoubleComplex)*SubArrayNum );
    cudaMemcpy(dev_Param,   Param,    10*sizeof(double), cudaMemcpyHostToDevice);
    cudaMemcpy(dev_SubOmiga,SubOmiga, SubArrayNum*sizeof(cuDoubleComplex), cudaMemcpyHostToDevice);
	CUDA_LineArrayCell(0, ThreadsPerBlock, BlockNum, dev_Param, (Complex*)dev_SubOmiga, (Complex*)dev_SubAntWeight);
	if(SubArrayNum%2 == 1)
	{
		for(int idz = 0;idz<SubArrayNum;idz++)
		{
			SubAntPos[idz].x = (idz-SubArrayNum/2)*SubArraySpace;	//横坐标
			SubAntPos[idz].y = 0;	//纵坐标
		}
	}
	else
	{
		for(int idz = 0;idz<SubArrayNum;idz++)
		{
			SubAntPos[idz].x = (idz-SubArrayNum/2+0.5)*SubArraySpace;	//横坐标
			SubAntPos[idz].y = 0;	//纵坐标
		}
	}
	//*********子阵间**********//

	//*********子阵内**********//
	cuDoubleComplex *dev_Omiga;
    cudaMalloc((void**)&dev_Omiga,sizeof(cuDoubleComplex)*ArrayNum );
	Param[0] = AntGain;			//增益
    Param[1] = AntSpace;		//阵元间距
    Param[2] = AntAziBound;		//方向图方位向范围
    Param[3] = AntEleBound;	//方向图俯仰向范围
    Param[4] = ArrayNum;		//阵元个数	
    Param[5] = AntFuncAziNum;	//方向图方位向点数
    Param[6] = AntFuncEleNum;	//方向图俯仰向点数
    Param[7] = Fc;				//载频
    Param[8] = SimAntSpacePhaseAzi;		//方向图范围向间隔（弧度）
    Param[9] = SimAntSpacePhasePitch;	//方向图俯仰向间隔（弧度）
    cudaMemcpy(dev_Param,   Param,    10*sizeof(double), cudaMemcpyHostToDevice);
    cudaMemcpy(dev_Omiga,   Omiga,    ArrayNum*sizeof(cuDoubleComplex), cudaMemcpyHostToDevice);
		

	CUDA_LineArrayCell(0, ThreadsPerBlock, BlockNum, dev_Param, (Complex*)dev_Omiga, (Complex*)dev_AntWeight);
	if(ArrayNum%2 == 1)
	{
		for(int idz = 0;idz<ArrayNum;idz++)
		{
			AntPos[idz].x = (idz-ArrayNum/2)*AntSpace;	//横坐标
			AntPos[idz].y = 0;	//纵坐标
		}
	}
	else
	{
		for(int idz = 0;idz<ArrayNum;idz++)
		{
			AntPos[idz].x = (idz-ArrayNum/2+0.5)*AntSpace;	//横坐标
			AntPos[idz].y = 0;	//纵坐标
		}
	}
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, (Complex*)dev_AntWeight, (Complex*)dev_SubAntWeight, (Complex*)dev_AntWeight, 1);
	//*********子阵内**********//

	///***************归一化******************///
	T *dev_AntFuction;
	cudaMalloc((void**)&dev_AntFuction, sizeof(T)*AntFuncAziNum * AntFuncEleNum);
	CUDA_ComplexAbs(0, ThreadsPerBlock, BlockNum, (Complex*)dev_AntWeight, dev_AntFuction);
    cudaMemcpy(SumAntennaFunction, dev_AntFuction, AntFuncAziNum * AntFuncEleNum*sizeof(T), cudaMemcpyDeviceToHost);

#if NORMALIZE
	int		MaxID[1];
	double	MaxData[1];
    GetMax(SumAntennaFunction,AntFuncAziNum * AntFuncEleNum,MaxData,MaxID);
    Param[0] = AntGain;
    Param[1] = *MaxData;
    cudaMemcpy(dev_Param,   Param,    10*sizeof(double), cudaMemcpyHostToDevice);
	CUDA_Normalize(0, ThreadsPerBlock, BlockNum, dev_Param, dev_AntFuction);	//归一化并取dB
    cudaMemcpy(SumAntennaFunction, dev_AntFuction, AntFuncAziNum * AntFuncEleNum*sizeof(T), cudaMemcpyDeviceToHost);
#endif
	///***************归一化******************///
	
    cudaFree(dev_Param);
    cudaFree(dev_Omiga);
    cudaFree(dev_SubOmiga);
    cudaFree(dev_AntWeight);
    cudaFree(dev_SubAntWeight);
    cudaFree(dev_AntFuction);
}

//矩形面阵-阵元 主函数
//*****输出******//
//SumAntennaFunction:方向图

//*****输入******//
//AntGain:天线增益
//AntFuncAziNum:方向图-方位向仿真间隔
//AntFuncEleNum:方向图-俯仰向仿真间隔
//ArrayNumRow:行阵元数
//ArrayNumColumn:列阵元数
//Omiga:各阵元加权
//Fc:载频
//AntSpaceRow:行阵元间距
//ArrayNumColumn:列阵元间距
//AntAziBound:方向图-方位向仿真点数
//AntEleBound:方向图-俯仰向仿真点数
//InitialePhase:第一个阵元的初相
template <class T>
void ThreadClassAntennaGen::RectAngArrayCellSim(T *SumAntennaFunction, double AntGain, int AntFuncAziNum, int AntFuncEleNum,
					int ArrayNumRow,int ArrayNumColumn,Complex *Omiga,double Fc,
                    double AntSpaceRow,double AntSpaceColumn,double AntAziBound,double AntEleBound,double InitialePhase,Complex *AntPos)
{
	double SimAntSpacePhaseAzi	 = AntAziBound/(AntFuncAziNum - 1);//仿真天线方向图的间距
    double SimAntSpacePhasePitch = AntEleBound/(AntFuncEleNum - 1);//仿真天线方向图的间距

    
    //**********CUDA配置**************//
    dim3 ThreadsPerBlock(512,1);
    dim3 BlockNum((AntFuncAziNum*AntFuncEleNum + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
    double Param[15];
    double *dev_Param;
	cuDoubleComplex *dev_Omiga;
	cuDoubleComplex *dev_AntWeight;		//复数方向图
	T *dev_AntFuction;				//归一化实数方向图
	dev_Param		= (double*)_MallocCUDA(sizeof(double) * 15);
	dev_Omiga		= (cuDoubleComplex*)_MallocCUDA(sizeof(cuDoubleComplex)*ArrayNumRow * ArrayNumColumn);
	dev_AntWeight	= (cuDoubleComplex*)_MallocCUDA(sizeof(cuDoubleComplex)*AntFuncAziNum * AntFuncEleNum);
	dev_AntFuction	= (T*)_MallocCUDA(sizeof(T)*AntFuncAziNum * AntFuncEleNum);
    cudaMemset(dev_AntWeight,	0, AntFuncAziNum * AntFuncEleNum*sizeof(cuDoubleComplex));
    cudaMemset(dev_Param,	0, 15*sizeof(double));

	Param[0] = AntSpaceRow;				//行阵元间距
    Param[1] = AntSpaceColumn;			//列阵元间距
    Param[2] = AntAziBound;				//方向图方位向范围
    Param[3] = AntEleBound;				//方向图俯仰向范围
    Param[4] = AntFuncAziNum;			//方向图方位向点数
    Param[5] = AntFuncEleNum;			//方向图俯仰向点数
    Param[6] = ArrayNumRow;				//阵元行数	
    Param[7] = ArrayNumColumn;			//阵元列数		
    Param[8] = SimAntSpacePhaseAzi;		//方向图范围向间隔（弧度）
    Param[9] = SimAntSpacePhasePitch;	//方向图俯仰向间隔（弧度）
    Param[10] = Fc;						//载频

    cudaMemcpy(dev_Omiga,   Omiga,    ArrayNumRow * ArrayNumColumn*sizeof(cuDoubleComplex), cudaMemcpyHostToDevice);

	
    for(int idx=0 ; idx<Param[6]; idx++)
    {
		Param[11] = idx;		//行号
		cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
		CUDA_RectAngArrayCell(0, ThreadsPerBlock, BlockNum, dev_Param, (Complex*)dev_Omiga, (Complex*)dev_AntWeight);
	}
	//****21091008新增，加入天线位置输出******//
	{
		double DeltaRow = 0,DeltaColumn = 0;
		if(ArrayNumRow%2 == 0)
		{
			DeltaRow = 0.5;
		}
		if(ArrayNumColumn%2 == 0)
		{
			DeltaColumn = 0.5;
		}
		for(int idx=0 ; idx<ArrayNumColumn; idx++)
		{
			for(int idy=0;idy<ArrayNumRow;idy++)
			{
				AntPos[idx*ArrayNumRow+idy].x = (idy - ArrayNumRow/2    + DeltaRow)	* AntSpaceRow;
				AntPos[idx*ArrayNumRow+idy].y = (idx - ArrayNumColumn/2 + DeltaColumn) * AntSpaceColumn;
			}
		}
	}
	//****21091008新增，加入天线位置输出******//
    Param[5] = AntFuncAziNum;	//方向图俯仰向点数
    Param[6] = AntFuncEleNum;		//阵元行数	
    cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
	CUDA_ComplexAbs(0, ThreadsPerBlock, BlockNum, (Complex*)dev_AntWeight, (T*)dev_AntFuction);
    cudaMemcpy(SumAntennaFunction, dev_AntFuction, AntFuncAziNum * AntFuncEleNum*sizeof(T), cudaMemcpyDeviceToHost);
	
#if NORMALIZE
	///***************归一化******************///
	int		MaxID[1];
	double	MaxData[1];
    GetMax(SumAntennaFunction,AntFuncAziNum * AntFuncEleNum,MaxData,MaxID);
    Param[0] = AntGain;
    Param[1] = *MaxData;
    cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
	CUDA_Normalize(0, ThreadsPerBlock, BlockNum, dev_Param, dev_AntFuction);	//归一化并取dB
    cudaMemcpy(SumAntennaFunction, dev_AntFuction,	AntFuncAziNum * AntFuncEleNum*sizeof(float), cudaMemcpyDeviceToHost);
	///***************归一化******************///
#endif
	//**********CUDA配置**************//
}

//矩形面阵-子阵 主函数
//*****输出******//
//SumAntennaFunction:方向图

//*****输入******//
//矩形面阵-子阵
//AntGain:天线增益
//AntFuncAziNum:方向图-方位向仿真间隔
//AntFuncEleNum:方向图-俯仰向仿真间隔
//ArrayNumRow:行阵元数
//ArrayNumColumn:列阵元数
//SubArrayNumRow:子阵行阵元数
//SubArrayNumColumn:子阵列阵元数
//Omiga:各阵元加权
//Fc:载频
//AntSpaceRow:行阵元间距
//ArrayNumColumn:列阵元间距
//AntAziBound:方向图-方位向仿真点数
//AntEleBound:方向图-俯仰向仿真点数
//InitialePhase:第一个阵元的初相
template <class T>
void ThreadClassAntennaGen::RectAngArrayArraySim(T *SumAntennaFunction,double AntGain,int AntFuncAziNum,int AntFuncEleNum,
					int ArrayNumRow,int SubArrayNumRow,int ArrayNumColumn,int SubArrayNumColumn,Complex *Omiga,
					Complex *SubOmiga,double Fc,double AntSpaceRow,double SubArraySpcaeRow, double AntSpaceColumn,
                    double SubArraySpcaeColumn,double AntAziBound,double AntEleBound,double InitialePhase,Complex *AntPos,Complex *SubAntPos)

{
	double SimAntSpacePhaseAzi	 = AntAziBound/(AntFuncAziNum - 1);//仿真天线方向图的间距
    double SimAntSpacePhasePitch = AntEleBound/(AntFuncEleNum - 1);//仿真天线方向图的间距

    //**********CUDA配置**************//
    dim3 ThreadsPerBlock(512,1);
    dim3 BlockNum((AntFuncAziNum*AntFuncEleNum + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
    double Param[15];
    double *dev_Param;
	cuDoubleComplex *dev_Omiga;
	cuDoubleComplex *dev_AntWeight;		//复数方向图
	T *dev_AntFuction;				//归一化实数方向图
    cudaMalloc((void**)&dev_Param,      sizeof(double)*15);
    cudaMalloc((void**)&dev_Omiga,      sizeof(cuDoubleComplex)*ArrayNumRow * ArrayNumColumn);
    cudaMalloc((void**)&dev_AntWeight,	sizeof(cuDoubleComplex)*AntFuncAziNum * AntFuncEleNum);
    cudaMalloc((void**)&dev_AntFuction,	sizeof(T)*AntFuncAziNum * AntFuncEleNum);
    cudaMemset(dev_AntWeight,	0, AntFuncAziNum * AntFuncEleNum*sizeof(cuDoubleComplex));
    cudaMemset(dev_Param,	0, 15*sizeof(double));

	//***********子阵内阵元***************//
	Param[0] = AntSpaceRow;		//行阵元间距
    Param[1] = AntSpaceColumn;	//列阵元间距
    Param[2] = AntAziBound;		//方向图方位向范围
    Param[3] = AntEleBound;	//方向图俯仰向范围
    Param[4] = AntFuncAziNum;	//方向图方位向点数
    Param[5] = AntFuncEleNum;	//方向图俯仰向点数
    Param[6] = ArrayNumRow;		//阵元行数	
    Param[7] = ArrayNumColumn;	//阵元列数		
    Param[8] = SimAntSpacePhaseAzi;		//方向图范围向间隔（弧度）
    Param[9] = SimAntSpacePhasePitch;	//方向图俯仰向间隔（弧度）
    Param[10] = Fc;		//载频
    cudaMemcpy(dev_Omiga,   Omiga,    ArrayNumRow * ArrayNumColumn*sizeof(cuDoubleComplex), cudaMemcpyHostToDevice);
	
    for(int idx=0 ; idx<Param[6]; idx++)
    {
		Param[11] = idx;		//行号
		cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
		CUDA_RectAngArrayCell(0, ThreadsPerBlock, BlockNum, dev_Param, (Complex*)dev_Omiga, (Complex*)dev_AntWeight);
	}
	//****21091008新增，加入天线位置输出******//
	{
		double DeltaRow = 0,DeltaColumn = 0;
		if(ArrayNumRow%2 == 0)
		{
			DeltaRow = 0.5;
		}
		if(ArrayNumColumn%2 == 0)
		{
			DeltaColumn = 0.5;
		}
		for(int idx=0 ; idx<ArrayNumColumn; idx++)
		{
			for(int idy=0;idy<ArrayNumRow;idy++)
			{
				AntPos[idx*ArrayNumRow+idy].x = (idy - ArrayNumRow/2    + DeltaRow)	* AntSpaceRow;
				AntPos[idx*ArrayNumRow+idy].y = (idx - ArrayNumColumn/2 + DeltaColumn) * AntSpaceColumn;
			}
		}
	}
	//***********子阵内阵元***************//

	
	//***********子阵间***************//
	cuDoubleComplex *dev_SubAntWeight;		//复数方向图
	cuDoubleComplex *dev_SubOmiga;		//复数方向图
    cudaMalloc((void**)&dev_SubOmiga,	 sizeof(cuDoubleComplex)*SubArrayNumRow * SubArrayNumColumn);
    cudaMalloc((void**)&dev_SubAntWeight,sizeof(cuDoubleComplex)*AntFuncAziNum * AntFuncEleNum);
    cudaMemset(dev_SubAntWeight,	0, AntFuncAziNum * AntFuncEleNum*sizeof(cuDoubleComplex));
	Param[0] = SubArraySpcaeRow;	//行子阵间距
    Param[1] = SubArraySpcaeColumn;	//列子阵间距
    Param[2] = AntAziBound;			//方向图方位向范围
    Param[3] = AntEleBound;			//方向图俯仰向范围
    Param[4] = AntFuncAziNum;		//方向图方位向点数
    Param[5] = AntFuncEleNum;		//方向图俯仰向点数
    Param[6] = SubArrayNumRow;		//子阵行数	
    Param[7] = SubArrayNumColumn;	//子阵列数		
    Param[8] = SimAntSpacePhaseAzi;		//方向图范围向间隔（弧度）
    Param[9] = SimAntSpacePhasePitch;	//方向图俯仰向间隔（弧度）
    Param[10] = Fc;		//载频 
    cudaMemcpy(dev_SubOmiga,   SubOmiga,    SubArrayNumRow * SubArrayNumColumn*sizeof(cuDoubleComplex), cudaMemcpyHostToDevice);
	
    for(int idx=0 ; idx<Param[6]; idx++)
    {
		Param[11] = idx;		//行号
		cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
		CUDA_RectAngArrayCell(0, ThreadsPerBlock, BlockNum, dev_Param, (Complex*)dev_SubOmiga, (Complex*)dev_SubAntWeight);
	}
	//****21091008新增，加入天线位置输出******//
	{
		double DeltaRow = 0,DeltaColumn = 0;
		if(SubArrayNumRow%2 == 0)
		{
			DeltaRow = 0.5;
		}
		if(SubArrayNumColumn%2 == 0)
		{
			DeltaColumn = 0.5;
		}
		for(int idx=0 ; idx<SubArrayNumColumn; idx++)
		{
			for(int idy=0;idy<SubArrayNumRow;idy++)
			{
				SubAntPos[idx*SubArrayNumRow+idy].x = (idy - SubArrayNumRow/2    + DeltaRow)	 * SubArraySpcaeRow;
				SubAntPos[idx*SubArrayNumRow+idy].y = (idx - SubArrayNumColumn/2 + DeltaColumn) * SubArraySpcaeColumn;
			}
		}
	}
	//***********子阵间***************//

	
	//***********合成方向图***************//
    Param[5] = AntFuncAziNum;		//方向图俯仰向点数
    Param[6] = AntFuncEleNum;		//阵元行数	
    cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);

	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, (Complex*)dev_AntWeight, (Complex*)dev_SubAntWeight, (Complex*)dev_AntWeight, 1);//合成方向图
	CUDA_ComplexAbs(0, ThreadsPerBlock, BlockNum, (Complex*)dev_AntWeight, dev_AntFuction);	//取模
    cudaMemcpy(SumAntennaFunction, dev_AntFuction,AntFuncAziNum * AntFuncEleNum*sizeof(T), cudaMemcpyDeviceToHost);
	//***********合成方向图***************//
	
#if NORMALIZE
	///***************归一化******************///
	int		MaxID[1];
	double	MaxData[1];
    GetMax(SumAntennaFunction,AntFuncAziNum * AntFuncEleNum,MaxData,MaxID);
    Param[0] = AntGain;
    Param[1] = *MaxData;
    cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
	CUDA_Normalize(0, ThreadsPerBlock, BlockNum, dev_Param, dev_AntFuction);	//归一化并取dB
    cudaMemcpy(SumAntennaFunction, dev_AntFuction,	AntFuncAziNum * AntFuncEleNum*sizeof(T), cudaMemcpyDeviceToHost);
	///***************归一化******************///
#endif

    cudaFree(dev_Param);
    cudaFree(dev_Omiga);
    cudaFree(dev_AntWeight);
    cudaFree(dev_AntFuction);
	//**********CUDA配置**************//
}

//圆周边界阵-阵元
//*****输出******//
//SumAntennaFunction:方向图

//*****输入******//
//矩形面阵-子阵
//AntGain:天线增益
//AntFuncAziNum:方向图-方位向仿真间隔
//AntFuncEleNum:方向图-俯仰向仿真间隔
//ArrayNumRow:行阵元数
//ArrayNumColumn:列阵元数
//SubArrayNumRow:子阵行阵元数
//SubArrayNumColumn:子阵列阵元数
//ArrayRadius:圆周
//Omiga:各阵元加权
//Fc:载频
//AntSpaceRow:行阵元间距
//ArrayNumColumn:列阵元间距
//AntAziBound:方向图-方位向仿真点数
//AntEleBound:方向图-俯仰向仿真点数
//InitialePhase:第一个阵元的初相
template <class T>
void  ThreadClassAntennaGen::CicleArrayBoundaryCellSim(T *SumAntennaFunction,double AntGain,double ArrayRadius,int AntFuncAziNum,
					int AntFuncEleNum,int ArrayNumRow,int ArrayNumColumn,Complex *Omiga,double Fc,
					double AntSpaceRow,double AntSpaceColumn,double AntAziBound,double AntEleBound,double InitialePhase,Complex *AntPos)

{
	double SimAntSpacePhaseAzi	 = AntAziBound/(AntFuncAziNum - 1);//仿真天线方向图的间距
    double SimAntSpacePhasePitch = AntEleBound/(AntFuncEleNum - 1);//仿真天线方向图的间距

    
    //**********CUDA配置**************//
    dim3 ThreadsPerBlock(512,1);
    dim3 BlockNum((AntFuncAziNum*AntFuncEleNum + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
    double Param[15];
    double *dev_Param;
	cuDoubleComplex *dev_Omiga;
	cuDoubleComplex *dev_AntWeight;		//复数方向图
	T *dev_AntFuction;				//归一化实数方向图
    cudaMalloc((void**)&dev_Param,      sizeof(double)*15);
    cudaMalloc((void**)&dev_Omiga,      sizeof(cuDoubleComplex)*ArrayNumRow * ArrayNumColumn);
    cudaMalloc((void**)&dev_AntWeight,	sizeof(cuDoubleComplex)*AntFuncAziNum * AntFuncEleNum);
    cudaMalloc((void**)&dev_AntFuction,	sizeof(T)*AntFuncAziNum * AntFuncEleNum);
    cudaMemset(dev_AntWeight,	0, AntFuncAziNum * AntFuncEleNum*sizeof(cuDoubleComplex));
    cudaMemset(dev_Param,	0, 15*sizeof(double));

	Param[0] = AntSpaceRow;		//行阵元间距
    Param[1] = AntSpaceColumn;	//列阵元间距
    Param[2] = AntAziBound;		//方向图方位向范围
    Param[3] = AntEleBound;	//方向图俯仰向范围
    Param[4] = AntFuncAziNum;	//方向图方位向点数
    Param[5] = AntFuncEleNum;	//方向图俯仰向点数
    Param[6] = ArrayNumRow;		//阵元行数	
    Param[7] = ArrayNumColumn;	//阵元列数		
    Param[8] = SimAntSpacePhaseAzi;		//方向图范围向间隔（弧度）
    Param[9] = SimAntSpacePhasePitch;	//方向图俯仰向间隔（弧度）
    Param[10] = Fc;				//载频
    Param[12] = ArrayRadius;	//圆半径

    cudaMemcpy(dev_Omiga,   Omiga,    ArrayNumRow * ArrayNumColumn*sizeof(cuDoubleComplex), cudaMemcpyHostToDevice);


    for(int idx=0 ; idx<Param[6]; idx++)
    {
		Param[11] = idx;		//行号
		cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
		CUDA_BoundaryCellSim(0, ThreadsPerBlock, BlockNum, dev_Param, (Complex*)dev_Omiga, (Complex*)dev_AntWeight);
	}
	//****21091008新增，加入天线位置输出******//
	{
		double Rx,Ry;
		double DeltaRow = 0,DeltaColumn = 0;
		if(ArrayNumRow%2 == 0)
		{
			DeltaRow = 0.5;
		}
		if(ArrayNumColumn%2 == 0)
		{
			DeltaColumn = 0.5;
		}
		for(int idx=0 ; idx<ArrayNumColumn; idx++)
		{
			for(int idy=0;idy<ArrayNumRow;idy++)
			{
				Rx = (idy - ArrayNumRow/2    + DeltaRow)	* AntSpaceRow;
				Ry = (idx - ArrayNumColumn/2 + DeltaColumn) * AntSpaceColumn;
				if(Rx*Rx + Ry*Ry > ArrayRadius*ArrayRadius)
					continue;
				AntPos[idx*ArrayNumRow+idy].x = Rx;
				AntPos[idx*ArrayNumRow+idy].y = Ry;
			}
		}
	}
	//****21091008新增，加入天线位置输出******//

    Param[5] = AntFuncAziNum;	//方向图俯仰向点数
    Param[6] = AntFuncEleNum;		//阵元行数	
    cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
	CUDA_ComplexAbs(0, ThreadsPerBlock, BlockNum, (Complex*)dev_AntWeight, dev_AntFuction);
    cudaMemcpy(SumAntennaFunction, dev_AntFuction,       AntFuncAziNum * AntFuncEleNum*sizeof(T), cudaMemcpyDeviceToHost);
#if NORMALIZE
	///***************归一化******************///
	int		MaxID[1];
	double	MaxData[1];
    GetMax(SumAntennaFunction,AntFuncAziNum * AntFuncEleNum,MaxData,MaxID);
    Param[0] = AntGain;
    Param[1] = *MaxData;
    cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
	CUDA_Normalize(0, ThreadsPerBlock, BlockNum, dev_Param, dev_AntFuction);	//归一化并取dB
    cudaMemcpy(SumAntennaFunction, dev_AntFuction,	AntFuncAziNum * AntFuncEleNum*sizeof(T), cudaMemcpyDeviceToHost);
	
	///***************归一化******************///
#endif

    cudaFree(dev_Param);
    cudaFree(dev_Omiga);
    cudaFree(dev_AntWeight);
    cudaFree(dev_AntFuction);
	//**********CUDA配置**************//
}

//圆周边界阵-子阵
template <class T>
void  ThreadClassAntennaGen::CicleArrayBoundaryArraySim(T *SumAntennaFunction,double AntGain,double ArrayRadius,int AntFuncAziNum,
					int AntFuncEleNum,int ArrayNumRow,int SubArrayNumRow,int ArrayNumColumn,
					int SubArrayNumColumn,Complex *Omiga,Complex *SubOmiga,double Fc,double AntSpaceRow,double SubArraySpcaeRow,
                    double AntSpaceColumn,double SubArraySpcaeColumn,double AntAziBound,double AntEleBound,double InitialePhase,
					Complex *AntPos,Complex *SubAntPos)


{
	double SimAntSpacePhaseAzi	 = AntAziBound/(AntFuncAziNum - 1);//仿真天线方向图的间距
    double SimAntSpacePhasePitch = AntEleBound/(AntFuncEleNum - 1);//仿真天线方向图的间距

    //**********CUDA配置**************//
    dim3 ThreadsPerBlock(512,1);
    dim3 BlockNum((AntFuncAziNum*AntFuncEleNum + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
    double Param[15];
    double *dev_Param;
	cuDoubleComplex *dev_Omiga;
	cuDoubleComplex *dev_AntWeight;		//复数方向图
	T *dev_AntFuction;				//归一化实数方向图
    cudaMalloc((void**)&dev_Param,      sizeof(double)*15);
    cudaMalloc((void**)&dev_Omiga,      sizeof(cuDoubleComplex)*ArrayNumRow * ArrayNumColumn);
    cudaMalloc((void**)&dev_AntWeight,	sizeof(cuDoubleComplex)*AntFuncAziNum * AntFuncEleNum);
    cudaMalloc((void**)&dev_AntFuction,	sizeof(T)*AntFuncAziNum * AntFuncEleNum);
    cudaMemset(dev_AntWeight,	0, AntFuncAziNum * AntFuncEleNum*sizeof(cuDoubleComplex));
    cudaMemset(dev_Param,	0, 15*sizeof(double));

	//***********子阵内阵元 - 矩形阵***************//
	Param[0] = AntSpaceRow;		//行阵元间距
    Param[1] = AntSpaceColumn;	//列阵元间距
    Param[2] = AntAziBound;		//方向图方位向范围
    Param[3] = AntEleBound;	//方向图俯仰向范围
    Param[4] = AntFuncAziNum;	//方向图方位向点数
    Param[5] = AntFuncEleNum;	//方向图俯仰向点数
    Param[6] = ArrayNumRow;		//阵元行数	
    Param[7] = ArrayNumColumn;	//阵元列数		
    Param[8] = SimAntSpacePhaseAzi;		//方向图范围向间隔（弧度）
    Param[9] = SimAntSpacePhasePitch;	//方向图俯仰向间隔（弧度）
    Param[10] = Fc;		//载频
    cudaMemcpy(dev_Omiga,   Omiga,    ArrayNumRow * ArrayNumColumn*sizeof(cuDoubleComplex), cudaMemcpyHostToDevice);

	
  
    for(int idx=0 ; idx<Param[6]; idx++)
    {
		Param[11] = idx;		//行号
		cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
		CUDA_RectAngArrayCell(0, ThreadsPerBlock, BlockNum, dev_Param, (Complex*)dev_Omiga, (Complex*)dev_AntWeight);
	}	
	//****21091008新增，加入天线位置输出******//
	{
		double DeltaRow = 0,DeltaColumn = 0;
		if(ArrayNumRow%2 == 0)
		{
			DeltaRow = 0.5;
		}
		if(ArrayNumColumn%2 == 0)
		{
			DeltaColumn = 0.5;
		}
		for(int idx=0 ; idx<ArrayNumColumn; idx++)
		{
			for(int idy=0;idy<ArrayNumRow;idy++)
			{
				AntPos[idx*ArrayNumRow+idy].x = (idy - ArrayNumRow/2    + DeltaRow)	* AntSpaceRow;
				AntPos[idx*ArrayNumRow+idy].y = (idx - ArrayNumColumn/2 + DeltaColumn) * AntSpaceColumn;
			}
		}
	}
	//***********子阵内阵元 - 矩形阵***************//

	
	//***********子阵间  - 圆周边界阵***************//
	cuDoubleComplex *dev_SubAntWeight;	//复数方向图
	cuDoubleComplex *dev_SubOmiga;		//复数方向图
    cudaMalloc((void**)&dev_SubOmiga,	 sizeof(cuDoubleComplex)*SubArrayNumRow* SubArrayNumColumn);
    cudaMalloc((void**)&dev_SubAntWeight,sizeof(cuDoubleComplex)*AntFuncAziNum * AntFuncEleNum);
    cudaMemset(dev_SubAntWeight,	0, AntFuncAziNum * AntFuncEleNum*sizeof(cuDoubleComplex));
	Param[0] = SubArraySpcaeRow;		//行子阵间距
    Param[1] = SubArraySpcaeColumn;		//列子阵间距
    Param[2] = AntAziBound;				//方向图方位向范围
    Param[3] = AntEleBound;			//方向图俯仰向范围
    Param[4] = AntFuncAziNum;			//方向图方位向点数
    Param[5] = AntFuncEleNum;			//方向图俯仰向点数
    Param[6] = SubArrayNumRow;			//子阵行数	
    Param[7] = SubArrayNumColumn;		//子阵列数		
    Param[8] = SimAntSpacePhaseAzi;		//方向图范围向间隔（弧度）
    Param[9] = SimAntSpacePhasePitch;	//方向图俯仰向间隔（弧度）
    Param[10] = Fc;		//载频
    Param[12] = ArrayRadius;		//载频
    cudaMemcpy(dev_SubOmiga,   SubOmiga,    SubArrayNumRow * SubArrayNumColumn*sizeof(cuDoubleComplex), cudaMemcpyHostToDevice);

	
	cuDoubleComplex* dev_SubAntPos;		//天线位置
    cudaMalloc((void**)&dev_SubAntPos,sizeof(cuDoubleComplex)*SubArrayNumColumn*SubArrayNumRow );
    cudaMemset(dev_SubAntPos,	0, SubArrayNumColumn*SubArrayNumRow*sizeof(cuDoubleComplex));


    for(int idx=0 ; idx<Param[6]; idx++)
    {
		Param[11] = idx;		//行号
		cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
		CUDA_BoundaryCellSim(0, ThreadsPerBlock, BlockNum, dev_Param, (Complex*)dev_SubOmiga, (Complex*)dev_SubAntWeight);
	}
	//****21091008新增，加入天线位置输出******//
	{
		double Rx,Ry;
		double DeltaRow = 0,DeltaColumn = 0;
		if(ArrayNumRow%2 == 0)
		{
			DeltaRow = 0.5;
		}
		if(ArrayNumColumn%2 == 0)
		{
			DeltaColumn = 0.5;
		}
		for(int idx=0 ; idx<SubArrayNumColumn; idx++)
		{
			for(int idy=0;idy<SubArrayNumRow;idy++)
			{
				Rx = (idy - SubArrayNumRow/2    + DeltaRow)	* SubArraySpcaeRow;
				Ry = (idx - SubArrayNumColumn/2 + DeltaColumn)* SubArraySpcaeColumn;
				if(Rx*Rx + Ry*Ry > ArrayRadius*ArrayRadius)
					continue;
				SubAntPos[idx*SubArrayNumRow+idy].x = Rx;
				SubAntPos[idx*SubArrayNumRow+idy].y = Ry;
			}
		}
	}
	//****21091008新增，加入天线位置输出******//
	//***********子阵间  - 圆周边界阵***************//

	
	//***********合成方向图***************//
    Param[5] = AntFuncAziNum;	//方向图俯仰向点数
    Param[6] = AntFuncEleNum;		//阵元行数	
    cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
	CUDA_ComplexMutiple(0, ThreadsPerBlock, BlockNum, (Complex*)dev_AntWeight, (Complex*)dev_SubAntWeight, (Complex*)dev_AntWeight, 1);//合成方向图
	CUDA_ComplexAbs(0, ThreadsPerBlock, BlockNum, (Complex*)dev_AntWeight, dev_AntFuction);	//取模
    cudaMemcpy(SumAntennaFunction, dev_AntFuction,AntFuncAziNum * AntFuncEleNum*sizeof(T), cudaMemcpyDeviceToHost);
	//***********合成方向图***************//
	
#if NORMALIZE
	///***************归一化******************///
	int		MaxID[1];
	double	MaxData[1];
    GetMax(SumAntennaFunction,AntFuncAziNum * AntFuncEleNum,MaxData,MaxID);
    Param[0] = AntGain;
    Param[1] = *MaxData;
    cudaMemcpy(dev_Param,   Param,    15*sizeof(double), cudaMemcpyHostToDevice);
	CUDA_Normalize(0, ThreadsPerBlock, BlockNum, dev_Param, dev_AntFuction);	//归一化并取dB
    cudaMemcpy(SumAntennaFunction, dev_AntFuction,	AntFuncAziNum * AntFuncEleNum*sizeof(T), cudaMemcpyDeviceToHost);
	///***************归一化******************///
#endif

    cudaFree(dev_Param);
    cudaFree(dev_Omiga);
    cudaFree(dev_AntWeight);
    cudaFree(dev_AntFuction);
	//**********CUDA配置**************//
}

double ThreadClassAntennaGen::fsolve(double val, double starting)
{
    double x = starting;
    double eps = 1e-6;
    int iterMax = 10000;
    for (int i = 0; i < iterMax; i++)
    {
        double fx = sin(x) / x - val;//radian
        double dfx = cos(x) / x - sin(x) / x / x;
        x -= (fx / dfx);
        fx = sin(x) / x - val;
        if (abs(fx) < eps || i == iterMax)
            return x;
    }
    return NAN;
}

//sinc
void ThreadClassAntennaGen::SincFunCal(ANTPARAM *AntParam, int GridNum, double AngleBound, double AngSampSpace, float *BeamWei, double AntenMainGain, double AntenGain)
{
    double AngGrid, SincX;
    for (int idx = 0; idx < GridNum; idx++)
    {
        AngGrid = -AngleBound + idx*AngSampSpace;
        SincX = PI * 0.886 * AngGrid / (AntParam->Main3dBlobeWidth[0]);
        if (AngGrid == 0)
            BeamWei[idx] = (float)AntenGain*AntenMainGain;
        else
            BeamWei[idx] = (float)AntenGain*AntenMainGain*abs(sin(SincX) / SincX);
    }
}

//拟合sinc
void ThreadClassAntennaGen::SincPolyFunCal(ANTPARAM *AntParam, int GridNum, double AngleBound, double AngSampSpace, float *BeamWei, double AntenMainGain, double AntenMainZeroGain, double AntenSideGain, double AntenMeanSideGain, double AntenGain, double *SidelobeZeroPos)
{
    double Angle3dBCoef = fsolve(0.5, 1.89);
    double AngleMainZeroCoef = fsolve(AntenMainZeroGain / (1.0 / 2) / AntenMainGain, PI);
    double AngleLobeZeroCoef = fsolve(AntenMainZeroGain / AntenSideGain, PI);

    double AngGrid, Theta, Alpha, SincX;
    for (int idx = 0; idx < GridNum; idx++)
    {
        AngGrid = -AngleBound + idx*AngSampSpace;
        Theta = AngGrid;
        Alpha = 2 * Angle3dBCoef*Theta / AntParam->Main3dBlobeWidth[1];     // x = 2*theta*coef/theta_0.5
        if (Alpha == 0)
            BeamWei[idx] = float(AntenGain);
        else
        {
            SincX = 2 * PI*AngGrid / (AntParam->MainlobeZeroPos[1] * 2.0);
            BeamWei[idx] = float(abs(sin(SincX) / SincX));         //零功率点
        }
        if (abs(Theta) <= AntParam->Main3dBlobeWidth[1] / 2.0)   // 方位向主瓣3dB内计算
        {
            Alpha = 2 * Angle3dBCoef*Theta / AntParam->Main3dBlobeWidth[1];			// x = 2*theta*coef/theta_0.5
            if (Alpha < -INF || Alpha > INF)
                BeamWei[idx] = float((AntenGain*sin(Alpha) / Alpha));
            else
                BeamWei[idx] = float(AntenGain);
        }
        if (abs(Theta) > AntParam->Main3dBlobeWidth[1] / 2.0 && abs(Theta) <= AntParam->MainlobeZeroPos[1])//主瓣3dB至主瓣零点计算
        {
            Alpha = 2 * AngleMainZeroCoef*(abs(Theta) - AntParam->Main3dBlobeWidth[1] / 2.0)
                / (2 * AntParam->MainlobeZeroPos[1] - AntParam->Main3dBlobeWidth[1]);
            BeamWei[idx] = float(0.5*(AntenGain)*sin(Alpha) / Alpha);
        }
        if (abs(Theta) > AntParam->MainlobeZeroPos[1] && abs(Theta) <= SidelobeZeroPos[1])	// 第一旁瓣计算
        {
            Alpha = AngleLobeZeroCoef*(abs(Theta) - AntParam->SidelobeMaxPos[1]) / (AntParam->SidelobeMaxPos[1] - AntParam->MainlobeZeroPos[1]);
            if (Alpha == 0)
                BeamWei[idx] = float((AntenSideGain*AntenGain));
            else
                BeamWei[idx] = float((AntenSideGain*AntenGain)*sin(Alpha) / Alpha);
        }
        if (abs(Theta) > SidelobeZeroPos[1])	//平均旁瓣计算
        {
            BeamWei[idx] = float((AntenMeanSideGain*AntenGain));
        }
    }
}

//高斯
void ThreadClassAntennaGen::GaussFunCal(ANTPARAM *AntParam, int GridNum, double AngleBound, double AngSampSpace, float *BeamWei, double AntenMainGain, double AntenMainZeroGain, double AntenSideGain, double AntenMeanSideGain, double AntenGain, double *SidelobeZeroPos)
{
    double Angle3dBCoef = sqrt(-log(0.707));
    double AngleMainZeroCoef = sqrt(-log((AntenMainZeroGain) / 0.707 / (AntenMainGain)));
    double AngleLobeZeroCoef = sqrt(-log(AntenMainZeroGain / AntenSideGain));

    double AngGrid, Theta, Alpha, SincX;
    for (int idx = 0; idx < GridNum; idx++)
    {
        AngGrid = -AngleBound + idx*AngSampSpace;
        Theta = AngGrid;
        Alpha = 2 * Angle3dBCoef*Theta / AntParam->Main3dBlobeWidth[1];     // x = 2*theta*coef/theta_0.5
        if (Alpha == 0)
            BeamWei[idx] = float(AntenGain);
        else
        {
            SincX = 2 * PI*AngGrid / (AntParam->MainlobeZeroPos[1] * 2.0);
            BeamWei[idx] = float(abs(sin(SincX) / SincX));         //零功率点
        }
        if (abs(Theta) <= AntParam->Main3dBlobeWidth[1] / 2.0)   // 方位向主瓣3dB内计算
        {
            Alpha = 2 * Angle3dBCoef*Theta / AntParam->Main3dBlobeWidth[1];			// x = 2*theta*coef/theta_0.5
            BeamWei[idx] = float(AntenGain*exp(-Alpha*Alpha));
        }
        if (abs(Theta) > AntParam->Main3dBlobeWidth[1] / 2.0 && abs(Theta) <= AntParam->MainlobeZeroPos[1])//主瓣3dB至主瓣零点计算
        {
            Alpha = AngleMainZeroCoef*(abs(Theta) - AntParam->Main3dBlobeWidth[1] / 2.0)
                / (AntParam->MainlobeZeroPos[1] - AntParam->Main3dBlobeWidth[1]/2.0);
            BeamWei[idx] = float(0.707*(AntenGain)*exp(-Alpha*Alpha));
        }
        if (abs(Theta) > AntParam->MainlobeZeroPos[1] && abs(Theta) <= SidelobeZeroPos[1])	// 第一旁瓣计算
        {
            Alpha = AngleLobeZeroCoef*(abs(Theta) - AntParam->SidelobeMaxPos[1]) / (AntParam->SidelobeMaxPos[1] - AntParam->MainlobeZeroPos[1]);
            BeamWei[idx] = float((AntenSideGain)*exp(-Alpha*Alpha));
        }
    }
}

//cos
void ThreadClassAntennaGen::CosFunCal(ANTPARAM *AntParam, int GridNum, double AngleBound, double AngSampSpace, float *BeamWei, double AntenMainGain, double AntenMainZeroGain, double AntenSideGain, double AntenMeanSideGain, double AntenGain, double *SidelobeZeroPos)
{
    double Angle3dBCoef = (acos(0.5));                      // cos(x) = 0.5 的解，半功率点系数
    double AngleMainZeroCoef = acos(AntenMainZeroGain / (1 / 2.0) / AntenMainGain);      // 主瓣0点位置
    double AngleLobeZeroCoef = acos(AntenMainZeroGain / AntenSideGain);            // 副瓣零点

    double AngGrid, Theta, Alpha, SincX;
    for (int idx = 0; idx < GridNum; idx++)
    {
        AngGrid = -AngleBound + idx*AngSampSpace;
        Theta = AngGrid;
        Alpha = 2 * Angle3dBCoef*Theta / AntParam->Main3dBlobeWidth[1];     // x = 2*theta*coef/theta_0.5
        if (Alpha == 0)
            BeamWei[idx] = float(AntenGain);
        else
        {
            SincX = 2 * PI*AngGrid / (AntParam->MainlobeZeroPos[1] * 2.0);
            BeamWei[idx] = float(abs(sin(SincX) / SincX));         //零功率点
        }
        if (abs(Theta) <= AntParam->Main3dBlobeWidth[1] / 2.0)   // 方位向主瓣3dB内计算
        {
            Alpha = 2 * Angle3dBCoef*Theta / AntParam->Main3dBlobeWidth[1];			// x = 2*theta*coef/theta_0.5
            BeamWei[idx] = float(AntenGain*cos(Alpha));
        }
        if (abs(Theta) > AntParam->Main3dBlobeWidth[1] / 2.0 && abs(Theta) <= AntParam->MainlobeZeroPos[1])//主瓣3dB至主瓣零点计算
        {
            Alpha = 2 * AngleMainZeroCoef*(abs(Theta) - AntParam->Main3dBlobeWidth[1] / 2.0)
                / (2 * AntParam->MainlobeZeroPos[1] - AntParam->Main3dBlobeWidth[1]);
            BeamWei[idx] = float(0.5*(AntenGain)*cos(Alpha));
        }
        if (abs(Theta) > AntParam->MainlobeZeroPos[1] && abs(Theta) <= SidelobeZeroPos[1])	// 第一旁瓣计算
        {
            Alpha = AngleLobeZeroCoef*(abs(Theta) - AntParam->SidelobeMaxPos[1]) / (AntParam->SidelobeMaxPos[1] - AntParam->MainlobeZeroPos[1]);
            BeamWei[idx] = float((AntenSideGain*AntenGain)*cos(Alpha));
        }
        if (abs(Theta) > SidelobeZeroPos[1])	//平均旁瓣计算
        {
            BeamWei[idx] = float((AntenMeanSideGain*AntenGain));
        }
    }
}

//余割平方
void ThreadClassAntennaGen::CscFunCal(ANTPARAM *AntParam, int GridNum, double AngleBound, double AngSampSpace, float *BeamWei, double AntenMainGain, double AntenMainZeroGain, double AntenSideGain, double AntenMeanSideGain, double AntenGain, double *SidelobeZeroPos)
{
    double main3dBLobeWidth = AntParam->angleMax * PI / 180;

    double Alpha;
    for (int idx = 0; idx < GridNum; idx++)
    {
        Alpha = -AngleBound + idx*AngSampSpace;

        if (abs(Alpha) > main3dBLobeWidth / 2)
        {
            BeamWei[idx] = float(pow(10, AntParam->cscOutsider / 20.0));
        }
        BeamWei[idx] = float(pow(1 / sin(Alpha + AntParam->cscTheta*PI / 180 - main3dBLobeWidth) / (1 / sin(main3dBLobeWidth)), 2)*AntenGain);
    }
}
void ThreadClassAntennaGen::PatternOffset(double AziDivAngle, double PitDivAngle, int AzimuGridNum, int PitchGridNum, ANTPARAM *AntParam, float *AntDivGain)
{
    int	AziDivNum = floor(AziDivAngle / AntParam->AngSampSpace[0] + 0.5);			//方位偏移采样点数
    int	PitchDivNum = floor(PitDivAngle / AntParam->AngSampSpace[1] + 0.5);			//俯仰偏移采样点数
    int idx, idy, idz;

    float *AntGainTemp = (float*)malloc(sizeof(float)*AzimuGridNum*PitchGridNum);
    memset(AntGainTemp, 0, sizeof(float)*AzimuGridNum*PitchGridNum);

    //方位偏移
    if (AziDivNum > 0)
    {
        for (idx = 0; idx < AzimuGridNum; idx++)
        {
            if (idx < AziDivNum)
            {
                for (idy = 0; idy < PitchGridNum; idy++)
                {
                    AntGainTemp[idy*AzimuGridNum + idx] = AntParam->SumAntennaFunction[idy*AzimuGridNum + (AzimuGridNum - AziDivNum + idx)];
                }
            }
            else
            {
                for (idy = 0; idy < PitchGridNum; idy++)
                {
                    AntGainTemp[idy*AzimuGridNum + idx] = AntParam->SumAntennaFunction[idy*AzimuGridNum + (idx - AziDivNum)];
                }
            }
        }
    }
    else if (AziDivNum == 0)
        memcpy(AntGainTemp, AntParam->SumAntennaFunction, sizeof(float)*AzimuGridNum*PitchGridNum);
    else
    {
        for (idx = 0; idx < AzimuGridNum; idx++)
        {
            if (idx < AzimuGridNum + AziDivNum)
            {
                for (idy = 0; idy < PitchGridNum; idy++)
                {
                    AntGainTemp[idy*AzimuGridNum + idx] = AntParam->SumAntennaFunction[idy*AzimuGridNum + (idx - AziDivNum)];
                }
            }
            else
            {
                for (idy = 0; idy < PitchGridNum; idy++)
                {
                    AntGainTemp[idy*AzimuGridNum + idx] = AntParam->SumAntennaFunction[idy*AzimuGridNum + (idx - AzimuGridNum - AziDivNum)];
                }
            }
        }
    }

    //俯仰偏移
    if (PitchDivNum > 0)
    {
        for (idy = 0; idy < PitchGridNum; idy++)
        {
            if (idy < PitchDivNum)
            {
                for (idx = 0; idx < AzimuGridNum; idx++)
                {
                    AntDivGain[idy*AzimuGridNum + idx] = AntGainTemp[(PitchGridNum - PitchDivNum + idy)*AzimuGridNum + idx];
                }
            }
            else
            {
                for (idx = 0; idx < AzimuGridNum; idx++)
                {
                    AntDivGain[idy*AzimuGridNum + idx] = AntGainTemp[(idy - PitchDivNum)*AzimuGridNum + idx];
                }
            }
        }
    }
    else if (PitchDivNum == 0)
        memcpy(AntDivGain, AntGainTemp, sizeof(float)*AzimuGridNum*PitchGridNum);
    else
    {
        for (idy = 0; idy < PitchGridNum; idy++)
        {
            if (idy < PitchGridNum + PitchDivNum)
            {
                for (idx = 0; idx < AzimuGridNum; idx++)
                {
                    AntDivGain[idy*AzimuGridNum + idx] = AntGainTemp[(idy - PitchDivNum)*AzimuGridNum + idx];
                }
            }
            else
            {
                for (idx = 0; idx < AzimuGridNum; idx++)
                {
                    AntDivGain[idy*AzimuGridNum + idx] = AntGainTemp[(idy - PitchGridNum - PitchDivNum)*AzimuGridNum + idx];
                }
            }
        }
    }
    free(AntGainTemp);
}

/************************************************************************
* @brief	//初始化天线方向图
* z
* @param[out]	AntennaPattern:		输出，天线方向图
* @param[in]	AntType:			天线类型
* @param[in]	AntParam:			天线参数
************************************************************************/
void ThreadClassAntennaGen::SinglePattern(ANTPARAM *AntParam)
{
    int	AzimuGridNum = (int)(2 * AntParam->AngleBound[0] / AntParam->AngSampSpace[0] + 0.5) + 1;	//方位向点数
    int	PitchGridNum = (int)(2 * AntParam->AngleBound[1] / AntParam->AngSampSpace[1] + 0.5) + 1;	//俯仰向点数

    double AntenGain = pow(10, AntParam->AntenMainAndSideGain[0] / 20.0);			//天线增益
    float *PitchBeam = (float *)malloc(PitchGridNum*sizeof(float));				//俯仰向方向图向量
    memset(PitchBeam, 0, PitchGridNum*sizeof(float));
    float *AzimuBeam = (float *)malloc(AzimuGridNum*sizeof(float));				//方位向方向图向量
    memset(AzimuBeam, 0, AzimuGridNum*sizeof(float));

    double SidelobeZeroPos[2];				// 旁瓣零点
	double AntenMainGain = AntenGain;			// 主瓣最大增益 dB，归一化天线增益
    double AntenMainZeroGain = pow(10, (AntParam->AntenMainAndSideGain[1]) / 20);        // 主瓣零点增益 dB
    double AntenSideGain = pow(10, (AntParam->AntenMainAndSideGain[2]) / 20);     	// 第一旁瓣中心增益 dB
    double AntenMeanSideGain = pow(10, (AntParam->AntenMainAndSideGain[3]) / 20);        // 平均旁瓣增益 dB
    SidelobeZeroPos[0] = AntParam->MainlobeZeroPos[0] + (AntParam->SidelobeMaxPos[0] - AntParam->MainlobeZeroPos[0]) * 2;
    SidelobeZeroPos[1] = AntParam->MainlobeZeroPos[1] + (AntParam->SidelobeMaxPos[1] - AntParam->MainlobeZeroPos[1]) * 2;

    if (0 == AntParam->AntennaMode)		//Sinc
    {
		SincFunCal(AntParam, AzimuGridNum, AntParam->AngleBound[0], AntParam->AngSampSpace[0], AzimuBeam, AntenMainGain, 1);
        SincFunCal(AntParam, PitchGridNum, AntParam->AngleBound[1], AntParam->AngSampSpace[1], PitchBeam, AntenMainGain, AntenGain);
    }
    else if (1 == AntParam->AntennaMode)		//拟合Sinc加权
    {
		SincPolyFunCal(AntParam, AzimuGridNum, AntParam->AngleBound[0], AntParam->AngSampSpace[0], AzimuBeam, AntenMainGain, AntenMainZeroGain, AntenSideGain, AntenMeanSideGain, 1, SidelobeZeroPos);
        SincPolyFunCal(AntParam, PitchGridNum, AntParam->AngleBound[1], AntParam->AngSampSpace[1], PitchBeam, AntenMainGain, AntenMainZeroGain, AntenSideGain, AntenMeanSideGain, AntenGain, SidelobeZeroPos);
    }
    else if (2 == AntParam->AntennaMode)		//高斯
    {
		GaussFunCal(AntParam, AzimuGridNum, AntParam->AngleBound[0], AntParam->AngSampSpace[0], AzimuBeam, AntenMainGain, AntenMainZeroGain, AntenSideGain, AntenMeanSideGain, AntenGain, SidelobeZeroPos);
        GaussFunCal(AntParam, PitchGridNum, AntParam->AngleBound[1], AntParam->AngSampSpace[1], PitchBeam, AntenMainGain, AntenMainZeroGain, AntenSideGain, AntenMeanSideGain, AntenGain, SidelobeZeroPos);
    }
    else if (3 == AntParam->AntennaMode)		//cos
    {
		CosFunCal(AntParam, AzimuGridNum, AntParam->AngleBound[0], AntParam->AngSampSpace[0], AzimuBeam, AntenMainGain, AntenMainZeroGain, AntenSideGain, AntenMeanSideGain, 1, SidelobeZeroPos);
        CosFunCal(AntParam, PitchGridNum, AntParam->AngleBound[1], AntParam->AngSampSpace[1], PitchBeam, AntenMainGain, AntenMainZeroGain, AntenSideGain, AntenMeanSideGain, AntenGain, SidelobeZeroPos);
    }
    else   //余割平方
    {
		GaussFunCal(AntParam, AzimuGridNum, AntParam->AngleBound[0], AntParam->AngSampSpace[0], AzimuBeam, AntenMainGain, AntenMainZeroGain, AntenSideGain, AntenMeanSideGain, 1, SidelobeZeroPos);
        CscFunCal(AntParam, PitchGridNum, AntParam->AngleBound[1], AntParam->AngSampSpace[1], PitchBeam, AntenMainGain, AntenMainZeroGain, AntenSideGain, AntenMeanSideGain, AntenGain, SidelobeZeroPos);
    }

    for (int idx = 0; idx < AzimuGridNum; idx++)
    {
        for (int idy = 0; idy < PitchGridNum; idy++)
        {
            AntParam->SumAntennaFunction[idy*AzimuGridNum + idx] = PitchBeam[idy] * AzimuBeam[idx];
        }
    }

    float *pSumAntennaFunction = (float *)malloc(sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum);

    //FILE *fp = nullptr;
    //fp = fopen("D:\\yjy\\files\\HQ\\HQRD\\CalcutePool\\data\\EchoANT","wb");
    //memcpy(pSumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum);
    //fwrite(pSumAntennaFunction, 1, sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum, fp);
    //fclose(fp);

    free(PitchBeam);
    free(AzimuBeam);
}

void ThreadClassAntennaGen::DiffPattern(ANTPARAM *AntParam, double AziDelta, double PitchDelta)
{
    SinglePattern(AntParam);

    int	AzimuGridNum = (int)(2 * AntParam->AngleBound[0] / AntParam->AngSampSpace[0] + 0.5) + 1;	//方位向点数
    int	PitchGridNum = (int)(2 * AntParam->AngleBound[1] / AntParam->AngSampSpace[1] + 0.5) + 1;	//俯仰向点数

    float *AntGain1 = (float*)malloc(sizeof(float)*AzimuGridNum*PitchGridNum);
    float *AntGain2 = (float*)malloc(sizeof(float)*AzimuGridNum*PitchGridNum);
    float *AntGain3 = (float*)malloc(sizeof(float)*AzimuGridNum*PitchGridNum);
    float *AntGain4 = (float*)malloc(sizeof(float)*AzimuGridNum*PitchGridNum);
    memset(AntGain1, 0, sizeof(float)*AzimuGridNum*PitchGridNum);
    memset(AntGain2, 0, sizeof(float)*AzimuGridNum*PitchGridNum);
    memset(AntGain3, 0, sizeof(float)*AzimuGridNum*PitchGridNum);
    memset(AntGain4, 0, sizeof(float)*AzimuGridNum*PitchGridNum);

    PatternOffset(-AziDelta, -PitchDelta, AzimuGridNum, PitchGridNum, AntParam, AntGain1);
    PatternOffset(-AziDelta, PitchDelta, AzimuGridNum, PitchGridNum, AntParam, AntGain2);
    PatternOffset(AziDelta, -PitchDelta, AzimuGridNum, PitchGridNum, AntParam, AntGain3);
    PatternOffset(AziDelta, PitchDelta, AzimuGridNum, PitchGridNum, AntParam, AntGain4);

    for (int idx = 0; idx < AzimuGridNum; idx++)
    {
        for (int idy = 0; idy < PitchGridNum; idy++)
        {
            AntParam->SumAntennaFunction[idy*AzimuGridNum + idx] = AntGain1[idy*AzimuGridNum + idx] + AntGain2[idy*AzimuGridNum + idx] +
                AntGain3[idy*AzimuGridNum + idx] + AntGain4[idy*AzimuGridNum + idx];     //和
            AntParam->AziSubAntennaFunction[idy*AzimuGridNum + idx] = -AntGain1[idy*AzimuGridNum + idx] - AntGain2[idy*AzimuGridNum + idx] +
                AntGain3[idy*AzimuGridNum + idx] + AntGain4[idy*AzimuGridNum + idx];     //方位差
            AntParam->PitSubAntennaFunction[idy*AzimuGridNum + idx] = -AntGain1[idy*AzimuGridNum + idx] + AntGain2[idy*AzimuGridNum + idx] -
                AntGain3[idy*AzimuGridNum + idx] + AntGain4[idy*AzimuGridNum + idx];     //俯仰差
        }
    }

	float *pSumAntennaFunction = (float *)malloc(sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum);

	//FILE *fp = nullptr;
	//fp = fopen("..\\..\\..\\data\\EchoANT","wb");
	//memcpy(pSumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum);
	//fwrite(pSumAntennaFunction, 1, sizeof(float) * AntParam->AntFuncAziNum * AntParam->AntFuncEleNum, fp);
	//fclose(fp);

	int AziStartID, AziEndID, EleStartID, EleEndID;
	int AziOverlapLen = (int)(AntParam->AziOverlap / AntParam->AngSampSpace[0] / 2 + 0.5);
	int EleOverlapLen = (int)(AntParam->EleOverlap / AntParam->AngSampSpace[1] / 2 + 0.5);
	AziStartID	= int(-AziOverlapLen + AntParam->AntFuncAziNum / 2.0 + 0.5);
	AziEndID = int(AziOverlapLen + AntParam->AntFuncAziNum / 2.0 + 0.5);
	EleStartID = int(-EleOverlapLen + AntParam->AntFuncEleNum / 2.0 + 0.5);
	EleEndID = int(EleOverlapLen + AntParam->AntFuncEleNum / 2.0 + 0.5);

	float m1, m2, m3, m4;
	m1 = AntParam->AziSubAntennaFunction[(int)((PitchGridNum / 2 - 1) * AzimuGridNum + AziStartID)] / AntParam->SumAntennaFunction[(int)((PitchGridNum / 2 - 1) * AzimuGridNum + AziStartID)];
	m2 = AntParam->AziSubAntennaFunction[(int)((PitchGridNum / 2 - 1) * AzimuGridNum + AziEndID)] / AntParam->SumAntennaFunction[(int)((PitchGridNum / 2 - 1) * AzimuGridNum + AziEndID)];
	m3 = m2 - m1;
	m4 = 2 * AziOverlapLen*AntParam->AngSampSpace[0];		//方位和差斜率比值
	AntParam->AziSlope = m3 / m4;

	float n1, n2, n3, n4;
	n1 = AntParam->PitSubAntennaFunction[(int)((EleStartID - 1) * AzimuGridNum + AzimuGridNum / 2)] / AntParam->SumAntennaFunction[(int)((EleStartID - 1) * AzimuGridNum + AzimuGridNum / 2)];
	n2 = AntParam->PitSubAntennaFunction[(int)((EleEndID - 1) * AzimuGridNum + AzimuGridNum / 2)] / AntParam->SumAntennaFunction[(int)((EleEndID - 1) * AzimuGridNum + AzimuGridNum / 2)];
	n3 = n2 - n1;
	n4 = 2 * EleOverlapLen*AntParam->AngSampSpace[1];
	AntParam->EleSlope = n3 / n4;


    free(AntGain1);
    free(AntGain2);
    free(AntGain3);
    free(AntGain4);
}
//生产天线方向图主函数
void ThreadClassAntennaGen::AntennaSimRe(RADAR *RadarParam, ANTPARAM *AntParam, SimStruct *SimData)
{
	int idx, idy;
    if (AntParam->AntennaType != 0)
    {
        for (idx = 0; idx<AntParam->ArrayNumColumn; idx++)
        {
            for (idy = 0; idy<AntParam->ArrayNumRow; idy++)
            {
                AntParam->Omiga[idy + AntParam->ArrayNumRow*idx].x = 1;
                AntParam->Omiga[idy + AntParam->ArrayNumRow*idx].y = 0;
            }
        }
        for (idx = 0; idx<AntParam->SubArrayNumColumn; idx++)
        {
            for (idy = 0; idy<AntParam->SubArrayNumRow; idy++)
            {
                AntParam->SubOmiga[idy + AntParam->SubArrayNumRow*idx].x = 1;
                AntParam->SubOmiga[idy + AntParam->SubArrayNumRow*idx].y = 0;
            }
        }
        switch (AntParam->AntennaType)
        {
        case Line:		//线阵
            if (ArryElement == AntParam->SubAntType)	//阵元
            {
                //-----------------------------计算-------------------------%
                AntParam->AntNum = AntParam->ArrayNumRow;
                LineArrayCellSim(AntParam->SumAntennaFunction, AntParam->AntGain, AntParam->ArrayNumRow, AntParam->AntFuncAziNum,
                    AntParam->AntFuncEleNum, AntParam->Omiga, RadarParam->Fc, AntParam->AntSpaceRow,
                    AntParam->AntAziBound, AntParam->AntEleBound, AntParam->InitialePhase, AntParam->AntPos);
            }
            else if (SubArray == AntParam->SubAntType)//子阵
            {
                //-----------------------------计算-------------------------%
                AntParam->AntNum = AntParam->ArrayNumRow;
                AntParam->SubAntNum = AntParam->SubArrayNumRow;
                LineArrayArraySim(AntParam->SumAntennaFunction, AntParam->AntGain, AntParam->ArrayNumRow, AntParam->SubArrayNumRow,
                    AntParam->AntFuncAziNum, AntParam->AntFuncEleNum, AntParam->Omiga, AntParam->SubOmiga, RadarParam->Fc,
                    AntParam->AntSpaceRow, AntParam->AntAziBound, AntParam->AntEleBound, AntParam->SubArraySpcaeRow,
                    AntParam->InitialePhase, AntParam->AntPos, AntParam->SubAntPos);
            }
            else
            {
                //printf("线阵,输入子阵类型错误！！！");
            }
            break;
        case RectAngle:
            if (ArryElement == AntParam->SubAntType)	//阵元
            {
                AntParam->AntNum = AntParam->ArrayNumRow*AntParam->ArrayNumColumn;
                RectAngArrayCellSim(AntParam->SumAntennaFunction, AntParam->AntGain, AntParam->AntFuncAziNum, AntParam->AntFuncEleNum,
                    AntParam->ArrayNumRow, AntParam->ArrayNumColumn, AntParam->Omiga, RadarParam->Fc,
                    AntParam->AntSpaceRow, AntParam->AntSpaceColumn, AntParam->AntAziBound, AntParam->AntEleBound,
                    AntParam->InitialePhase, AntParam->AntPos);
            }
            else if (SubArray == AntParam->SubAntType)//子阵
            {
                //-----------------------------计算-------------------------%*/
                AntParam->AntNum = AntParam->ArrayNumRow	  *AntParam->ArrayNumColumn;
                AntParam->SubAntNum = AntParam->SubArrayNumRow*AntParam->SubArrayNumColumn;
                RectAngArrayArraySim(AntParam->SumAntennaFunction, AntParam->AntGain, AntParam->AntFuncAziNum, AntParam->AntFuncEleNum,
                    AntParam->ArrayNumRow, AntParam->SubArrayNumRow, AntParam->ArrayNumColumn, AntParam->SubArrayNumColumn, AntParam->Omiga,
                    AntParam->SubOmiga, RadarParam->Fc, AntParam->AntSpaceRow, AntParam->SubArraySpcaeRow, AntParam->AntSpaceColumn,
                    AntParam->SubArraySpcaeColumn, AntParam->AntAziBound, AntParam->AntEleBound, AntParam->InitialePhase, AntParam->AntPos, AntParam->SubAntPos);
            }
            else
            {

            }
            //DoulbeArrayToDisk((double*)AntParam->SumAntennaFunction,"D:\\SumAntennaFunction",201*201);
            break;
        case CircleBound:	//圆周边界阵列
            if (ArryElement == AntParam->SubAntType)	//阵元
            {
                if (AntParam->ArrayNumRow*AntParam->AntSpaceRow > AntParam->ArrayNumColumn*AntParam->AntSpaceColumn)
                    AntParam->ArrayRadius = AntParam->ArrayNumColumn*AntParam->AntSpaceColumn / 2.0;
                else
                    AntParam->ArrayRadius = AntParam->ArrayNumRow*AntParam->AntSpaceRow / 2.0;
                AntParam->AntNum = AntParam->ArrayNumRow	  *AntParam->ArrayNumColumn;
                //-----------------------------计算-------------------------%*/
                CicleArrayBoundaryCellSim(AntParam->SumAntennaFunction, AntParam->AntGain, AntParam->ArrayRadius, AntParam->AntFuncAziNum,
                    AntParam->AntFuncEleNum, AntParam->ArrayNumRow, AntParam->ArrayNumColumn, AntParam->Omiga, RadarParam->Fc,
                    AntParam->AntSpaceRow, AntParam->AntSpaceColumn, AntParam->AntAziBound, AntParam->AntEleBound, AntParam->InitialePhase, AntParam->AntPos);
            }
            else if (SubArray == AntParam->SubAntType)//子阵
            {
                if (AntParam->SubArrayNumRow*AntParam->SubArraySpcaeRow > AntParam->SubArrayNumColumn*AntParam->SubArraySpcaeColumn)
                    AntParam->ArrayRadius = AntParam->SubArrayNumColumn*AntParam->SubArraySpcaeColumn / 2.0;
                else
                    AntParam->ArrayRadius = AntParam->SubArrayNumRow*AntParam->SubArraySpcaeRow / 2.0;
                AntParam->AntNum = AntParam->ArrayNumRow	  *AntParam->ArrayNumColumn;
                AntParam->SubAntNum = AntParam->SubArrayNumRow*AntParam->SubArrayNumColumn;
                //-----------------------------计算-------------------------%
                CicleArrayBoundaryArraySim(AntParam->SumAntennaFunction, AntParam->AntGain, AntParam->ArrayRadius, AntParam->AntFuncAziNum,
                    AntParam->AntFuncEleNum, AntParam->ArrayNumRow, AntParam->SubArrayNumRow, AntParam->ArrayNumColumn,
                    AntParam->SubArrayNumColumn, AntParam->Omiga, AntParam->SubOmiga, RadarParam->Fc, AntParam->AntSpaceRow, AntParam->SubArraySpcaeRow,
                    AntParam->AntSpaceColumn, AntParam->SubArraySpcaeColumn, AntParam->AntAziBound, AntParam->AntEleBound,
                    AntParam->InitialePhase, AntParam->AntPos, AntParam->SubAntPos);
            }
            else
            {

            }
            break;
        default:break;
            //printf("输入阵列类型错误！！！");
        }
    }
    else
    {
        double AziDelta = AntParam->OverlapCoef[0] * AntParam->Main3dBlobeWidth[0] / 2.0;			//方位偏角
        double PitchDelta = AntParam->OverlapCoef[1] * AntParam->Main3dBlobeWidth[1] / 2.0;		//俯仰偏角
        if (SimData->AziEleFalg == 0)
            SinglePattern(AntParam);
        else
            DiffPattern(AntParam, AziDelta, PitchDelta);
    }

	//********生成旋转矩阵*******//

	double GamaBeam = -asin(SimData->Radar2Target.y / sqrt(SimData->Radar2Target.x*SimData->Radar2Target.x +
		SimData->Radar2Target.y*SimData->Radar2Target.y + SimData->Radar2Target.z*SimData->Radar2Target.z)); 	//波束下倾角（与水平面夹角）
	double AlphaBeam = atan(SimData->Radar2Target.z / SimData->Radar2Target.x);    	//波束方位角（与北向夹角）

	if (SimData->Radar2Target.x<0)
		AlphaBeam = PI + AlphaBeam;

	double BeamAngle[2];
	BeamAngle[0] = -AlphaBeam;
	BeamAngle[1] = -GamaBeam;

	AntParam->G_Matrix[0 * 3 + 0] = cos(BeamAngle[1])*cos(BeamAngle[0]);
	AntParam->G_Matrix[1 * 3 + 0] = sin(BeamAngle[1]);
	AntParam->G_Matrix[2 * 3 + 0] = -cos(BeamAngle[1])*sin(BeamAngle[0]);

	AntParam->G_Matrix[0 * 3 + 1] = -sin(BeamAngle[1])*cos(BeamAngle[0]);
	AntParam->G_Matrix[1 * 3 + 1] = cos(BeamAngle[1]);
	AntParam->G_Matrix[2 * 3 + 1] = sin(BeamAngle[0])*sin(BeamAngle[1]);

	AntParam->G_Matrix[0 * 3 + 2] = sin(BeamAngle[0]);
	AntParam->G_Matrix[1 * 3 + 2] = 0;
	AntParam->G_Matrix[2 * 3 + 2] = cos(BeamAngle[0]);
}

//天线方向图生成槽函数
void ThreadClassAntennaGen::slotAntennaSimRe(void *p)
{
	stAntennaPara *pStAntennaPara = (stAntennaPara*)p;
	AntennaSimRe(&pStAntennaPara->RadarParam, &pStAntennaPara->AntParam, &pStAntennaPara->SimData);
}
