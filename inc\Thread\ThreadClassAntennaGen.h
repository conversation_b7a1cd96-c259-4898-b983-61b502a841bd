/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassAntennaGen.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassAntennaGen.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef THREADCLASSANTENNAGEN_H
#define THREADCLASSANTENNAGEN_H

#include <QObject>

#include"MyMalloc.h"
#include"MyMallocCUDA.h"

#include "WhiteCalGlobel.h"

class ThreadClassAntennaGen : public QObject,MyMalloc,MyMallocCUDA
{
    Q_OBJECT
public:
    explicit ThreadClassAntennaGen(QObject *parent = 0);
    ~ThreadClassAntennaGen();
    
public:
    //初始化GPU设备工作参数
	bool InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer);
    /************************************************************************
    * @brief	//天线旋转矩阵初始化
    * z
    * @param[in]	RotAng:			俯仰、偏航、横滚
    * @param[in]	GimbalsAngle:	内外框架角
    * @param[in]	BeamAngle:      波束方位、俯仰角
    * @param[out]	G_Matrix:       坐标转换旋转矩阵3*3
    ************************************************************************/
    void AntIni(double *RotAng, double *GimbalsAngle, double *BeamAngle, double *G_Matrix);
    //生产天线方向图主函数
    void AntennaSimRe(RADAR *RadarParam, ANTPARAM *AntParam, SimStruct *SimData);

signals:

public slots :
	//天线方向图生成槽函数
	void slotAntennaSimRe(void *p);
    
private:
	template <class T, class T1, class T2, class T3>
    void LineArrayCellSim(T3 *SumAntennaFunction, T1 AntGain, int ArrayNum, int SimAntSpaceNumAzi,
							int  SimAntSpaceNumPitch, T2 *Omiga, double Fc, T AntSpace,
							T SimPhaseAzi, T SimPhasePitch, T InitialePhase, T2* AntPos);
	template <class T>
    void LineArrayArraySim(T *SumAntennaFunction,double AntGain,int ArrayNum,int SubArrayNum,
                        int AntFuncAziNum,int AntFuncEleNum,Complex *Omiga,Complex *SubOmiga,double Fc,
                        double AntSpace,double AntAziBound,double AntEleBound,double SubArraySpace,
                        double InitialePhase,Complex *AntPos,Complex *SubAntPos);
	template <class T>
    void RectAngArrayCellSim(T *SumAntennaFunction, double AntGain, int AntFuncAziNum, int AntFuncEleNum,
                        int ArrayNumRow,int ArrayNumColumn,Complex *Omiga,double Fc,
                        double AntSpaceRow,double AntSpaceColumn,double AntAziBound,double AntEleBound,double InitialePhase,Complex *AntPos);
	template <class T>
    void RectAngArrayArraySim(T *SumAntennaFunction, double AntGain, int AntFuncAziNum, int AntFuncEleNum,
                        int ArrayNumRow,int SubArrayNumRow,int ArrayNumColumn,int SubArrayNumColumn,Complex *Omiga,
                        Complex *SubOmiga,double Fc,double AntSpaceRow,double SubArraySpcaeRow, double AntSpaceColumn,
                        double SubArraySpcaeColumn,double AntAziBound,double AntEleBound,double InitialePhase,Complex *AntPos,Complex *SubAntPos);    
	template <class T>
    void  CicleArrayBoundaryCellSim(T *SumAntennaFunction, double AntGain, double ArrayRadius, int AntFuncAziNum,
                        int AntFuncEleNum,int ArrayNumRow,int ArrayNumColumn,Complex *Omiga,double Fc,
                        double AntSpaceRow,double AntSpaceColumn,double AntAziBound,double AntEleBound,double InitialePhase,Complex *AntPos);    
    //圆周边界阵-子阵
	template <class T>
    void  CicleArrayBoundaryArraySim(T *SumAntennaFunction, double AntGain, double ArrayRadius, int AntFuncAziNum,
                        int AntFuncEleNum,int ArrayNumRow,int SubArrayNumRow,int ArrayNumColumn,
                        int SubArrayNumColumn,Complex *Omiga,Complex *SubOmiga,double Fc,double AntSpaceRow,double SubArraySpcaeRow,
                        double AntSpaceColumn,double SubArraySpcaeColumn,double AntAziBound,double AntEleBound,double InitialePhase,
                        Complex *AntPos,Complex *SubAntPos);
    
private:
    int m_DevID;
    char *d_BufferFather;

    double fsolve(double val, double starting);
    //sinc
    void SincFunCal(ANTPARAM *AntParam, int GridNum, double AngleBound, double AngSampSpace, float *BeamWei, double AntenMainGain, double AntenGain);
    //拟合sinc
    void SincPolyFunCal(ANTPARAM *AntParam, int GridNum, double AngleBound, double AngSampSpace, float *BeamWei, double AntenMainGain, double AntenMainZeroGain, double AntenSideGain, double AntenMeanSideGain, double AntenGain, double *SidelobeZeroPos);
    //高斯
    void GaussFunCal(ANTPARAM *AntParam, int GridNum, double AngleBound, double AngSampSpace, float *BeamWei, double AntenMainGain, double AntenMainZeroGain, double AntenSideGain, double AntenMeanSideGain, double AntenGain, double *SidelobeZeroPos);
    //cos
    void CosFunCal(ANTPARAM *AntParam, int GridNum, double AngleBound, double AngSampSpace, float *BeamWei, double AntenMainGain, double AntenMainZeroGain, double AntenSideGain, double AntenMeanSideGain, double AntenGain, double *SidelobeZeroPos);
    //余割平方
    void CscFunCal(ANTPARAM *AntParam, int GridNum, double AngleBound, double AngSampSpace, float *BeamWei, double AntenMainGain, double AntenMainZeroGain, double AntenSideGain, double AntenMeanSideGain, double AntenGain, double *SidelobeZeroPos);

    void PatternOffset(double AziDivAngle, double PitDivAngle, int AzimuGridNum, int PitchGridNum, ANTPARAM *AntParam, float *AntDivGain);
    void SinglePattern(ANTPARAM *AntParam);
    void DiffPattern(ANTPARAM *AntParam, double AziDelta, double PitchDelta);
    
};

#endif // THREADCLASSANTENNAGEN_H
