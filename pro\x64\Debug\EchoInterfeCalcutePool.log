﻿生成启动时间为 2025/6/9 17:55:37。
     1>项目“D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\pro\EchoInterfeCalcutePool.vcxproj”在节点 2 上(Build 个目标)。
     1>项目“D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\pro\EchoInterfeCalcutePool.vcxproj”(1)正在节点 2 上生成“D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\pro\EchoInterfeCalcutePool.vcxproj”(1:2) (Build 个目标)。
     1>ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\x86_amd64\CL.exe /c /ID:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\pro /IDebug /I.\GeneratedFiles\Debug /I.\GeneratedFiles /I. /I..\inc /I..\..\..\inc /I..\..\..\inc\GLWidget /I..\..\..\cuda\inc /I..\..\..\inc\MemPool /I..\inc\Thread /I..\inc\Class /I..\inc\public /I../inc/cuda /I..\inc\GLWidget /I..\..\..\inc\Extern /IC:\cuda\v10.0\include /IC:\cuda\v10.0\common\inc /IC:\cuda\v10.0 /I..\..\..\..\CalcutePool /I..\..\EchoInterfeCalcutePool_Com /Idebug /IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include /IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtOpenGL /IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets /IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui /IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtANGLE /IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore /I"C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\win32-msvc" /IDebug /ID:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\pro /Zi /nologo /W3 /WX- /MP /Od /D _WINDOWS /D UNICODE /D WIN32 /D WIN64 /D QT_DEPRECATED_WARNINGS /D _STUMEMPOOL_LIBRARY /D _GLWidgetBase_LIBRARY /D _ECHO_KERNEL_LIBRARY /D UNICODE /D WIN32 /D WIN64 /D QT_OPENGL_LIB /D QT_WIDGETS_LIB /D QT_GUI_LIB /D QT_CORE_LIB /Gm- /EHsc /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"x64\Debug\\" /Fd"x64\Debug\vc140.pdb" /Gd /TP /wd4577 /wd4467 /errorReport:prompt -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 ..\src\Thread\ThreadClassGPUCalcute.cpp
         ThreadClassGPUCalcute.cpp
     1>..\src\Thread\ThreadClassGPUCalcute.cpp : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\inc\Thread\ThreadClassGPUCalcute.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\inc\Class\ClassRcsRead.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\inc\Class\ClassCoorDinate.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\inc\WhiteCalGlobel.h(32): warning C4005: “PI”: 宏重定义
         d:\weiyingzhen\project\hqp2442\develop\p1\5_software\52_client\calcutepool\inc\globalExternalStruct.h(29): note: 参见“PI”的前一个定义
     1>d:\weiyingzhen\project\hqp2442\develop\p1\5_software\52_client\calcutepool\src\echointerfecalcutepool_com\inc\thread\ThreadClassClutter.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>d:\weiyingzhen\project\hqp2442\develop\p1\5_software\52_client\calcutepool\src\echointerfecalcutepool_com\inc\thread\ThreadClassGenJAM.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>d:\weiyingzhen\project\hqp2442\develop\p1\5_software\52_client\calcutepool\src\echointerfecalcutepool_com\inc\thread\ThreadClassAntennaGen.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>d:\weiyingzhen\project\hqp2442\develop\p1\5_software\52_client\calcutepool\src\echointerfecalcutepool_com\inc\thread\ThreadSocket.h(62): warning C4100: “parent”: 未引用的形参
     1>d:\weiyingzhen\project\hqp2442\develop\p1\5_software\52_client\calcutepool\src\echointerfecalcutepool_com\inc\thread\ThreadClassBlanketJamGen.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>d:\weiyingzhen\project\hqp2442\develop\p1\5_software\52_client\calcutepool\src\echointerfecalcutepool_com\inc\thread\ThreadClassSarEchoClutter.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\inc\Class\ClassClutterRcsRead.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>d:\weiyingzhen\project\hqp2442\develop\p1\5_software\52_client\calcutepool\src\echointerfecalcutepool_com\inc\thread\ThreadClassDeceptionJamGen.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(216): warning C4100: “buffer_size”: 未引用的形参
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(314): warning C4189: “EleOverlapLen”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(313): warning C4189: “AziOverlapLen”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(286): warning C4189: “Nr”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(375): warning C4189: “dev_Range_2”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(374): warning C4189: “dev_Range”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(398): warning C4189: “cudaStreamNO”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(660): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(621): warning C4189: “dev_Range_2”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(620): warning C4189: “dev_Range”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(1001): warning C4189: “cur2”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(811): warning C4189: “TarNum”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(934): warning C4189: “Err”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(1019): warning C4189: “k”: 局部变量已初始化但不引用
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(1117): warning C4100: “p”: 未引用的形参
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(1121): warning C4100: “pc”: 未引用的形参
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(1127): warning C4100: “pc”: 未引用的形参
     1>..\src\Thread\ThreadClassGPUCalcute.cpp(1127): warning C4100: “p”: 未引用的形参
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\x86_amd64\link.exe /ERRORREPORT:PROMPT /OUT:"..\bin\\EchoInterfeCalcutePool.exe" /NOLOGO /LIBPATH:C:\utils\my_sql\my_sql\lib /LIBPATH:C:\utils\postgresql\pgsql\lib /LIBPATH:C:\cuda\v10.0\lib\x64 /LIBPATH:C:\CUDA\v10.0\lib\x64 /LIBPATH:D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\lib /LIBPATH:C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib /LIBPATH:C:\utils\my_sql\my_sql\lib /LIBPATH:C:\utils\postgresql\pgsql\lib shell32.lib opengl32.lib C:\cuda\v10.0\lib\x64\cudart.lib C:\cuda\v10.0\lib\x64\cublas.lib C:\cuda\v10.0\lib\x64\cufft.lib C:\cuda\v10.0\lib\x64\curand.lib D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\lib\_stumempoold.lib D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\lib\GLWidgetBased.lib D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\lib\ShareMemoryd.lib C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\qtmaind.lib shell32.lib C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5OpenGLd.lib C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Widgetsd.lib C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Guid.lib C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Cored.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /Debug /PDB:"..\bin\EchoInterfeCalcutePool.pdb" /SUBSYSTEM:WINDOWS /TLBID:1 /DYNAMICBASE /NXCOMPAT /MACHINE:X64 "/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'" KernelAntennatestcuda.obj
         KernelChafftestcuda.obj
         KernelCoorConvertestcuda.obj
         KernelCulttertestcuda.obj
         KernelJAMtestcuda.obj
         KernelPublictestcuda.obj
         KernelSartestcuda.obj
         KernelSplinetestcuda.obj
         KernelTarHsystestcuda.obj
         KernelTarHsysFdtestcuda.obj
         x64\Debug\ClassClutterRcsRead.obj
         x64\Debug\ClassCoorDinate.obj
         x64\Debug\ClassEchoWaveFd.obj
         x64\Debug\ClassRcsRead.obj
         x64\Debug\GLWidgetInstanceBWFreq.obj
         x64\Debug\GLWidgetWave.obj
         x64\Debug\MainWindow.obj
         x64\Debug\MyMalloc.obj
         x64\Debug\MyMallocCUDA.obj
         x64\Debug\ThreadClassAntennaGen.obj
         x64\Debug\ThreadClassBase.obj
         x64\Debug\ThreadClassBlanketJamGen.obj
         x64\Debug\ThreadClassChaffEcho.obj
         x64\Debug\ThreadClassClutter.obj
         x64\Debug\ThreadClassDeceptionJamGen.obj
         x64\Debug\ThreadClassGPUCalcute.obj
         x64\Debug\ThreadClassGenJAM.obj
         x64\Debug\ThreadClassJFEcho.obj
         x64\Debug\ThreadClassSarEcho.obj
         x64\Debug\ThreadClassSarEchoClutter.obj
         x64\Debug\ThreadClassSendWaveGen.obj
         x64\Debug\ThreadEchoDataFormat.obj
         x64\Debug\ThreadEchoDataSave.obj
         x64\Debug\ThreadResourceScheduling.obj
         x64\Debug\ThreadSocket.obj
         x64\Debug\ThreadSocketTCP.obj
         x64\Debug\ThreadSocketTCPExtern.obj
         x64\Debug\WhiteCalGloable.obj
         x64\Debug\WidgetShow.obj
         x64\Debug\main.obj
         x64\Debug\moc_GLWidgetInstanceBWFreq.obj
         x64\Debug\moc_GLWidgetWave.obj
         x64\Debug\moc_MainWindow.obj
         x64\Debug\moc_ThreadClassAntennaGen.obj
         x64\Debug\moc_ThreadClassBase.obj
         x64\Debug\moc_ThreadClassBlanketJamGen.obj
         x64\Debug\moc_ThreadClassChaffEcho.obj
         x64\Debug\moc_ThreadClassClutter.obj
         x64\Debug\moc_ThreadClassDeceptionJamGen.obj
         x64\Debug\moc_ThreadClassGPUCalcute.obj
         x64\Debug\moc_ThreadClassGenJAM.obj
         x64\Debug\moc_ThreadClassJFEcho.obj
         x64\Debug\moc_ThreadClassSarEcho.obj
         x64\Debug\moc_ThreadClassSarEchoClutter.obj
         x64\Debug\moc_ThreadClassSendWaveGen.obj
         x64\Debug\moc_ThreadEchoDataFormat.obj
         x64\Debug\moc_ThreadEchoDataSave.obj
         x64\Debug\moc_ThreadResourceScheduling.obj
         x64\Debug\moc_ThreadSocket.obj
         x64\Debug\moc_ThreadSocketTCP.obj
         x64\Debug\moc_ThreadSocketTCPExtern.obj
         x64\Debug\moc_WidgetShow.obj
         x64\Debug\moc__GLWidgetBase.obj
         EchoInterfeCalcutePool.vcxproj -> D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\pro\..\bin\EchoInterfeCalcutePool.exe
     1>已完成生成项目“D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\pro\EchoInterfeCalcutePool.vcxproj”(Build 个目标)的操作。
     1>已完成生成项目“D:\weiyingzhen\Project\HQP2442\DEVELOP\P1\5_Software\52_Client\CalcutePool\src\EchoInterfeCalcutePool_Com\pro\EchoInterfeCalcutePool.vcxproj”(Build 个目标)的操作。

已成功生成。

已用时间 00:00:01.34
