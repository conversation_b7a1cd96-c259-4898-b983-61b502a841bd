/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassGPUCalcute.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassGPUCalcute.cpp $
 *         $Author: yening $
 *         $Revision: 507 $
 *         $Date: 2025-05-27 19:14:15 $
*******************************************************************************************/
#include "ThreadClassGPUCalcute.h"
#include<QThread>
#include<QDebug>
#include<QDateTime>

#include <cuda_runtime_api.h>
#include "KernelPublic.h"
#include "ThreadClassSendWaveGen.h"

#include "KernelPublic.h"
#include "KernelTarHsys.h"
#include "KernelTarHsysFd.h"


ThreadClassGPUCalcute::ThreadClassGPUCalcute(int Dev,QObject *parent) : QObject(parent)
{
    m_Dev = Dev;
	_DefineMemPoolCUDA(stEchoData, 8);
	_DefineMemPool(stSarEchoPara, 30);
	_DefineMemPool(stClutterEchoPara, 30);
	_DefineMemPool(_stChaffEchoPara, 50);
	_DefineMemPool(stJamEchoPara, 30);

	InitMyMalloc(32 * 1024 * 1024);

	m_GPU_Num = 0;
	for (int i = 0; i < MAX_GPU_NUM; i++){
		m_GPU_ID[i] = -1;
	}
	bGenJAM_Flag = false;

	//三角函数查表
	double CosValueSample = 1.0 / 4096;
	CosValueLen = 4100;
	m_CosValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_CosValue, 0, sizeof(float)*CosValueLen);
	m_SinValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_SinValue, 0, sizeof(float)*CosValueLen);
	for (int idd = 0; idd < CosValueLen; idd++)
	{
		if (CosValueSample*idd <= 1)
		{
			m_CosValue[idd] = cos(idd*CosValueSample * 2 * PI);
			m_SinValue[idd] = sin(idd*CosValueSample * 2 * PI);
		}
	}

    pThreadClassSarEcho     = new ThreadClassSarEcho;
    pThreadClassJFEcho      = new ThreadClassJFEcho;
    pThreadClassChaffEcho   = new ThreadClassChaffEcho;
	pThreadClassSarEchoClutter = new ThreadClassSarEchoClutter;
	pThreadClassBlanketJamGen = new ThreadClassBlanketJamGen;
	pThreadClassDeceptionJamGen = new ThreadClassDeceptionJamGen;

	connect(pThreadClassSarEcho, SIGNAL(sendEchoScheduling(void*)), this, SLOT(slotEchoGPUCalcute(void*)), Qt::QueuedConnection);
	connect(this, SIGNAL(sendGenSenceEcho(void*)), pThreadClassSarEcho, SLOT(slotGenSenceEcho(void*)), Qt::QueuedConnection);
	
	pStEchoPara = new stEchoPara; memset(pStEchoPara, 0, sizeof(stEchoPara));

	m_Fs = m_Br = m_Tp = 0;

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassGPUCalcute::~ThreadClassGPUCalcute()
{

}
//初始化GPU运算资源，由外部配置，支持重新配置
bool ThreadClassGPUCalcute::InitGPUResource(int *GPU_IDInfo,stTaskInfo *pStTaskInfo)
{
    m_GPU_Num = pStTaskInfo->GPU_Num;
    //GPU ID是否自动分配
    if(pStTaskInfo->GPU_ID == 0xFFFF)//自动分配
    {
        for(int i = 0; i < m_GPU_Num;i++){
            int cnt = 0;
            while(cnt < 8){
                if(GPU_IDInfo[cnt] == 0){break;}
                cnt++;
            }
            m_GPU_ID[i] = cnt;
        } 
    }
    else{
        for(int i = 0; i < m_GPU_Num;i++){
            m_GPU_ID[i] = (pStTaskInfo->GPU_ID >>(i*8)) & 0xFF;
        } 
    }
    //
    for(int i = 0; i < m_GPU_Num;i++)
    {
        cudaSetDevice(m_GPU_ID[i]);
		InitCUDAPara();
        char *d_Buffer = nullptr;
		char *d_BufferPublic = nullptr;
		qint64 SarBytesPub = 5.0 * 1024 * 1024 * 1024.0;
		cudaMalloc((void**)&d_BufferPublic, SarBytesPub);
        for(int j = 0; j < 3;j++)
        {
            if((pStTaskInfo->GPU_Func[i] >>j) & 0x01)
            {
                switch(j)
                {
                case 0:{
                    qint64 SarBytes = 2.5 * 1024 * 1024 * 1024.0;
					cudaMalloc((void**)&d_Buffer, SarBytes);
					InitDeviceWorkPara(m_GPU_ID[i], SarBytes, d_Buffer, d_BufferPublic);
					cudaMalloc((void**)&d_Buffer, SarBytes);
					pThreadClassSarEcho->InitCUDAPara();
					pThreadClassSarEcho->InitDeviceWorkPara(m_GPU_ID[i], SarBytes, d_Buffer, d_BufferPublic);
					cudaError_t Err = cudaGetLastError();
                    //角反
					SarBytes = 1.6 * 1024 * 1024 * 1024.0;
					cudaMalloc((void**)&d_Buffer, SarBytes);
					pThreadClassJFEcho->InitDeviceWorkPara(m_GPU_ID[i], SarBytes, d_Buffer, d_BufferPublic);
					Err = cudaGetLastError();
                    break;
                }
                case 1:{
					//面目标杂波
					qint64 SarBytes = 6.0 * 1024 * 1024 * 1024.0;
					cudaMalloc((void**)&d_Buffer, SarBytes);
					pThreadClassSarEchoClutter->InitDeviceWorkPara(m_GPU_ID[i], SarBytes, d_Buffer, d_BufferPublic);
					break;
                }
                case 2:{
					qint64 SarBytes = 256.0 * 1024 * 1024.0;
					cudaMalloc((void**)&d_Buffer, SarBytes);
					pThreadClassBlanketJamGen->InitCUDAPara();
					pThreadClassBlanketJamGen->InitDeviceWorkPara(m_GPU_ID[i], SarBytes, d_Buffer, d_BufferPublic);
					cudaError_t Err = cudaGetLastError();

					SarBytes = 256.0 * 1024 * 1024.0;
					cudaMalloc((void**)&d_Buffer, SarBytes);
					pThreadClassDeceptionJamGen->InitCUDAPara();
					pThreadClassDeceptionJamGen->InitDeviceWorkPara(m_GPU_ID[i], SarBytes, d_Buffer, d_BufferPublic);
					Err = cudaGetLastError();

					SarBytes = 1024.0 * 1024 * 1024.0;
					cudaMalloc((void**)&d_Buffer, SarBytes);
					pThreadClassChaffEcho->InitCUDAPara();
					pThreadClassChaffEcho->InitDeviceWorkPara(m_GPU_ID[i], SarBytes, d_Buffer, d_BufferPublic);

                    break;
                }
                default:break;
                }
            }            
        }
        
    }
    return true;
}
//初始化cuda参数
bool ThreadClassGPUCalcute::InitCUDAPara()
{
	cudaSetDevice(m_Dev);
	int    BATCH = 0;
	cufftResult ret;
	for (int i = 0; i < 128; i++)
	{
		BATCH = i+1;
		ret = cufftPlan1d(&m_planEchoCvntModu[i], ECHO_FFT_DOT, CUFFT_C2C, BATCH);
	}
    BATCH = 1;
    for (int i = 0; i < CUDAStreamMaxNum; i++)
    {
        cudaStreamCreate(&cudaStream[i]);
        cufftPlan1d(&plan[i], 16384, CUFFT_C2C, BATCH);
        cufftSetStream(plan[i], cudaStream[i]);
    }
    //随机数句柄产生
    curandCreateGenerator(&gen_curand,CURAND_RNG_PSEUDO_MRG32K3A);

	qint64 SarBytes = 128.0*ECHO_FFT_DOT*sizeof(Complexf);
	cudaMalloc((void**)&dev_SumEchoDataPolar1, SarBytes);
	cudaMalloc((void**)&dev_AziEchoDataPolar1, SarBytes);
	cudaMalloc((void**)&dev_EleEchoDataPolar1, SarBytes);
	cudaMalloc((void**)&dev_ExEchoDataPolar1, SarBytes);

	cudaMalloc((void**)&dev_SumEchoDataPolarFinal, SarBytes);
	cudaMalloc((void**)&dev_AziEchoDataPolarFinal, SarBytes);
	cudaMalloc((void**)&dev_EleEchoDataPolarFinal, SarBytes);
	cudaMalloc((void**)&dev_ExEchoDataPolarFinal, SarBytes);

    SarBytes = 3072*3072*sizeof(float);
    cudaMalloc((void**)&dev_Target_ALL1, SarBytes);
    cudaMalloc((void**)&dev_Target_ALL2, SarBytes);
    cudaMalloc((void**)&dev_Target_ALL3, SarBytes);
    cudaMalloc((void**)&dev_Target_ALL4, SarBytes);
    cudaMalloc((void**)&dev_Target_ALL5, SarBytes);
    cudaMemset(dev_Target_ALL1,0,SarBytes);
    cudaMemset(dev_Target_ALL2,0,SarBytes);
    cudaMemset(dev_Target_ALL3,0,SarBytes);
    cudaMemset(dev_Target_ALL4,0,SarBytes);
    cudaMemset(dev_Target_ALL5,0,SarBytes);

	cudaMalloc((void**)&dev_Signal, 256 * 1024 * sizeof(float));
	cudaMalloc((void**)&dev_Signal_Send, 256 * 1024 * sizeof(float));

	return true;
}
//初始化GPU设备工作参数
bool ThreadClassGPUCalcute::InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic)
{
    BufferInit_Cur			= 0;
    BufferInit_SizeTotal	= (long long)512 * 1024 * 1024;

    m_DevID				= DevID;
    d_BufferInit		= d_Buffer;
    d_BufferFather		= d_BufferPublic;
    //分配GPU伪内存池空间
    InitMyMallocCUDA(d_BufferFather, 5.0 * 1024 * 1024 * 1024);

    //发射信号缓存区
    dev_Signal_Send = (Complexf*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += 128*1024*sizeof(float);
    //三角函数查表
    long long Bytes = (CosValueLen*sizeof(float) + 16) / 16 * 16;
    dev_CosValue = (float*)(d_BufferInit + BufferInit_Cur);
    cudaMemcpy(dev_CosValue, m_CosValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
    dev_SinValue = (float*)(d_BufferInit + BufferInit_Cur);
    cudaMemcpy(dev_SinValue, m_SinValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;

    Bytes = 5000.f * 5000.f * sizeof(float);
    dev_AziSubAntennaFunction   = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_SumAntennaFunction      = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
    dev_PitSubAntennaFunction   = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;

    m_AntParam = (ANTPARAM*)malloc(sizeof(ANTPARAM));//仅存储天线参数

    return true;
}
//天线参数初始化
void ThreadClassGPUCalcute::SettingAntPara(void *p)
{
    cudaSetDevice(m_DevID);
    _Release_Malloc();      //初始化伪内存池
    _Release_MallocCUDA();
    ANTPARAM        *AntParam = (ANTPARAM*)p;
    //***********天线加权gpu***********//
    cudaMemcpy(dev_SumAntennaFunction, AntParam->SumAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_AziSubAntennaFunction, AntParam->AziSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    cudaMemcpy(dev_PitSubAntennaFunction, AntParam->PitSubAntennaFunction, sizeof(float)*AntParam->AntFuncAziNum*AntParam->AntFuncEleNum, cudaMemcpyHostToDevice);
    memcpy(m_AntParam, AntParam, sizeof(ANTPARAM));
}
//目标RCS散射系数
void ThreadClassGPUCalcute::InitTarRcsPara(void *p)
{
	stTarPitLink *pStTarPitLink = (stTarPitLink*)p;
	if (pStTarPitLink->iTarNo == 0){
		pThreadClassJFEcho->InitTarRcsPara(p);
	}
	else{
		pThreadClassSarEcho->InitTarRcsPara(p);
	}
}
//加载杂波散射系数文件
void ThreadClassGPUCalcute::InitClutterRcsPara(void *p)
{
    pThreadClassSarEchoClutter->InitClutterRcsPara(p);
}

void ThreadClassGPUCalcute::InitTarClutterPara(void *p)
{
	pThreadClassSarEchoClutter->InitTarClutterPara(p);
}

//运算参数参数初始化
void ThreadClassGPUCalcute::UpdateCalcutePara(RADAR *ParamR, ANTPARAM *AntParam, SimStruct *SimData)
{
    int TarNum	= 8192;// 此值用来预分配内存和显存
    int TrNum	= (int)(ParamR->Fs*ParamR->Tp + 0.5f);		//一个脉宽的点数

	int Nr		= ECHO_FFT_DOT;
    int SaveNr	= SimData->SaveFlie_Nr;

    //to do
    ParamR->RadomeLoss = 1;
    //-------------------------------------------------------------------------//
	DOUBLE_2 *param = (DOUBLE_2*)_Malloc(sizeof(DOUBLE_2) * 32);//GPU计算时需要的双精度参数，内存
    param[0] = SimData->Rmin;
	param[1] = 2.0*ParamR->Fs / C_;
	param[2] = -2.0 / (C_ / ParamR->Fc);//-4.0*PI / (C_ / ParamR->Fc)
	param[3] = ParamR->Amp*ParamR->RadomeLoss*(C_ / ParamR->Fc)*(C_ / ParamR->Fc) / (64.0 * PI*PI*PI);
	param[4] = 2 * PI;
    param[5] = 1.0 / (AntParam->AntAziBound * 2 / AntParam->AntFuncAziNum);
    param[6] = 1.0 / (AntParam->AntEleBound * 2 / AntParam->AntFuncEleNum);
	param[7] = AntParam->AziAngle;		//波束方位角
	param[8] = AntParam->PitAngle;		//波束俯仰角
	param[9] = 1.519917446101894e+05;	//辅助天线增益


	DOUBLE_2 *param2 = (DOUBLE_2*)_Malloc(sizeof(DOUBLE_2) * 32);
    param2[0] = SimData->Rmin;
    param2[1] = 2.0*ParamR->Fs / C_;
    param2[2] = -2.0 / (C_ / ParamR->Fc);//-4.0*PI / (C_ / ParamR->Fc)
    param2[3] = ParamR->Amp*ParamR->RadomeLoss*(C_ / ParamR->Fc)*(C_ / ParamR->Fc) / (64.0 * PI*PI*PI);
    param2[4] = 2 * PI;
    param2[5] = 1.0 / (2.0 * PI);

    int AziOverlapLen = (int)(AntParam->Main3dBlobeWidth[0] * param[3] *0.5f + 0.5f);//方位向半重叠波束长度
    int EleOverlapLen = (int)(AntParam->Main3dBlobeWidth[1] * param[4] *0.5f + 0.5f);//俯仰向半重叠波束长度

    int *else_param;//GPU计算时需要的整形参数，内存
    else_param = (int*)_Malloc(sizeof(int) * 32);
    else_param[0] = TarNum;//散射点个数
    else_param[1] = TrNum;//发射信号的实际点数
    else_param[2] = AntParam->AntFuncAziNum;
    else_param[3] = AntParam->AntFuncEleNum;
    else_param[4] = SimData->AziEleFalg;
    else_param[5] = SimData->TransLossFlag;
    //else_param[6] = (int)(-AziOverlapLen + AntParam->AntFuncAziNum *0.5f + 0.5f);//方位起始照射索引
    //else_param[7] = (int)(AziOverlapLen + AntParam->AntFuncAziNum *0.5f + 0.5f);//方位结束照射索引
    //else_param[8] = (int)(-EleOverlapLen + AntParam->AntFuncEleNum *0.5f + 0.5f);//俯仰起始照射索引
    //else_param[9] = (int)(EleOverlapLen + AntParam->AntFuncEleNum *0.5f + 0.5f);//俯仰结束照射索引

	else_param[6] = 0;//方位起始照射索引
	else_param[7] = AntParam->AntFuncAziNum;//方位结束照射索引
	else_param[8] = 0;//俯仰起始照射索引
	else_param[9] = AntParam->AntFuncEleNum;//俯仰结束照射索引
	else_param[10] = SaveNr;
	else_param[13] = ParamR->Na;

	pStEchoPara->param		= param;
	pStEchoPara->param2		= param2;
	pStEchoPara->else_param = else_param;
}
//传函回波调制
void ThreadClassGPUCalcute::GenEchoWave(void *p,stSarEchoPara *pStSarEchoPara,stEchoData *pStEchoData)
{
	if (pStEchoData->SacreNum <= 0)
		return;
	cudaSetDevice(m_DevID);
    cudaError_t Err;
    stEchoPara *pStEchoPara = (stEchoPara*)p;
	DOUBLE_2   *param = pStEchoPara->param;
	DOUBLE_2   *param2			= pStEchoPara->param2;
    int     *else_param		= pStEchoPara->else_param;

	int     TarNum		= pStEchoData->SacreNum;
    int     Na          = pStEchoData->Na_Save;
    int     Nr          = ECHO_FFT_DOT;

	//to do
	//TarNum = 1;
	//cudaMemset(dev_Target_ALL1, 0, 1024);
	//cudaMemset(dev_Target_ALL2, 0, 1024);
	//cudaMemset(dev_Target_ALL3, 0, 1024);
    double *Ps_i = (double*)_Malloc(sizeof(double)*4*128);

    double *dev_Ps          = (double*)_MallocCUDA(sizeof(double)*4*128);
	DOUBLE_2 *dev_param = (DOUBLE_2*)_MallocCUDA(sizeof(DOUBLE_2) * 4 * 128);
	DOUBLE_2 *dev_param2		= (DOUBLE_2*)_MallocCUDA(sizeof(DOUBLE_2) * 4 * 128);
	int *dev_else_param		= (int*)_MallocCUDA(sizeof(int) * 4 * 128);

    Err = cudaGetLastError();
    float *dev_ant_factor, *dev_AziAnt_factor, *dev_EleAnt_factor;
	dev_ant_factor		= (float*)_MallocCUDA(TarNum*sizeof(float)*Na);
	dev_AziAnt_factor = (float*)_MallocCUDA(TarNum*sizeof(float)*Na);
	dev_EleAnt_factor = (float*)_MallocCUDA(TarNum*sizeof(float)*Na);

	double *dev_Range = (double*)_MallocCUDA(TarNum*sizeof(double));
    float *dev_Range_2	= (float*)_MallocCUDA(TarNum*sizeof(float));

	Complexf*dev_hsysSum = pStEchoData->dev_SumEchoDataPolar1;//产生系统响应函数
	Complexf*dev_hsysAzi = pStEchoData->dev_AziEchoDataPolar1;
	Complexf*dev_hsysPit = pStEchoData->dev_EleEchoDataPolar1;
	Complexf*dev_hsys_Ex = pStEchoData->dev_ExEchoDataPolar1;

    else_param[0] = TarNum;
    cudaMemcpy(dev_else_param, else_param, 15 * sizeof(int), cudaMemcpyHostToDevice);
	cudaMemcpy(dev_param, param, 15 * sizeof(DOUBLE_2), cudaMemcpyHostToDevice);
    cudaMemcpy(dev_param2, param2, 15 * sizeof(DOUBLE_2), cudaMemcpyHostToDevice);

	Complexf *pTemp = (Complexf *)_Malloc(TarNum * 4);
	cudaMemcpy(pTemp, dev_SumAntennaFunction, 1024, cudaMemcpyDeviceToHost);
	float *pTemp1 = (float *)_Malloc(1024*1024);
	


    PLATFORM *pPlatForm = pStSarEchoPara->PlatForm;

	int cntPs = 0;
	for (int idx = 0; idx < Na; idx++)
	{
		int cudaStreamNO = idx;
		Ps_i[cntPs++] = pPlatForm[idx].NorthPos;
		Ps_i[cntPs++] = pPlatForm[idx].SkyPos;
		Ps_i[cntPs++] = pPlatForm[idx].EastPos;
		cntPs++;
	}
	cudaMemcpy(dev_Ps, Ps_i, sizeof(double) * 4 * Na, cudaMemcpyHostToDevice);
	for (int idx = 0; idx < 1; idx++)
	{
		dim3 ThreadsPerBlock(512, 1);
		dim3 BlockNum((TarNum + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);

		CUDA_HSYS_MutiPulseClutter(0, ThreadsPerBlock, BlockNum, dev_Ps + idx * 4, dev_param, dev_else_param,
			dev_Target_ALL1, dev_Target_ALL2, dev_Target_ALL3, dev_Target_ALL4, dev_Target_ALL5, dev_CosValue, dev_SinValue,
			dev_SumAntennaFunction, dev_AziSubAntennaFunction, dev_PitSubAntennaFunction,
			dev_hsysSum + Nr*idx, dev_hsysAzi + Nr*idx, dev_hsysPit + Nr*idx, dev_hsys_Ex + Nr*idx);
		cudaMemcpy(pTemp1, dev_Target_ALL1, TarNum, cudaMemcpyDeviceToHost);
		cudaMemcpy(pTemp1, dev_Target_ALL2, TarNum, cudaMemcpyDeviceToHost);
		if (m_Dev == 5)
		{
			cudaMemcpy(pTemp, dev_hsysSum, sizeof(Complexf)*Nr*Na, cudaMemcpyDeviceToHost);
			QFile *pQFile = new QFile;
			pQFile->setFileName("..\\..\\..\\data\\EchoDMC3");
			pQFile->open(QFile::WriteOnly);
			pQFile->write((char*)pTemp, sizeof(Complexf)*Nr*Na);
			pQFile->close();
			delete pQFile;
		}
		Err = cudaGetLastError();
		if (Err != cudaSuccess) {
			qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
		}
		//卷积调制
		EchoCovtModu(pStEchoData, dev_Signal, nullptr);
	}
	//if (m_Dev ==5 )
	//{
	//	cudaMemcpy(pTemp, dev_hsysSum, sizeof(Complexf)*Nr, cudaMemcpyDeviceToHost);
	//	QFile *pQFile = new QFile;
	//	pQFile->setFileName("..\\..\\..\\data\\EchoDMC3");
	//	pQFile->open(QFile::WriteOnly);
	//	pQFile->write((char*)pTemp, sizeof(Complexf)*Nr);
	//	pQFile->close();
	//	delete pQFile;
	//}

}
//传函回波调制HCC
void ThreadClassGPUCalcute::GenEchoWaveHCC(void *p, stSarEchoPara *pStSarEchoPara, stEchoData *pStEchoData)
{
	cudaSetDevice(m_DevID);
	cudaError_t Err;
	stEchoPara *pStEchoPara = (stEchoPara*)p;
	DOUBLE_2   *param = pStEchoPara->param;
	DOUBLE_2   *param2 = pStEchoPara->param2;
	int     *else_param = pStEchoPara->else_param;

	int     TarNum = pStEchoData->SacreNum;
	int     Na = pStEchoData->Na_Save;
	int     Nr = ECHO_FFT_DOT;

	else_param[0] = TarNum;

	double *Ps_i = (double*)_Malloc(sizeof(double) * 4 * 128);

	double *dev_Ps = (double*)_MallocCUDA(sizeof(double) * 4 * 128);
	DOUBLE_2 *dev_param = (DOUBLE_2*)_MallocCUDA(sizeof(DOUBLE_2) * 4 * 128);
	DOUBLE_2 *dev_param2 = (DOUBLE_2*)_MallocCUDA(sizeof(DOUBLE_2) * 4 * 128);
	int *dev_else_param = (int*)_MallocCUDA(sizeof(int) * 4 * 128);

	Err = cudaGetLastError();
	float *dev_ant_factor, *dev_AziAnt_factor, *dev_EleAnt_factor;
	dev_ant_factor = (float*)_MallocCUDA(TarNum*sizeof(float)*Na);
	dev_AziAnt_factor = (float*)_MallocCUDA(TarNum*sizeof(float)*Na);
	dev_EleAnt_factor = (float*)_MallocCUDA(TarNum*sizeof(float)*Na);

	double *dev_Range = (double*)_MallocCUDA(TarNum*sizeof(double));
	float *dev_Range_2 = (float*)_MallocCUDA(TarNum*sizeof(float));

	Complexf*dev_hsysSum = (Complexf*)_MallocCUDA(sizeof(Complexf)*Nr* Na);//产生系统响应函数
	Complexf*dev_hsysAzi = (Complexf*)_MallocCUDA(sizeof(Complexf)*Nr* Na);
	Complexf*dev_hsysEle = (Complexf*)_MallocCUDA(sizeof(Complexf)*Nr* Na);
	cudaMemset(dev_hsysSum, 0, sizeof(Complexf)*Nr* Na);
	cudaMemset(dev_hsysAzi, 0, sizeof(Complexf)*Nr* Na);
	cudaMemset(dev_hsysEle, 0, sizeof(Complexf)*Nr* Na);

	else_param[0] = TarNum;
	cudaMemcpy(dev_else_param, else_param, 15 * sizeof(int), cudaMemcpyHostToDevice);
	cudaMemcpy(dev_param, param, 15 * sizeof(DOUBLE_2), cudaMemcpyHostToDevice);
	cudaMemcpy(dev_param2, param2, 15 * sizeof(DOUBLE_2), cudaMemcpyHostToDevice);

	Complexf *pTemp = (Complexf *)_Malloc(TarNum * 4);
	cudaMemcpy(pTemp, dev_SumAntennaFunction, 1024, cudaMemcpyDeviceToHost);

	PLATFORM *pPlatForm = pStSarEchoPara->PlatForm;


	for (int idx = 0; idx < Na; idx++)
	{
		Err = cudaGetLastError();
		if (Err != cudaSuccess){
			qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
		}
		Ps_i[0] = pPlatForm[idx].NorthPos;
		Ps_i[1] = pPlatForm[idx].SkyPos;
		Ps_i[2] = pPlatForm[idx].EastPos;

		//TarNum = 1;
		cudaMemcpy(dev_Ps, Ps_i, sizeof(double) * 4, cudaMemcpyHostToDevice);
		Err = cudaGetLastError();
		if (Err != cudaSuccess){
			qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
		}
		dim3 ThreadsPerBlock(512, 1);
		dim3 BlockNum((TarNum + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
		//计算每个目标的天线加权(固定距离门)
		CUDA_Ant_Pattern_HCC(0, ThreadsPerBlock, BlockNum, dev_Ps, dev_param, dev_else_param,
			dev_Target_ALL1, dev_Target_ALL2, dev_Target_ALL3,
			dev_SumAntennaFunction, dev_AziSubAntennaFunction, dev_PitSubAntennaFunction, 
			dev_ant_factor, dev_AziAnt_factor, dev_EleAnt_factor, dev_Range, dev_Range_2);
		Err = cudaGetLastError();
		if (Err != cudaSuccess){
			qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
		}

		Complexf *dev_SumEchoDataPolar1 = dev_hsysSum + Nr*idx;
		Complexf *dev_AziEchoDataPolar1 = dev_hsysAzi + Nr*idx;
		Complexf *dev_EleEchoDataPolar1 = dev_hsysEle + Nr*idx;

		//固定距离门的传函计算(不叠加多普勒频率)
		CUDA_Hsys_jushu_HCC(0, ThreadsPerBlock, BlockNum, dev_Ps, dev_param, dev_else_param,
			dev_Target_ALL4, dev_Target_ALL5, dev_CosValue, dev_SinValue,
			dev_ant_factor, dev_AziAnt_factor, dev_EleAnt_factor, dev_Range, dev_Range_2, dev_SumEchoDataPolar1, dev_AziEchoDataPolar1, dev_EleEchoDataPolar1);
		Err = cudaGetLastError();
		if (Err != cudaSuccess){
			qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
		}

		//频域传函
		cufftHandle plan = m_planEchoCvntModu[0];
		cufftExecC2C(plan, (cufftComplex*)dev_SumEchoDataPolar1, (cufftComplex*)dev_SumEchoDataPolar1, CUFFT_FORWARD);
		cufftExecC2C(plan, (cufftComplex*)dev_AziEchoDataPolar1, (cufftComplex*)dev_AziEchoDataPolar1, CUFFT_FORWARD);
		cufftExecC2C(plan, (cufftComplex*)dev_EleEchoDataPolar1, (cufftComplex*)dev_EleEchoDataPolar1, CUFFT_FORWARD);

		BlockNum.x = (Nr + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x;
		CUDA_ComplexMutipleSend(0, ThreadsPerBlock, BlockNum, Nr, Nr, (Complexf*)dev_Signal, (Complexf*)dev_SumEchoDataPolar1, dev_SumEchoDataPolar1, 1);
		CUDA_ComplexMutipleSend(0, ThreadsPerBlock, BlockNum, Nr, Nr, (Complexf*)dev_Signal, (Complexf*)dev_AziEchoDataPolar1, dev_AziEchoDataPolar1, 1);
		CUDA_ComplexMutipleSend(0, ThreadsPerBlock, BlockNum, Nr, Nr, (Complexf*)dev_Signal, (Complexf*)dev_EleEchoDataPolar1, dev_EleEchoDataPolar1, 1);
		cufftExecC2C(plan, (cufftComplex*)dev_SumEchoDataPolar1, (cufftComplex*)dev_SumEchoDataPolar1, CUFFT_INVERSE);
		cufftExecC2C(plan, (cufftComplex*)dev_AziEchoDataPolar1, (cufftComplex*)dev_AziEchoDataPolar1, CUFFT_INVERSE);
		cufftExecC2C(plan, (cufftComplex*)dev_EleEchoDataPolar1, (cufftComplex*)dev_EleEchoDataPolar1, CUFFT_INVERSE);
		//数据截取
		int Num = pStEchoData->Nr_Save;
		BlockNum.x = (Num + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x;
		CUDA_DataCutComplexAdd(0, ThreadsPerBlock, BlockNum, Num, ECHO_FFT_DOT, pStEchoData->Nr_Save, dev_SumEchoDataPolar1, dev_SumEchoDataPolarFinal + Num*idx);
		CUDA_DataCutComplexAdd(0, ThreadsPerBlock, BlockNum, Num, ECHO_FFT_DOT, pStEchoData->Nr_Save, dev_AziEchoDataPolar1, dev_AziEchoDataPolarFinal + Num*idx);
		CUDA_DataCutComplexAdd(0, ThreadsPerBlock, BlockNum, Num, ECHO_FFT_DOT, pStEchoData->Nr_Save, dev_EleEchoDataPolar1, dev_EleEchoDataPolarFinal + Num*idx);

		Err = cudaGetLastError();
		if (Err != cudaSuccess){
			qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_Dev;
		}
	}
	cudaMemcpy(pStEchoData->SumEchoDataPolar1, dev_SumEchoDataPolarFinal, sizeof(Complexf)*pStEchoData->Nr_Save*Na, cudaMemcpyDeviceToHost);
	cudaMemcpy(pStEchoData->AziEchoDataPolar1, dev_AziEchoDataPolarFinal, sizeof(Complexf)*pStEchoData->Nr_Save*Na, cudaMemcpyDeviceToHost);
	cudaMemcpy(pStEchoData->EleEchoDataPolar1, dev_EleEchoDataPolarFinal, sizeof(Complexf)*pStEchoData->Nr_Save*Na, cudaMemcpyDeviceToHost);


	if (m_Dev == 8)
	{
		QFile *pQFile = new QFile;
		pQFile->setFileName("..\\..\\..\\data\\EchoDMC3");
		pQFile->open(QFile::WriteOnly);
		cudaMemcpy(pTemp, dev_SumEchoDataPolarFinal + pStEchoData->Nr_Save*(Na - 1), sizeof(Complexf)*pStEchoData->Nr_Save, cudaMemcpyDeviceToHost);
		pQFile->write((char*)pTemp, sizeof(Complexf) *pStEchoData->Nr_Save);
		cudaMemcpy(pTemp, dev_AziEchoDataPolarFinal + pStEchoData->Nr_Save*(Na - 1), sizeof(Complexf)*pStEchoData->Nr_Save, cudaMemcpyDeviceToHost);
		pQFile->write((char*)pTemp, sizeof(Complexf) *pStEchoData->Nr_Save);
		cudaMemcpy(pTemp, dev_EleEchoDataPolarFinal + pStEchoData->Nr_Save*(Na - 1), sizeof(Complexf)*pStEchoData->Nr_Save, cudaMemcpyDeviceToHost);
		pQFile->write((char*)pTemp, sizeof(Complexf) *pStEchoData->Nr_Save);
		pQFile->close();
		delete pQFile;
	}
}
//回波调制FD
void ThreadClassGPUCalcute::GenEchoWaveFd(void *p, stSarEchoPara *pStSarEchoPara, stEchoData *pStEchoData)
{
	if (pStEchoData->pStTarInfoFd.ScareNum <= 0)
		return;
	cudaSetDevice(m_DevID);
	cudaError_t Err;
	stEchoPara	*pStEchoPara = (stEchoPara*)p;
	stTarInfoFd *pStTarInfoFd = (stTarInfoFd*)&pStEchoData->pStTarInfoFd;

	DOUBLE_2	*param = pStEchoPara->param;
	DOUBLE_2		*param2 = pStEchoPara->param2;
	int			*else_param = pStEchoPara->else_param;

	int     TarNum	= pStTarInfoFd->ScareNum;
	int     Na = pStEchoData->Na_Save;
	int     Nr		= ECHO_FFT_DOT;

	////to do
	//TarNum = 1;
	//cudaMemset(dev_Target_ALL1, 0, 1024);
	//cudaMemset(dev_Target_ALL2, 0, 1024);
	//cudaMemset(dev_Target_ALL3, 0, 1024);

	else_param[0] = TarNum;

	double *Ps_i = (double*)_Malloc(sizeof(double) * 4 * 128);

	double *dev_Ps		= (double*)_MallocCUDA(sizeof(double) * 4 * 128);
	DOUBLE_2 *dev_param = (DOUBLE_2*)_MallocCUDA(sizeof(DOUBLE_2) * 4 * 128);
	DOUBLE_2 *dev_param2	= (DOUBLE_2*)_MallocCUDA(sizeof(DOUBLE_2) * 4 * 128);
	int *dev_else_param = (int*)_MallocCUDA(sizeof(int) * 4 * 128);

	Err = cudaGetLastError();
	float *dev_ant_factor, *dev_AziAnt_factor, *dev_EleAnt_factor;
	dev_ant_factor		= (float*)_MallocCUDA(TarNum*sizeof(float)*Na);
	dev_AziAnt_factor	= (float*)_MallocCUDA(TarNum*sizeof(float)*Na);
	dev_EleAnt_factor	= (float*)_MallocCUDA(TarNum*sizeof(float)*Na);

	double *dev_Range = (double*)_MallocCUDA(TarNum*sizeof(double)*Na);
	float *dev_Range_2	= (float*)_MallocCUDA(TarNum*sizeof(float)*Na);
	float *dev_Fd		= (float*)_MallocCUDA(TarNum*sizeof(float)*Na);
	short *dev_ID		= (short*)_MallocCUDA(TarNum*sizeof(short)*Na);

	Complexf *dev_SumEchoHsys = (Complexf*)_MallocCUDA(sizeof(Complexf)*Nr* Na);
	cudaMemset(dev_SumEchoHsys, 0, sizeof(Complexf)*Nr* Na);

	Complexf*dev_SumEcho = (Complexf*)_MallocCUDA(sizeof(Complexf)*Nr* Na);//产生系统响应函数
	cudaMemset(dev_SumEcho, 0, sizeof(Complexf)*Nr* Na);

	else_param[0] = TarNum;
	cudaMemcpy(dev_else_param, else_param, 15 * sizeof(int), cudaMemcpyHostToDevice);
	cudaMemcpy(dev_param, param, 15 * sizeof(DOUBLE_2), cudaMemcpyHostToDevice);
	cudaMemcpy(dev_param2, param2, 15 * sizeof(DOUBLE_2), cudaMemcpyHostToDevice);

	Complexf *pTemp = (Complexf *)_Malloc(Nr*10 * 4);

	PLATFORM *pPlatForm = pStSarEchoPara->PlatForm;

	float *pViolate = (float*)_Malloc(sizeof(float) * 1024);
	float *dev_Violate = (float*)_MallocCUDA(sizeof(float) * 1024);
	//多个目标
	for (int i = 0; i < pStTarInfoFd->TarNum; i++)
	{
		float *dev_Target_ALL1 = pStEchoData->dev_Target_ALL1_Fd + pStTarInfoFd->pTarMoveInfo[i].offsetDot;
		float *dev_Target_ALL2 = pStEchoData->dev_Target_ALL2_Fd + pStTarInfoFd->pTarMoveInfo[i].offsetDot;
		float *dev_Target_ALL3 = pStEchoData->dev_Target_ALL3_Fd + pStTarInfoFd->pTarMoveInfo[i].offsetDot;
		float *dev_Target_ALL4 = pStEchoData->dev_Target_ALL4_Fd + pStTarInfoFd->pTarMoveInfo[i].offsetDot;
		float *dev_Target_ALL5 = pStEchoData->dev_Target_ALL5_Fd + pStTarInfoFd->pTarMoveInfo[i].offsetDot;

		else_param[0] = pStTarInfoFd->pTarMoveInfo[i].ScareDot;
		cudaMemcpy(dev_else_param, else_param, 15 * sizeof(int), cudaMemcpyHostToDevice);

		long long ScareDot = pStTarInfoFd->pTarMoveInfo[i].ScareDot;
		long long SendWaveDot	= m_TrNum;
		int CombinDot = 1;// ceil(ScareDot*SendWaveDot / (16384.f * 4));
		int PartNum		= ceil((float)SendWaveDot / CombinDot);

		unsigned short *pDotPerThread		= (unsigned short *)_Malloc(1024 * sizeof(unsigned short));
		unsigned short *dev_DotPerThread	= (unsigned short *)_MallocCUDA(1024*sizeof(unsigned short));
		for (int p = 0; p < PartNum; p++){
			pDotPerThread[p] = CombinDot;
		}
		pDotPerThread[PartNum - 1] = SendWaveDot - (PartNum - 1)*CombinDot;
		cudaMemcpy(dev_DotPerThread, pDotPerThread, sizeof(unsigned short)*PartNum, cudaMemcpyHostToDevice);
		if (0)
		{
			for (int idx = 0; idx < Na; idx++)
			{
				Err = cudaGetLastError();
				if (Err != cudaSuccess){
					qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
				}
				Ps_i[0] = pPlatForm[idx].NorthPos;
				Ps_i[1] = pPlatForm[idx].SkyPos;
				Ps_i[2] = pPlatForm[idx].EastPos;

				pViolate[0] = pPlatForm[idx].NorthVel;
				pViolate[1] = pPlatForm[idx].SkyVel;
				pViolate[2] = pPlatForm[idx].EastVel;
				pViolate[4] = pStTarInfoFd->pTarMoveInfo[i].Vx;
				pViolate[5] = pStTarInfoFd->pTarMoveInfo[i].Vy;
				pViolate[6] = pStTarInfoFd->pTarMoveInfo[i].Vz;

				//TarNum = 1;
				cudaMemcpy(dev_Ps, Ps_i, sizeof(double) * 4, cudaMemcpyHostToDevice);
				cudaMemcpy(dev_Violate, pViolate, sizeof(float) * 16, cudaMemcpyHostToDevice);
				Err = cudaGetLastError();
				if (Err != cudaSuccess){
					qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
				}
				dim3 ThreadsPerBlock(512, 1);
				dim3 BlockNum((pStTarInfoFd->pTarMoveInfo[i].ScareDot + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
				CUDA_HSYS_Sum_Fd(0, ThreadsPerBlock, BlockNum, dev_Ps, dev_param, dev_else_param, dev_Violate, dev_Violate + 4,
					dev_Target_ALL1, dev_Target_ALL2, dev_Target_ALL3, dev_Target_ALL4, dev_Target_ALL5, dev_CosValue, dev_SinValue,
					dev_SumAntennaFunction, dev_SumEchoHsys + Nr*idx, dev_Fd, dev_ID);
				Err = cudaGetLastError();
				if (Err != cudaSuccess){
					qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
				}

				float Fs_ref = 1.f / m_Fs;
				Complexf *dev_Source = dev_Signal_Send;
				//时域回波调制
				BlockNum.x = ((ScareDot*PartNum + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x);
				CUDA_EchoMode_Sum_Fd(0, ThreadsPerBlock, BlockNum, ScareDot, SendWaveDot, CombinDot, PartNum, Fs_ref, Nr, dev_DotPerThread,
					dev_CosValue, dev_SinValue, dev_SumEchoHsys + Nr*idx, dev_Fd, dev_ID, dev_Source, dev_SumEcho + Nr*idx);
				Err = cudaGetLastError();
				if (Err != cudaSuccess){
					qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << idx << "GPU:" << m_DevID;
				}

				//数据截取
				int Num = pStEchoData->Nr_Save;
				BlockNum.x = (Num + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x;
				CUDA_DataCutComplexAdd(0, ThreadsPerBlock, BlockNum, Num, ECHO_FFT_DOT, pStEchoData->Nr_Save, dev_SumEcho + Nr*idx, dev_SumEchoDataPolarFinal + Num*idx);
				cudaMemcpy(pStEchoData->SumEchoDataPolar1 + Num*idx, dev_SumEchoDataPolarFinal + Num*idx, sizeof(Complexf)*Num, cudaMemcpyDeviceToHost);
				Err = cudaGetLastError();
				if (Err != cudaSuccess){
					qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_DevID;
				}
			}


		}
		else
		{
			int cntPos = 0, cntV = 0,cntPlat = 0;
			for (int idx = 0; idx < Na; idx++)
			{
				Ps_i[cntPos++] = pPlatForm[idx].NorthPos;
				Ps_i[cntPos++] = pPlatForm[idx].SkyPos;
				Ps_i[cntPos++] = pPlatForm[idx].EastPos;
				cntPos++;
				pViolate[cntPlat++] = pPlatForm[idx].NorthVel;
				pViolate[cntPlat++] = pPlatForm[idx].SkyVel;
				pViolate[cntPlat++] = pPlatForm[idx].EastVel;
				cntPlat++;
				pViolate[512 + cntV++] = pStTarInfoFd->pTarMoveInfo[i].Vx;
				pViolate[512 + cntV++] = pStTarInfoFd->pTarMoveInfo[i].Vy;
				pViolate[512 + cntV++] = pStTarInfoFd->pTarMoveInfo[i].Vz;
				cntV++;
			}

			cudaMemcpy(dev_Ps, Ps_i, sizeof(double) * 4 * Na, cudaMemcpyHostToDevice);
			cudaMemcpy(dev_Violate, pViolate, sizeof(float) * 1024, cudaMemcpyHostToDevice);
			Err = cudaGetLastError();
			if (Err != cudaSuccess){
				qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err<< "GPU:" << m_DevID;
			}
			dim3 ThreadsPerBlock(512, 1);
			dim3 BlockNum((pStTarInfoFd->pTarMoveInfo[i].ScareDot + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
			CUDA_HSYS_Sum_Fd(0, ThreadsPerBlock, BlockNum, (int)ScareDot, Na, dev_Ps, dev_param, dev_else_param, dev_Violate, dev_Violate + 512,
				dev_Target_ALL1, dev_Target_ALL2, dev_Target_ALL3, dev_Target_ALL4, dev_Target_ALL5, dev_CosValue, dev_SinValue,
				dev_SumAntennaFunction, dev_SumEchoHsys, dev_Fd, dev_ID);
			Err = cudaGetLastError();
			if (Err != cudaSuccess){
				qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_DevID;
			}

			float Fs_ref = 1.f / m_Fs;
			Complexf *dev_Source = dev_Signal_Send;
			//时域回波调制
			BlockNum.x = ((ScareDot*SendWaveDot + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x);
			CUDA_EchoMode_Sum_Fd(0, ThreadsPerBlock, BlockNum, ScareDot, SendWaveDot, Na, Fs_ref, Nr, dev_CosValue, dev_SinValue, dev_SumEchoHsys, dev_Fd, dev_ID, dev_Source, dev_SumEcho);
			Err = cudaGetLastError();
			if (Err != cudaSuccess){
				qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_DevID;
			}

			//数据截取
			int Num = pStEchoData->Nr_Save;
			BlockNum.x = (Num + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x;
			CUDA_DataCutComplexAdd(0, ThreadsPerBlock, BlockNum, Num, ECHO_FFT_DOT, pStEchoData->Nr_Save, Na,dev_SumEcho, dev_SumEchoDataPolarFinal);
			cudaMemcpy(pStEchoData->SumEchoDataPolar1, dev_SumEchoDataPolarFinal, sizeof(Complexf)*Num*Na, cudaMemcpyDeviceToHost);
			Err = cudaGetLastError();
			if (Err != cudaSuccess){
				qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_DevID;
			}
		}
		
	}
	if (m_DevID == 8)
	{
		cudaMemcpy(pTemp, dev_SumEchoDataPolarFinal + pStEchoData->Nr_Save*(Na - 1), sizeof(Complexf)*pStEchoData->Nr_Save, cudaMemcpyDeviceToHost);
		QFile *pQFile = new QFile;
		pQFile->setFileName("..\\..\\..\\data\\EchoDMC3");
		pQFile->open(QFile::WriteOnly);
		pQFile->write((char*)pTemp, sizeof(Complexf) *pStEchoData->Nr_Save);
		pQFile->close();
		delete pQFile;
	}
}

//目标回波生成
void ThreadClassGPUCalcute::slotTarEchoDateGen(int Dev,void *p,void *p2)
{
    if(m_Dev != Dev){
        return;
    }
	_Release_Malloc();      //初始化伪内存池
	_Release_MallocCUDA();
	cudaSetDevice(m_Dev);
	cudaEvent_t e_start, e_stop;
	cudaError_t ret = cudaEventCreate(&e_start);
	ret = cudaEventCreate(&e_stop);
	cudaEventRecord(e_start, 0);

	stResourceScheduingPara *pStResourceScheduingPara = (stResourceScheduingPara*)p2;
	stDYTExternPara *pStDYTExternPara = (stDYTExternPara*)p;
	int TarNum			= pStDYTExternPara->TargetNum;
	int pulseStartIndex = pStResourceScheduingPara->pulseStartIndex;

	DYTPara *pDYTPara = (DYTPara*)&pStDYTExternPara->mDYTPara;
	stSarEchoPara *pStSarEchoPara = (stSarEchoPara*)_MallocMemObj(stSarEchoPara);
	pStSarEchoPara->ParamR->Amp = pDYTPara->DYTPt;			//发射信号功率
	pStSarEchoPara->ParamR->Fc	= pDYTPara->DYTFc;			//载频
	pStSarEchoPara->ParamR->F0	= 0;						//中频
	pStSarEchoPara->ParamR->Prf = 1.0 / pDYTPara->DYTPRT;	//脉冲重复频率
	pStSarEchoPara->ParamR->Fs	= pDYTPara->DYTFs;			//采样率
	pStSarEchoPara->ParamR->Br	= pDYTPara->DYTBw;			//带宽
	pStSarEchoPara->ParamR->Tp	= pDYTPara->DYTPw;			//时宽
	pStSarEchoPara->ParamR->SignalType = pDYTPara->DYTType;
	pStSarEchoPara->ParamR->Na	= pDYTPara->DYTPulseNum;
	pStSarEchoPara->ParamR->Nr = 16384;
	pStSarEchoPara->AntParam->AziAngle = pDYTPara->Azimuth;
	pStSarEchoPara->AntParam->PitAngle = pDYTPara->Pitch;
	pStSarEchoPara->AntParam->Main3dBlobeWidth[0] = pDYTPara->BeamWidth;
	pStSarEchoPara->AntParam->Main3dBlobeWidth[1] = pDYTPara->BeamWidth;
	m_AntParam->AziAngle = pDYTPara->Azimuth;
	m_AntParam->PitAngle = pDYTPara->Pitch;

	memcpy(pStSarEchoPara->pTarget, pStDYTExternPara->mTarget, sizeof(Target)*pStDYTExternPara->TargetNum);

	double accX = pDYTPara->pDYT_MovePara[0].DYTRadarAccX;
	double accY = pDYTPara->pDYT_MovePara[0].DYTRadarAccY;
	double accZ = pDYTPara->pDYT_MovePara[0].DYTRadarAccZ;

	double vX = pDYTPara->pDYT_MovePara[0].DYTRadarVX;
	double vY = pDYTPara->pDYT_MovePara[0].DYTRadarVY;
	double vZ = pDYTPara->pDYT_MovePara[0].DYTRadarVZ;

	float sum_PRT = pulseStartIndex*pDYTPara->DYTPRT;
	for (int j = pulseStartIndex; j < pDYTPara->DYTPulseNum + pulseStartIndex; j++)
	{
		pStSarEchoPara->PlatForm[j - pulseStartIndex].NorthPos	= pDYTPara->pDYT_MovePara[0].DYTRadarX + 0.5*accX*sum_PRT*sum_PRT + vX*sum_PRT;
		pStSarEchoPara->PlatForm[j - pulseStartIndex].SkyPos	= pDYTPara->pDYT_MovePara[0].DYTRadarY + 0.5*accY*sum_PRT*sum_PRT + vY*sum_PRT;
		pStSarEchoPara->PlatForm[j - pulseStartIndex].EastPos	= pDYTPara->pDYT_MovePara[0].DYTRadarZ + 0.5*accZ*sum_PRT*sum_PRT + vZ*sum_PRT;
		pStSarEchoPara->PlatForm[j - pulseStartIndex].NorthVel	= pDYTPara->pDYT_MovePara[0].DYTRadarVX + accX*sum_PRT;
		pStSarEchoPara->PlatForm[j - pulseStartIndex].EastVel	= pDYTPara->pDYT_MovePara[0].DYTRadarVY + accY*sum_PRT;
		pStSarEchoPara->PlatForm[j - pulseStartIndex].SkyVel	= pDYTPara->pDYT_MovePara[0].DYTRadarVZ + accZ*sum_PRT;
		pStSarEchoPara->PlatForm[j - pulseStartIndex].NorthAcc	= accX;
		pStSarEchoPara->PlatForm[j - pulseStartIndex].SkyAcc	= accY;
		pStSarEchoPara->PlatForm[j - pulseStartIndex].EastAcc	= accZ;
		sum_PRT += pDYTPara->DYTPRT;
	}

	pStSarEchoPara->SimData.SaveFlie_Nr = pDYTPara->WaveGateWidth;
	pStSarEchoPara->SimData.Rmin		= pDYTPara->DYTRmin;
	pStSarEchoPara->SimData.AziEleFalg	= pDYTPara->AziEleFlag;
	pStSarEchoPara->SimData.SenceTarNum = pStDYTExternPara->TargetNum;
	pStSarEchoPara->RADARNo = pStDYTExternPara->mDYTPara.DYTNo;

	//发射信号生成
	int FdFlag = 0; //0=卷积方式调制 1=散射点叠加方式
	if (abs(m_Fs - pStSarEchoPara->ParamR->Fs) > 1 || abs(m_Br - pStSarEchoPara->ParamR->Br) > 1 || abs(m_Tp*1e6 - pStSarEchoPara->ParamR->Tp*1e6) > 0.1f)
	{
		Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) *  pStSarEchoPara->ParamR->Nr);
		memset(BaseSignal, 0, sizeof(Complexf) *  pStSarEchoPara->ParamR->Nr);
		m_TrNum = (int)(pStSarEchoPara->ParamR->Fs* pStSarEchoPara->ParamR->Tp + 0.5);		//一个脉宽的点数
		pStSarEchoPara->ParamR->Kr = pStSarEchoPara->ParamR->Br / pStSarEchoPara->ParamR->Tp;
		ThreadClassSendWaveGen::Instance()->TransSignalSim(pStSarEchoPara->ParamR, &pStSarEchoPara->SimData, m_TrNum, BaseSignal);
		m_Fs = pStSarEchoPara->ParamR->Fs; m_Br = pStSarEchoPara->ParamR->Br; m_Tp = pStSarEchoPara->ParamR->Tp;
		cudaMemset(dev_Signal, 0, pStSarEchoPara->ParamR->Nr*sizeof(Complexf));
		cudaMemcpy(dev_Signal, BaseSignal, m_TrNum*sizeof(Complexf), cudaMemcpyHostToDevice);
		cudaMemset(dev_Signal_Send, 0, pStSarEchoPara->ParamR->Nr*sizeof(Complexf));
		cudaMemcpy(dev_Signal_Send, dev_Signal, m_TrNum*sizeof(Complexf), cudaMemcpyDeviceToDevice);
		cufftResult ret;
		if (FdFlag == 0)
			ret = cufftExecC2C(m_planEchoCvntModu[0], (cufftComplex*)dev_Signal, (cufftComplex*)dev_Signal, CUFFT_FORWARD);
		ret = ret;
	}

	//目标
	stEchoData *pStEchoData = (stEchoData*)_MallocMemObj(stEchoData); pStEchoData->init();
	cudaMemset(dev_SumEchoDataPolar1, 0, sizeof(Complexf)*pDYTPara->DYTPulseNum * ECHO_FFT_DOT);
	cudaMemset(dev_SumEchoDataPolarFinal, 0, sizeof(Complexf)*pDYTPara->DYTPulseNum * ECHO_FFT_DOT);
	if (pDYTPara->AziEleFlag > 0)
	{
		cudaMemset(dev_AziEchoDataPolar1, 0, sizeof(Complexf)*pDYTPara->DYTPulseNum * ECHO_FFT_DOT);
		cudaMemset(dev_EleEchoDataPolar1, 0, sizeof(Complexf)*pDYTPara->DYTPulseNum * ECHO_FFT_DOT);
		cudaMemset(dev_ExEchoDataPolar1, 0, sizeof(Complexf)*pDYTPara->DYTPulseNum * ECHO_FFT_DOT);
		cudaMemset(dev_AziEchoDataPolarFinal, 0, sizeof(Complexf)*pDYTPara->DYTPulseNum * ECHO_FFT_DOT);
		cudaMemset(dev_EleEchoDataPolarFinal, 0, sizeof(Complexf)*pDYTPara->DYTPulseNum * ECHO_FFT_DOT);
		cudaMemset(dev_ExEchoDataPolarFinal, 0, sizeof(Complexf)*pDYTPara->DYTPulseNum * ECHO_FFT_DOT);
	}

	pStEchoData->dev_SumEchoDataPolar1	= dev_SumEchoDataPolar1;
	pStEchoData->dev_AziEchoDataPolar1	= dev_AziEchoDataPolar1;
	pStEchoData->dev_EleEchoDataPolar1	= dev_EleEchoDataPolar1;
	pStEchoData->dev_ExEchoDataPolar1	= dev_ExEchoDataPolar1;

	pStEchoData->dev_SumEchoDataPolarFinal	= dev_SumEchoDataPolarFinal;
	pStEchoData->dev_AziEchoDataPolarFinal	= dev_AziEchoDataPolarFinal;
	pStEchoData->dev_EleEchoDataPolarFinal	= dev_EleEchoDataPolarFinal;
	pStEchoData->dev_ExEchoDataPolarFinal	= dev_ExEchoDataPolarFinal;

    pStEchoData->dev_Target_ALL1 = dev_Target_ALL1;//x
    pStEchoData->dev_Target_ALL2 = dev_Target_ALL2;//y
    pStEchoData->dev_Target_ALL3 = dev_Target_ALL3;//z
    pStEchoData->dev_Target_ALL4 = dev_Target_ALL4;//amp
    pStEchoData->dev_Target_ALL5 = dev_Target_ALL5;//phi
	pStEchoData->dev_Target_ALL1_Fd = dev_Target_ALL1 + 2048*2048;//x
	pStEchoData->dev_Target_ALL2_Fd = dev_Target_ALL2 + 2048*2048;//y
	pStEchoData->dev_Target_ALL3_Fd = dev_Target_ALL3 + 2048*2048;//z
	pStEchoData->dev_Target_ALL4_Fd = dev_Target_ALL4 + 2048*2048;//amp
	pStEchoData->dev_Target_ALL5_Fd = dev_Target_ALL5 + 2048*2048;//phi

	memcpy(&pStEchoData->SimData, &pStSarEchoPara->SimData, sizeof(SimStruct));
	
	pStEchoData->FrameSerial		= pStResourceScheduingPara->FrameSerial;		//大帧流水号
	pStEchoData->childFrameSerial	= pStResourceScheduingPara->childFrameSerial;	//子帧编号 从1开始编号
	pStEchoData->childFrameNum		= pStResourceScheduingPara->childFrameNum;		//子帧总数
	pStEchoData->Nr_Save			= pStResourceScheduingPara->WaveGateWidth;		//回波点数
	pStEchoData->Na_Save			= pDYTPara->DYTPulseNum;						//回波个数
	pStEchoData->Rmin				= pDYTPara->DYTRmin;//
	pStEchoData->Fs					= pDYTPara->DYTFs;
	pStEchoData->RADARNo			= pDYTPara->DYTNo;
	pStEchoData->curMSecsSinceEpoch = pStResourceScheduingPara->curMSecsSinceEpoch;
	pStEchoData->SacreNum			= 0;
	pStEchoData->AziEleFlag			= pDYTPara->AziEleFlag;
	pStEchoData->FdFlag				= 0;	//1=调制脉内多普勒  0=不调制脉内多普勒

	cudaError_t Err = cudaGetLastError();
    ////目标
	if (pStDYTExternPara->TargetNum > 0)
	{
		pThreadClassSarEcho->GenSenceEcho(pStSarEchoPara, pStEchoData);
	}
	////角反
	pStSarEchoPara->CorRefNum = pStDYTExternPara->CorRefNum;
	if (pStDYTExternPara->CorRefNum > 0)
	{
		for (int i = 0; i < pStDYTExternPara->CorRefNum; i++)//角反
		{
			pStSarEchoPara->pCorRef[i].ModeNo = 0;
			pStSarEchoPara->pCorRef[i].TargetX = pStDYTExternPara->mCorRef[i].CorRefX;
			pStSarEchoPara->pCorRef[i].TargetY = pStDYTExternPara->mCorRef[i].CorRefY;
			pStSarEchoPara->pCorRef[i].TargetZ = pStDYTExternPara->mCorRef[i].CorRefZ;
		}
		pThreadClassJFEcho->GenSenceEcho(pStSarEchoPara, pStEchoData);
	}
    ////面目标杂波
	pStSarEchoPara->SeaStateClass = pStDYTExternPara->SeaStateClass;
	if (pStSarEchoPara->SeaStateClass > 0)
	{
		pStSarEchoPara->SeaMoveX = pStDYTExternPara->SeaMoveX;
		pStSarEchoPara->SeaMoveZ = pStDYTExternPara->SeaMoveZ;
		pThreadClassSarEchoClutter->GenSenceEcho(pStSarEchoPara, pStEchoData);
	}
	////箔条
	_stChaffEchoPara *pstChaffEchoPara = (_stChaffEchoPara*)_MallocMemObj(_stChaffEchoPara);
	pstChaffEchoPara->Jammer->ChaffNum = pStDYTExternPara->ChaffNum;
	if (pStDYTExternPara->ChaffNum > 0)
	{
		for (int i = 0; i < pStDYTExternPara->ChaffNum; i++)//箔条
		{
			pstChaffEchoPara->Jammer[i].ChaffPos[0] = pStDYTExternPara->mChaff[i].ChaffX;
			pstChaffEchoPara->Jammer[i].ChaffPos[1] = pStDYTExternPara->mChaff[i].ChaffY;
			pstChaffEchoPara->Jammer[i].ChaffPos[2] = pStDYTExternPara->mChaff[i].ChaffZ;
			pstChaffEchoPara->Jammer[i].ChaffVelf[0] = pStDYTExternPara->mChaff[i].WindVX;
			pstChaffEchoPara->Jammer[i].ChaffVelf[1] = pStDYTExternPara->mChaff[i].WindVY;
			pstChaffEchoPara->Jammer[i].ChaffVelf[2] = pStDYTExternPara->mChaff[i].WindVZ;
			pstChaffEchoPara->Jammer[i].Chafftime = pStDYTExternPara->mChaff[i].ChaffTime;

		}
		memcpy(pstChaffEchoPara->PlatForm, pStSarEchoPara->PlatForm, sizeof(PLATFORM)*pDYTPara->DYTPulseNum);
		memcpy(pstChaffEchoPara->AntParam, pStSarEchoPara->AntParam, sizeof(ANTPARAM));
		memcpy(pstChaffEchoPara->ParamR, pStSarEchoPara->ParamR, sizeof(RADAR));
		memcpy(&pstChaffEchoPara->SimData, &pStSarEchoPara->SimData, sizeof(SimStruct));
		pThreadClassChaffEcho->GenChaffEcho(pstChaffEchoPara, pStEchoData);
	}
	_ReleaseMemObj(_stChaffEchoPara, pstChaffEchoPara);

	//回波调制
	UpdateCalcutePara(pStSarEchoPara->ParamR, m_AntParam, &pStSarEchoPara->SimData);
	if (pStEchoData->AziEleFlag == 0){
		GenEchoWave(pStEchoPara, pStSarEchoPara, pStEchoData);
		//GenEchoWaveFd(pStEchoPara, pStSarEchoPara, pStEchoData);
	}	
	else
		GenEchoWaveHCC(pStEchoPara, pStSarEchoPara, pStEchoData);

	memcpy(&pStEchoData->mDYTPara, &pStDYTExternPara->mDYTPara, sizeof(DYTPara));
	memcpy(&pStEchoData->PlatForm, pStSarEchoPara->PlatForm, sizeof(PLATFORM)*pDYTPara->DYTPulseNum);

	ret = cudaEventRecord(e_stop, 0);
	ret = cudaEventSynchronize(e_stop);
	float elapsedTime = 0;
	ret = cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
	qint64 cur2 = QDateTime::currentMSecsSinceEpoch();
	//qDebug() << "GPU:" << m_DevID << "==============================================================================  EchoWAve Cost Time is  " << (elapsedTime) << " ms "<< cur2;

	_ReleaseMemObj(stResourceScheduingPara,pStResourceScheduingPara);
	_ReleaseMemObj(stDYTExternPara, pStDYTExternPara);
	_ReleaseMemObj(stSarEchoPara, pStSarEchoPara);
	
	pStEchoData->SumEchoData1Dot = pStEchoData->Nr_Save;
	if (pStEchoData->AziEleFlag > 0){
		pStEchoData->AziEchoData1Dot = pStEchoData->Nr_Save;
		pStEchoData->EleEchoData1Dot = pStEchoData->Nr_Save;
	}
	emit sendEchoScheduling(pStEchoData);
	
}
//回波卷积调制
void ThreadClassGPUCalcute::EchoCovtModu(stEchoData *pStEchoData, Complexf *dev_Signal, float *dev_fd)
{
	int k = 0;
	cudaError_t Err = cudaGetLastError();

	int Nr = ECHO_FFT_DOT;
	//频域发射信号
	cufftHandle plan = m_planEchoCvntModu[pStEchoData->Na_Save - 1];
	cufftExecC2C(plan, (cufftComplex*)pStEchoData->dev_SumEchoDataPolar1, (cufftComplex*)pStEchoData->dev_SumEchoDataPolar1, CUFFT_FORWARD);
	if (pStEchoData->SimData.AziEleFalg > 0)
	{
		cufftExecC2C(plan, (cufftComplex*)pStEchoData->dev_AziEchoDataPolar1, (cufftComplex*)pStEchoData->dev_AziEchoDataPolar1, CUFFT_FORWARD);
		cufftExecC2C(plan, (cufftComplex*)pStEchoData->dev_EleEchoDataPolar1, (cufftComplex*)pStEchoData->dev_EleEchoDataPolar1, CUFFT_FORWARD);
		cufftExecC2C(plan, (cufftComplex*)pStEchoData->dev_ExEchoDataPolar1, (cufftComplex*)pStEchoData->dev_ExEchoDataPolar1, CUFFT_FORWARD);
	}
	Err = cudaGetLastError();
	if (Err != cudaSuccess) {
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_DevID;
	}
	dim3 ThreadsPerBlock(512, 1);
	dim3 BlockNum((Nr*pStEchoData->Na_Save + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
	CUDA_ComplexMutipleSend(0, ThreadsPerBlock, BlockNum, Nr*pStEchoData->Na_Save, Nr, (Complexf*)dev_Signal, (Complexf*)pStEchoData->dev_SumEchoDataPolar1, pStEchoData->dev_SumEchoDataPolar1, Nr);
	if (pStEchoData->SimData.AziEleFalg > 0)
	{
		CUDA_ComplexMutipleSend(0, ThreadsPerBlock, BlockNum, Nr*pStEchoData->Na_Save, Nr, (Complexf*)dev_Signal, (Complexf*)pStEchoData->dev_AziEchoDataPolar1, pStEchoData->dev_AziEchoDataPolar1, Nr);
		CUDA_ComplexMutipleSend(0, ThreadsPerBlock, BlockNum, Nr*pStEchoData->Na_Save, Nr, (Complexf*)dev_Signal, (Complexf*)pStEchoData->dev_EleEchoDataPolar1, pStEchoData->dev_EleEchoDataPolar1, Nr);
		CUDA_ComplexMutipleSend(0, ThreadsPerBlock, BlockNum, Nr*pStEchoData->Na_Save, Nr, (Complexf*)dev_Signal, (Complexf*)pStEchoData->dev_ExEchoDataPolar1, pStEchoData->dev_ExEchoDataPolar1, Nr);
	}
	cufftExecC2C(plan, (cufftComplex*)pStEchoData->dev_SumEchoDataPolar1, (cufftComplex*)pStEchoData->dev_SumEchoDataPolar1, CUFFT_INVERSE);
	if (pStEchoData->SimData.AziEleFalg > 0)
	{
		cufftExecC2C(plan, (cufftComplex*)pStEchoData->dev_AziEchoDataPolar1, (cufftComplex*)pStEchoData->dev_AziEchoDataPolar1, CUFFT_INVERSE);
		cufftExecC2C(plan, (cufftComplex*)pStEchoData->dev_EleEchoDataPolar1, (cufftComplex*)pStEchoData->dev_EleEchoDataPolar1, CUFFT_INVERSE);
		cufftExecC2C(plan, (cufftComplex*)pStEchoData->dev_ExEchoDataPolar1, (cufftComplex*)pStEchoData->dev_ExEchoDataPolar1, CUFFT_INVERSE);
	}
	//调制脉内多普勒
	if (dev_fd != nullptr)
	{
		int PRTNum = pStEchoData->PRT*pStEchoData->Fs;
		CUDA_Fd_Calcute(0, ThreadsPerBlock, BlockNum, pStEchoData->Na_Save*Nr, Nr, PRTNum, dev_fd, dev_CosValue, dev_SinValue, pStEchoData->dev_SumEchoDataPolar1, pStEchoData->dev_SumEchoDataPolar1);
		Err = cudaGetLastError();
		if (Err != cudaSuccess) {
			qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_DevID;
		}
		if (pStEchoData->SimData.AziEleFalg > 0)
		{
			CUDA_Fd_Calcute(0, ThreadsPerBlock, BlockNum, pStEchoData->Na_Save*Nr, Nr, PRTNum, dev_fd, dev_CosValue, dev_SinValue, pStEchoData->dev_AziEchoDataPolar1, pStEchoData->dev_AziEchoDataPolar1);
			CUDA_Fd_Calcute(0, ThreadsPerBlock, BlockNum, pStEchoData->Na_Save*Nr, Nr, PRTNum, dev_fd, dev_CosValue, dev_SinValue, pStEchoData->dev_EleEchoDataPolar1, pStEchoData->dev_EleEchoDataPolar1);
			CUDA_Fd_Calcute(0, ThreadsPerBlock, BlockNum, pStEchoData->Na_Save*Nr, Nr, PRTNum, dev_fd, dev_CosValue, dev_SinValue, pStEchoData->dev_ExEchoDataPolar1, pStEchoData->dev_ExEchoDataPolar1);
		}
	}

	//数据截取
	int Num = pStEchoData->Na_Save*pStEchoData->Nr_Save;
	BlockNum.x = (Num + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x;
	CUDA_DataCutComplexAdd(0, ThreadsPerBlock, BlockNum, Num, ECHO_FFT_DOT, pStEchoData->Nr_Save, pStEchoData->dev_SumEchoDataPolar1, pStEchoData->dev_SumEchoDataPolarFinal);
	cudaMemset(pStEchoData->dev_SumEchoDataPolar1, 0, sizeof(Complexf)*ECHO_FFT_DOT*pStEchoData->Na_Save);
	if (pStEchoData->SimData.AziEleFalg > 0)
	{
		CUDA_DataCutComplexAdd(0, ThreadsPerBlock, BlockNum, Num, ECHO_FFT_DOT, pStEchoData->Nr_Save, pStEchoData->dev_AziEchoDataPolar1, pStEchoData->dev_AziEchoDataPolarFinal);
		CUDA_DataCutComplexAdd(0, ThreadsPerBlock, BlockNum, Num, ECHO_FFT_DOT, pStEchoData->Nr_Save, pStEchoData->dev_EleEchoDataPolar1, pStEchoData->dev_EleEchoDataPolarFinal);
		CUDA_DataCutComplexAdd(0, ThreadsPerBlock, BlockNum, Num, ECHO_FFT_DOT, pStEchoData->Nr_Save, pStEchoData->dev_ExEchoDataPolar1, pStEchoData->dev_ExEchoDataPolarFinal);

		cudaMemset(pStEchoData->dev_AziEchoDataPolar1, 0, sizeof(Complexf)*ECHO_FFT_DOT*pStEchoData->Na_Save);
		cudaMemset(pStEchoData->dev_EleEchoDataPolar1, 0, sizeof(Complexf)*ECHO_FFT_DOT*pStEchoData->Na_Save);
		cudaMemset(pStEchoData->dev_ExEchoDataPolar1, 0, sizeof(Complexf)*ECHO_FFT_DOT*pStEchoData->Na_Save);
	}
	Err = cudaGetLastError();
	if (Err != cudaSuccess) {
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_DevID;
	}

	//数据拷贝
	cudaMemcpy(pStEchoData->SumEchoDataPolar1, dev_SumEchoDataPolarFinal, sizeof(Complexf)*Num, cudaMemcpyDeviceToHost);
	if (pStEchoData->SimData.AziEleFalg > 0)
	{
		cudaMemcpy(pStEchoData->AziEchoDataPolar1, dev_AziEchoDataPolarFinal, sizeof(Complexf)*Num, cudaMemcpyDeviceToHost);
		cudaMemcpy(pStEchoData->EleEchoDataPolar1, dev_EleEchoDataPolarFinal, sizeof(Complexf)*Num, cudaMemcpyDeviceToHost);
		//cudaMemcpy(pStEchoData->ExEchoDataPolar1, dev_ExEchoDataPolarFinal, sizeof(Complexf)*Num, cudaMemcpyDeviceToHost);
	}
	Err = cudaGetLastError();
	if (Err != cudaSuccess) {
		qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_Dev;
	}

}
void ThreadClassGPUCalcute::slotEchoGPUCalcute(void *p)
{
	emit sendEchoScheduling(p);
}

void ThreadClassGPUCalcute::slotASKWaveDate()
{
    emit sendASKWaveDate();
}

void ThreadClassGPUCalcute::slotWaveShow(void *p)
{
    emit sendWaveShow(p);
}
void ThreadClassGPUCalcute::slotSettingSarSence(void *p)
{

}
void ThreadClassGPUCalcute::slotSettingAntPara(void *p, void *pc)
{
	SettingAntPara(p);
	pThreadClassBlanketJamGen->slotSettingAntPara(p);
	pThreadClassDeceptionJamGen->slotSettingAntPara(p);
}
void ThreadClassGPUCalcute::slotSettingClutter(void *p, void *pc)
{
	
}

