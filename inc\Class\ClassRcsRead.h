/*******************************************************************************************
 * FileProperties: 
 *     FileName: ClassRcsRead.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Class/ClassRcsRead.h $
 *         $Author: weiyingzhen $
 *         $Revision: 503 $
 *         $Date: 2025-05-27 10:41:15 $
*******************************************************************************************/
#ifndef CLASSRCSREAD_H
#define CLASSRCSREAD_H

#include<QFile>

#include "_stumempool.h"
#include"globalRadarStruct.h"
#include"globalExternalStruct.h"

#define MAX_TAR_RCS_DOT 8192

#pragma pack(1)//1字节对齐
typedef struct {
    float x;
    float y;
    float z;
    float rcs;
    float phi;
}ModPhara;
typedef struct {
	float x[MAX_TAR_RCS_DOT + 32];
	float y[MAX_TAR_RCS_DOT + 32];
	float z[MAX_TAR_RCS_DOT + 32];
	float rcs[MAX_TAR_RCS_DOT + 32];
	float phi[MAX_TAR_RCS_DOT + 32];
} MissileData;
typedef struct {
	float *x;
	float *y;
	float *z;
	float *rcs;
	float *phi;
} MissileDataGPU;

typedef struct {
	float MaxX;
	float MinX;
	float MaxZ;
	float MinZ;
}ClutterRoeData;

//方位链表
typedef struct _stTarAziLink
{
    int iNodeCount;
	int		pointNum;//散射点数
	float	azi;
	float	pit;
    MissileData *pMissileData;
	MissileDataGPU pMissileDataGPU;
    _stTarAziLink *prev;
    _stTarAziLink *next;
    _stTarAziLink *last;
public:
    void init() { memset(this, 0, sizeof(_stTarAziLink)); prev = last = this; next = NULL; }
}stTarAziLink;
//俯仰链表
typedef struct _stTarPitLink
{
    int		iNodeCount;
	int		iTarNo;			//目标编号
	char	cTarName[128];	//目标名称

	float	pitStep;		//俯仰向角度间隔
	float	aziStep;		//方位向角度间隔

    stTarAziLink *pStTarAziLink;

    _stTarPitLink *prev;
    _stTarPitLink *next;
    _stTarPitLink *last;
public:
    void init() { memset(this, 0, sizeof(_stTarPitLink)); prev = last = this; next = NULL; }
}stTarPitLink;

//目标链表
typedef struct _stTarLink
{
    int		iNodeCount;
    int		iTarNo;			//目标编号
    char	cTarName[128];	//目标名称

    stTarPitLink *pStTarPitLink;

    _stTarLink *prev;
    _stTarLink *next;
    _stTarLink *last;
public:
    void init() { memset(this, 0, sizeof(_stTarLink)); prev = last = this; next = NULL; }
}stTarLink;

typedef struct _stTarClutterLink
{
	int		iNodeCount;
	int		iTarNo;			//目标编号
	ClutterRoeData *pClutterRoeData;
	_stTarClutterLink *prev;
	_stTarClutterLink *next;
	_stTarClutterLink *last;
public:
	void init() { memset(this, 0, sizeof(_stTarClutterLink)); prev = last = this; next = NULL; }
}stTarClutterLink;

#pragma pack()//1字节对齐

class ClassRcsRead : public _StuMemPool
{

public:
    explicit ClassRcsRead();
    ~ClassRcsRead();
    


private:
    void extractNumbers(char* filename3, double &pit, double &azi);
	

public:
    stTarPitLink* readModPhara(char* filename2);//根据RCS模型文件夹路径提取该目标所有角度的散射系数

	stTarAziLink* CalTarRcs(float pit, float azi, stTarPitLink* pSt);
	void WriteRCS2File(int AngleNum, stTarPitLink *pStTarPitLinkHead, char *pFilePath);
	stTarPitLink* ReadRCS_File2Link(char *pFilePath);
	
};

#endif // CLASSRCSREAD_H
