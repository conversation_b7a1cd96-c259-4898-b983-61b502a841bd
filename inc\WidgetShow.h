/*******************************************************************************************
 * FileProperties: 
 *     FileName: WidgetShow.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/WidgetShow.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef WIDGETSHOW_H
#define WIDGETSHOW_H

#include <QWidget>
#include"GLWidgetWave.h"

namespace Ui {
class WidgetShow;
}

class WidgetShow : public QWidget
{
    Q_OBJECT

public:
    explicit WidgetShow(QWidget *parent = 0);
    ~WidgetShow();

signals:
    void sendASKWaveDate();
public slots:
    void slotWaveShow(void *p);


private slots:
    void on_pushButtonRefresh_clicked();



public:
    GLWidgetWave *pGLWidgetWaveSend;
    GLWidgetWave *pGLWidgetWaveEcho;

private:
    Ui::WidgetShow *ui;
};

#endif // WIDGETSHOW_H
