/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassChaffEcho.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassChaffEcho_0109.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef THREADCLASSCHAFFECHO
#define THREADCLASSCHAFFECHO


#include <QObject>

#include<curand.h>
#include<cufft.h>

#include"MyMalloc.h"
#include"MyMallocCUDA.h"
#include"_stumempool.h"
#include"ClassRcsRead.h"
#include"ClassCoorDinate.h"
#include "KernelChaff.h"

#include "WhiteCalGlobel.h"
#pragma pack(1)//1字节对齐
typedef struct _stDateStruct1
{
	float pBuffer[10000];
}stDateStruct1;
typedef struct ChaffRead
{
	float    Num;
	float	*Chafft;
	float	*ChaffX;			//箔条中心位置x
	float	*ChaffY;			//箔条中心位置y
	float	*ChaffZ;			//箔条中心位置z
	float	*ChaffRcs;		//箔条扩散时间
	float	*ChaffAzi;			//风速x
	float	*ChaffReal;			//风速y
	float	*ChaffImag;			//风速z 
}ChaffRead;//箔条


typedef struct {
	float *x;
	float *y;
	float *z;
	float *rcs;
	float *phi;
} ChaffData;
//链表
typedef struct _stChaffLink
{
	int		iNodeCount;
	float		timeNo;
	float		pointNum;//散射点数
	ChaffData pChaffData;
	_stChaffLink *prev;
	_stChaffLink *next;
	_stChaffLink *last;
public:
	void init() { memset(this, 0, sizeof(_stChaffLink)); prev = last = this; next = NULL; }
}stChaffLink;

typedef struct _stChaffTimeLink
{
	int		iNodeCount;
	float timeNo;//时间编号
	stChaffLink *pstChaffLink;
	_stChaffTimeLink *prev;
	_stChaffTimeLink *next;
	_stChaffTimeLink *last;
public:
	void init() { memset(this, 0, sizeof(_stChaffTimeLink)); prev = last = this; next = NULL; }
}stChaffTimeLink;

#pragma pack()//1字节对齐



class ThreadClassChaffEcho : public QObject, MyMalloc, MyMallocCUDA, ClassRcsRead
{
    Q_OBJECT
public:
    explicit ThreadClassChaffEcho(int DevID = -1, int buffer_size = 0, char *d_Buffer = nullptr, QObject *parent = 0);

    ~ThreadClassChaffEcho();
	void InitChaffRcsPara(void *p);
public:
    //初始化GPU设备工作参数
	bool InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic);
    //初始化cuda参数
    bool InitCUDAPara();
    //产生发射信号
    void RadarSendWaveGen(RADAR *ParamR,Complex *BaseSignal);

    template <class T, class T2, class T3, class T4, class T5>
	void GenDMCChaffEcho(JAM_PARAM *state, PLATFORM *pPlatForm , RADAR *ParamR, ANTPARAM *AntParam, SimStruct *SimData, T *RadarAPC, T2 *RadarVel, T3 *TargetAll, T4 *BaseSignal,
        T5 *SumCluDataPolar1, T5 *AziCluDataPolar1, T5 *EleCluDataPolar1, T5 *ExCluDataPolar1);
	stChaffTimeLink* readChaffRCSFile(char *path);
	stChaffLink* CalChaffRcs(float time, stChaffTimeLink* pSt);
	
    void GenChaffEcho(void *p, stEchoData *pStEchoData);

	bool InitCUDACullterPara();
signals:
    void sendEchoDataFormat(void *p);
    void sendEchoScheduling(void *p);
    void sendWaveShow(void *p);

public slots:
    //回波生成槽函数
    void slotGenChaffEcho(void *p);
    void slotASKWaveDate();
    void slotSettingChaffSence(void *p);
    //天线
    void slotSettingChaffAntPara(void *p);

private:
    int m_DevID;
    char *d_BufferFather, *d_BufferInit;
    long long BufferInit_SizeTotal, BufferInitChaffRcs_Size,BufferInit_Cur;

    float   *dev_SumAntennaFunction,*dev_AziSubAntennaFunction,*dev_PitSubAntennaFunction;
	float   *dev_Chafft, *dev_ChaffX, *dev_ChaffY, *dev_ChaffZ, *dev_ChaffRcs, *dev_ChaffAzi, *dev_ChaffReal, *dev_ChaffImag;
	double  *dev_Matrix;

    float *dev_Target_ALL1, *dev_Target_ALL2, *dev_Target_ALL3, *dev_Target_ALL4, *dev_Target_ALL5;
    float *dev_DMCTarget_ALL1, *dev_DMCTarget_ALL2, *dev_DMCTarget_ALL3, *dev_DMCTarget_ALL4, *dev_DMCTarget_ALL5;


    curandGenerator_t gen_curand;
    cufftHandle plan[CUDAStreamMaxNum];
    cudaStream_t cudaStream[CUDAStreamMaxNum];
    cudaEvent_t e_start, e_stop, e_start2, e_stop2;
	cufftHandle plan1;


    int CosValueLen;//三角函数表长
    float *dev_CosValue, *dev_SinValue;//三角函数查表显存
    float *m_CosValue, *m_SinValue;
    bool m_bWaveDataShowFlag;

    double m_Fs, m_Br, m_Tp;
    float *dev_Signal_Send;

    stTarLink *m_StTarLink;
    char				*m_d_BufferChaffRCS, *m_pBufferChaffRCS;
    unsigned long long	m_BytesOffsetChaffRCS;

	ChaffRead           *m_Chaff;
    ClassCoorDinate *pClassCoorDinate;
    ANTPARAM        *m_AntParam;
	stChaffTimeLink *m_StChaffLinkHead;
	void ChaffSet();
};






#endif // THREADCLASSCHAFFECHO

