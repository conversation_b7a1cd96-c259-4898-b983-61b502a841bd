/*******************************************************************************************
 * FileProperties: 
 *     FileName: ClassCoorDinate.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Class/ClassCoorDinate.cpp $
 *         $Author: yening $
 *         $Revision: 160 $
 *         $Date: 2025-02-05 17:40:22 $
*******************************************************************************************/
#include "ClassCoorDinate.h"
#include <QFile>
#include <QDir>
#include "ThreadSocketTCP.h"

#include <iostream>
#include<fstream>
#include<cmath>
#include<sstream>
#include<string>
#include<cstdlib>
#include<limits>
#include<locale>
#include<codecvt>

#include "KernelCoorConver.h"

ClassCoorDinate::ClassCoorDinate()
{
    SetMemPoolSrcFileName((char*)"ClassCoorDinate", sizeof("ClassCoorDinate"));
    //_DefineMemPool(stTarPitLink, 2000);
    InitMyMalloc(32*1024*1024);


}

ClassCoorDinate::~ClassCoorDinate()
{

}
//矩阵乘法
void ClassCoorDinate::matrixmul(float *matrix1, int row1, int col1, float *matrix2, int row2, int col2, float *matrix)
{
    //int num= row1*col2;				//结果矩阵的大小
    //float m1, m2, m3;				//矩阵相乘中的过程变量

    for (int z = 0; z < row1; z++)
    {
        for (int j = 0; j < col2; j++)
        {
            for (int i = 0; i < row2; i++)
            {
                matrix[z*col1 + j] += matrix1[z*col1 + i] * matrix2[i*col2 + j];
            }
        }
    }
}
/**************************************
 * in:	alpha,beta,gamma		本体姿态角：航向角、俯仰角和横滚角(rad)
 * out: pMatixG                 旋转矩阵
 * ************************************/
void ClassCoorDinate::CoorMatrixG(float alpha, float beta, float gamma,float *pMatixG)
{
    int row1 = 3, col1 = 3;		//xOz平面绕y轴逆时针旋转俯仰角的旋转矩阵的行列定义
    float *matrix1 = (float *)_Malloc(row1*col1*sizeof(float));			//旋转矩阵过程量
    memset(matrix1, 0, row1*col1*sizeof(float));
	matrix1[0] = cos(alpha);
    matrix1[1] = 0;
	matrix1[2] = sin(alpha);
    matrix1[3] = 0;
    matrix1[4] = 1;
    matrix1[5] = 0;
	matrix1[6] = -sin(alpha);
    matrix1[7] = 0;
	matrix1[8] = cos(alpha);

    int row2 = 3, col2 = 3;		//x'Oy平面绕oz'轴逆时针旋转航向角的旋转矩阵的行列定义
    float *matrix2 = (float *)_Malloc(row2*col2*sizeof(float));			//旋转矩阵过程量
    memset(matrix2, 0, row2*col2*sizeof(float));
    matrix2[0] = 1;
    matrix2[1] = 0;
    matrix2[2] = 0;
    matrix2[3] = 0;
	matrix2[4] = cos(gamma);
	matrix2[5] = sin(gamma);
    matrix2[6] = 0;
	matrix2[7] = -sin(gamma);
	matrix2[8] = cos(gamma);

    int row3 = 3, col3 = 3;		//y'Oz'平面绕X轴逆时针旋转横滚角的旋转矩阵的行列定义
    float *matrix3 = (float *)_Malloc(row3*col3*sizeof(float));			//旋转矩阵过程量
    memset(matrix3, 0, row3*col3*sizeof(float));
	matrix3[0] = cos(beta);
	matrix3[1] = sin(beta);
    matrix3[2] = 0;
	matrix3[3] = -sin(beta);
	matrix3[4] = cos(beta);
    matrix3[5] = 0;
    matrix3[6] = 0;
    matrix3[7] = 0;
    matrix3[8] = 1;

	int row4 = 3, col4 = 3;		//y'Oz'平面绕X轴逆时针旋转横滚角的旋转矩阵的行列定义
	float *matrix4 = (float *)_Malloc(row3*col3*sizeof(float));			//旋转矩阵过程量
	memset(matrix4, 0, row3*col3*sizeof(float));
	matrix4[0] = 1;
	matrix4[1] = 0;
	matrix4[2] = 0;
	matrix4[3] = 0;
	matrix4[4] = 0;
	matrix4[5] = -1;
	matrix4[6] = 0;
	matrix4[7] = 1;
	matrix4[8] = 0;

    float *matrix5 = (float *)_Malloc(row1*col2*sizeof(float));				//旋转矩阵过程量
    memset(matrix5, 0, row1*col2*sizeof(float));
    matrixmul(matrix1, row1, col1, matrix2, row2, col2, matrix5);

	float *matrix6 = (float *)_Malloc(row1*col3*sizeof(float));				//旋转矩阵过程量
	memset(matrix6, 0, row1*col3*sizeof(float));
	matrixmul(matrix5, row1, col2, matrix3, row3, col3, matrix6);

	for (int idx = 0; idx < row1; idx++)
	{
		for (int jdx = 0; jdx < col4; jdx++)
		{
			pMatixG[idx * 3 + jdx] = matrix6[idx + jdx * 3];
		}
	}
}

/***************************************
* 实现从场景北天东坐标系到目标本体坐标系的坐标转换
* in:	Btd						北天东坐标系中待转换的坐标
* in:	origin					本体坐标系原点在北天东坐标系中的坐标
* in:	alpha,beta,gamma		本体姿态角：航向角、俯仰角和横滚角(rad)
* in:	TarNum                  目标个数
* out:	boat					转换为本体坐标系中的坐标
************************************/
void ClassCoorDinate::CoorBtdToBoat(int TarNum, float *pMatixG,float *origin,float *BTD,float *Boat)
{
    //先平移后旋转
    int col3 = 3;

    float *matrixBTD1 = (float *)_Malloc(TarNum*col3*sizeof(float));
    memset(matrixBTD1, 0, TarNum*col3*sizeof(float));

    for (int idx = 0; idx < TarNum; idx++)
    {
        for (int idy = 0; idy < 3; idy++)
        {
			matrixBTD1[idx * 3 + idy] = BTD[idx * 3 + idy] -origin[idy];
        }
    }
    matrixmul(matrixBTD1, TarNum, 3, pMatixG, col3, col3, Boat);				//先平移后旋转


}
/***************************************
* 实现从目标本体坐标系到场景北天东坐标系的坐标转换
* in:   Boat					本体坐标系中待转换的坐标
* in:	origin					本体坐标系原点在北天东坐标系中的坐标
* in:	Num						本体中散射点个数
* out:	BTD						转换为北天东坐标系中的坐标
************************************/
void ClassCoorDinate::CoorBoatToBtd(int Num, float *pMatixG,float *origin,float *Boat,float *BTD)
{
    int col3 = 3;
    float *matrixBTD1 = (float *)_Malloc(Num*col3*sizeof(float));
    memset(matrixBTD1, 0, Num*col3*sizeof(float));

    //先旋转后平移
    matrixmul(Boat, Num, 3, pMatixG, col3, col3, matrixBTD1);				//先平移后旋转

    for (int idx = 0; idx < Num; idx++)
    {
        for (int idy = 0; idy < 3; idy++)
        {
            BTD[idx * 3 + idy] = matrixBTD1[idx * 3 + idy] + origin[idy];
        }
    }
}
void ClassCoorDinate::CoorBoatToBtd_CUDA(int Num, float *dev_MatixG, float *dev_origin, float *dev_Boat_X, float *dev_Boat_Y, float *dev_Boat_Z, float *dev_BTD_X, float *dev_BTD_Y, float *dev_BTD_Z)
{
	int col3 = 3;
	//先旋转后平移		
	dim3 ThreadsPerBlock(512, 1);
	dim3 BlockNum((Num + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
	CUDA_MatrixMul_n_3(0, ThreadsPerBlock, BlockNum, Num, dev_Boat_X, dev_Boat_Y, dev_Boat_Z, dev_MatixG, dev_origin, dev_BTD_X, dev_BTD_Y, dev_BTD_Z);
}

//计算目标旋转矩阵和DYT信号入射角
//pTarVel:目标速度
//pTarPoz:目标位置
//pTarDYTRang:弹目矢量
//Azi:入射方位角
//Pit:入射俯仰角
//pMatixG:输出旋转矩阵
void ClassCoorDinate::ConvertAngleMatixG(float *pTarVel, float *pTarPoz, float *pTarDYTRang, float &Azi, float &Pit, float *pMatixG)
{
	//计算目标旋转矩阵
	float temp		=	atan(pTarVel[2] / pTarVel[0]);
	float alpha;

	if (pTarVel[2]<0 && pTarVel[0]>0)	alpha = 2 * PI + temp;
	else if (pTarVel[0]<0)	alpha = PI + temp;
	else alpha = temp;

	CoorMatrixG(alpha, 0.f, 0.f, pMatixG);
	//DYT转换到目标本体系中
	float *pBoat	= (float*)_Malloc(9 * sizeof(float));	//本体坐标系下弹目距位置
	memset(pBoat, 0, sizeof(float) * 9);
	CoorBtdToBoat(1, pMatixG, (float*)pTarPoz, pTarDYTRang, pBoat);
	//入射角
	float RadarTar	= sqrt(pBoat[0] * pBoat[0] + pBoat[1] * pBoat[1] + pBoat[2] * pBoat[2]);
	float temp1 = atan(pBoat[2] / pBoat[0]);
	if (pBoat[2] < 0 && pBoat[0]>0)	Azi = 2 * PI + temp1;
	else if (pBoat[0] < 0)	Azi = PI + temp1;
	else Azi = temp1;

	Pit		= asin(pBoat[1] / RadarTar);

	//


}

















