/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassChaffEcho.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassChaffEcho.cpp $
 *         $Author: yening $
 *         $Revision: 507 $
 *         $Date: 2025-05-27 19:14:15 $
*******************************************************************************************/
#include "ThreadClassChaffEcho.h"
#include<QThread>
#include <QDateTime>

#include <cuda_runtime_api.h>
#include<cuda_runtime.h>
#include<cuda_device_runtime_api.h>
#include<cufft.h>
#include<curand.h>
#include <fstream> 
#include <QFile>
#include <QDebug>

#include "KernelCultter.h"
#include "KernelChaff.h"
#include "KernelSar.h"
#include "KernelPublic.h"

#include "ThreadClassSendWaveGen.h"


ThreadClassChaffEcho::ThreadClassChaffEcho(int DevID, int buffer_size, char *d_Buffer, QObject *parent) : QObject(parent)
{
    SetMemPoolSrcFileName((char*)"ThreadClassChaffEcho", sizeof("ThreadClassChaffEcho"));
	_DefineMemPool(stChaffTimeLink, 50);
	_DefineMemPool(stChaffLink, 650);
	_DefineMemPool(stDateStruct1, 200);
	_DefineMemPool(stDateStruct2, 40);
    //分配CPU伪内存池空间
    InitMyMalloc(64*1024*1024);
    m_DevID				= -1;
	m_StChaffLinkHead = (stChaffTimeLink*)_MallocMemObj(stChaffTimeLink); m_StChaffLinkHead->init();

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}
ThreadClassChaffEcho::~ThreadClassChaffEcho()
{

}
//初始化GPU设备工作参数
bool ThreadClassChaffEcho::InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic)
{
    m_BytesOffsetChaffRCS		= 0;
    BufferInit_Cur			= 0;
    BufferInit_SizeTotal	= (long long)16 * 1024 * 1024;
	m_BytesOffsetChaffRCS = 0;
    if(d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
        m_DevID				= DevID;
        d_BufferInit		= d_Buffer;
		d_BufferFather		= d_BufferPublic;
		m_d_BufferChaffRCS = d_Buffer + BufferInit_SizeTotal;
        //分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, 5.0 * 1024 * 1024 * 1024);
    }
    else{//作为独立的线程使用，单独使用一个GPU卡
        m_DevID         = -1;
        d_BufferFather  = nullptr;
        //分配GPU伪内存池空间
        cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
        cudaMalloc((void**)&d_Buffer,buffer_size);
        d_BufferFather = d_Buffer;
        InitMyMallocCUDA(d_BufferFather, buffer_size);
    }
	//int Bytes = 1024.f * 100.f * sizeof(float);
	//dev_ChaffReal = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;
	//dev_ChaffImag = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += Bytes;

	//m_Chaff = (ChaffRead *)malloc(sizeof(ChaffRead));
	//ChaffSet();
	stChaffTimeLink *ptChaffTimeLink = readChaffRCSFile(nullptr);
	InitChaffRcsPara(ptChaffTimeLink);
	//DestroyLink(stChaffLink, ptChaffTimeLink->pstChaffLink, true);
    return true;
}
//初始化cuda参数
bool ThreadClassChaffEcho::InitCUDAPara()
{
    return true;
}
stChaffLink* ThreadClassChaffEcho::CalChaffRcs(float time, stChaffTimeLink* pSt)
{
	float minpitDistance = FLT_MAX;
	float pitdistance;
	cudaError_t Err = cudaGetLastError();
	stChaffLink *finalLink = nullptr;
	stChaffTimeLink *pStChaffStateLink = pSt;
	while (pStChaffStateLink->next) {
		pStChaffStateLink = pStChaffStateLink->next;
		stChaffLink * pStChaffLinkFind = pStChaffStateLink->pstChaffLink;

		while (pStChaffLinkFind->next)
		{
			pStChaffLinkFind = pStChaffLinkFind->next;
			pitdistance = abs(time - pStChaffLinkFind->timeNo);
			if (pitdistance < minpitDistance)
			{
				minpitDistance = pitdistance;
				finalLink = pStChaffLinkFind;
			}
		}

		float *p11 = (float*)_Malloc(sizeof(float) * 64 * finalLink->pointNum);
		cudaMemcpy(p11, finalLink->pChaffData.df, sizeof(float) * 64 * finalLink->pointNum, cudaMemcpyDeviceToHost);
		return finalLink;

	}
	return 0;
}

void ThreadClassChaffEcho::InitChaffRcsPara(void *p)
{
	cudaSetDevice(m_DevID);
	stChaffTimeLink *pstChaffTimeRecv = (stChaffTimeLink*)p;
	stChaffTimeLink *pstChaffTimeLinkRecvNode = pstChaffTimeRecv;

	stChaffLink *pStChaffrLinkHead = (stChaffLink*)_MallocMemObj(stChaffLink); pStChaffrLinkHead->init();

	stChaffLink *pStChaffLinkRecv = pstChaffTimeLinkRecvNode->pstChaffLink;

	while (pStChaffLinkRecv->next)
	{
		pStChaffLinkRecv = pStChaffLinkRecv->next;

		stChaffLink *pStChaffLinkNode = (stChaffLink*)_MallocMemObj(stChaffLink);
		pStChaffLinkNode->init();

		int Bytes = (pStChaffLinkRecv->pointNum*sizeof(float) + 16) / 16 * 16;

		pStChaffLinkNode->pChaffData.x = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.x, pStChaffLinkRecv->pChaffData.x, sizeof(float)*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStChaffLinkNode->pChaffData.y = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.y, pStChaffLinkRecv->pChaffData.y, sizeof(float)*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStChaffLinkNode->pChaffData.z = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.z, pStChaffLinkRecv->pChaffData.z, sizeof(float)*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStChaffLinkNode->pChaffData.rcs = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.rcs, pStChaffLinkRecv->pChaffData.rcs, sizeof(float)*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStChaffLinkNode->pChaffData.phi = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.phi, pStChaffLinkRecv->pChaffData.phi, sizeof(float)*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		Bytes = 64 * pStChaffLinkRecv->pointNum*sizeof(float);
		pStChaffLinkNode->pChaffData.df = (float*)(m_d_BufferChaffRCS + m_BytesOffsetChaffRCS); m_BytesOffsetChaffRCS += Bytes;
		cudaMemcpy(pStChaffLinkNode->pChaffData.df, pStChaffLinkRecv->pChaffData.df, sizeof(float)*64*pStChaffLinkRecv->pointNum, cudaMemcpyHostToDevice);
		pStChaffLinkNode->timeNo = pStChaffLinkRecv->timeNo;
		pStChaffLinkNode->pointNum = pStChaffLinkRecv->pointNum;;
		AddLink(pStChaffrLinkHead, pStChaffLinkNode);

	}

	stChaffTimeLink *pStChaffLinkNode = (stChaffTimeLink*)_MallocMemObj(stChaffTimeLink); pStChaffLinkNode->init();
	pStChaffLinkNode->pstChaffLink = pStChaffrLinkHead;
	AddLink(m_StChaffLinkHead, pStChaffLinkNode);



}

stChaffTimeLink* ThreadClassChaffEcho::readChaffRCSFile(char *path)
{
	cudaError_t Err = cudaGetLastError();
	stChaffTimeLink *ptChaffTimeLink = (stChaffTimeLink*)_MallocMemObj(stChaffTimeLink); ptChaffTimeLink->init();

	stChaffLink *pstChaffLinkHead = (stChaffLink*)_MallocMemObj(stChaffLink); pstChaffLinkHead->init();

	ptChaffTimeLink->pstChaffLink = pstChaffLinkHead;



	QFile *pQFile = new QFile;
	pQFile->setFileName("..\\..\\..\\rcs\\Chaff");
	pQFile->open(QFile::ReadOnly);
	for (int i = 0; i < 601; i++)
	{
		stChaffLink *pstChaffLink = (stChaffLink*)_MallocMemObj(stChaffLink); pstChaffLink->init();
		pQFile->read((char*)&(pstChaffLink->timeNo), sizeof(float));
		pQFile->read((char*)&(pstChaffLink->pointNum), sizeof(float));
		pstChaffLink->pChaffData.x = (float*)_MallocMemObj(stDateStruct1);
		pstChaffLink->pChaffData.y = (float*)_MallocMemObj(stDateStruct1);
		pstChaffLink->pChaffData.z = (float*)_MallocMemObj(stDateStruct1);
		pstChaffLink->pChaffData.rcs = (float*)_MallocMemObj(stDateStruct1);
		pstChaffLink->pChaffData.phi = (float*)_MallocMemObj(stDateStruct1);
		pstChaffLink->pChaffData.df = (float*)_MallocMemObj(stDateStruct2);
		pQFile->read((char*)pstChaffLink->pChaffData.x, sizeof(float)* pstChaffLink->pointNum);
		pQFile->read((char*)pstChaffLink->pChaffData.y, sizeof(float)* pstChaffLink->pointNum);
		pQFile->read((char*)pstChaffLink->pChaffData.z, sizeof(float)* pstChaffLink->pointNum);
		pQFile->read((char*)pstChaffLink->pChaffData.rcs, sizeof(float)* pstChaffLink->pointNum);
		pQFile->read((char*)pstChaffLink->pChaffData.phi, sizeof(float)* pstChaffLink->pointNum);
		pQFile->read((char*)pstChaffLink->pChaffData.df, sizeof(float)*64* pstChaffLink->pointNum);
		AddLink(ptChaffTimeLink->pstChaffLink, pstChaffLink);

	}
	pQFile->close();
	delete pQFile;
	return ptChaffTimeLink;
}
//箔条
void ThreadClassChaffEcho::ChaffSet()
{
	//cudaSetDevice(m_DevID);
	//_Release_Malloc();      //初始化伪内存池
	//_Release_MallocCUDA();

	//ChaffRead  *Chaff = m_Chaff;

	//QFile *pQFile1 = new QFile;
	//pQFile1->setFileName("..\\..\\..\\data\\Chaffdefalt");
	//pQFile1->open(QFile::ReadOnly);
	//Chaff->ChaffReal = (float*)malloc(sizeof(float) * 76800);
	//pQFile1->read((char*)Chaff->ChaffReal, 76800 * sizeof(float));
	//Chaff->ChaffImag = (float*)malloc(sizeof(float) * 76800);
	//pQFile1->read((char*)Chaff->ChaffImag, 76800 * sizeof(float));
	//pQFile1->close();

	//cudaMemcpy(dev_ChaffReal, Chaff->ChaffReal, sizeof(float) * 76800, cudaMemcpyHostToDevice);
	//cudaMemcpy(dev_ChaffImag, Chaff->ChaffImag, sizeof(float) * 76800, cudaMemcpyHostToDevice);

}

//提取箔条散射系数
void ThreadClassChaffEcho::ExtractChaffCoef(JAM_PARAM *state, RADAR *ParamR, SimStruct *SimData)
{
	cudaError_t Err = cudaGetLastError();

	int  N_comp = 1;//散射点数
	float t1 = 1;//扩散时间
	float t2 = 3;//稳定时间
	float t3 = 2;//消散时间
	float lamda = C_ / ParamR->Fc;

	int TarNum = 8192;// 此值用来预分配内存和显存
	int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5f);		//一个脉宽的点数

	int Nr = 16384;//ParamR->Nr;				//原ParamR->Nr
	int SaveNr = ParamR->Fs*ParamR->Tp;
	int Na = ParamR->Na;	//原ParamR->Na

	float *ChaffX	= (float*)_MallocCUDA(Nr*sizeof(float));
	float *ChaffY	= (float*)_MallocCUDA(Nr*sizeof(float));
	float *ChaffZ	= (float*)_MallocCUDA(Nr*sizeof(float));
	float *time0	= (float*)_MallocCUDA(Nr*sizeof(float));
	float *sawtooth = (float*)_MallocCUDA(Nr*sizeof(float));
	float *cos0		= (float*)_MallocCUDA(Nr*sizeof(float));

	Err = cudaGetLastError();

	float *pTemp = (float*)_Malloc(sizeof(float) * 10000);

	//float *p11 = (float*)_Malloc(sizeof(float) * 100);
	//cudaMemcpy(p11, m_StChaffLinkHead->next->pstChaffLink->next->pChaffData.x, sizeof(float) * 100, cudaMemcpyDeviceToHost);
	
	float *V			= (float*)_Malloc(3 * sizeof(float));		memset(V, 0, sizeof(float) * 3);
	float *Ps			= (float*)_Malloc(3 * sizeof(float));		memset(Ps, 0, sizeof(float) * 3);
	float *dev_V		= (float*)_MallocCUDA(3 * sizeof(float));	cudaMemset(dev_V, 0, sizeof(float) * 3);
	float *dev_ChaffPs	= (float*)_MallocCUDA(3 * sizeof(float));	cudaMemset(dev_ChaffPs, 0, sizeof(float) * 3);

	float time;
	float vx, vy, vz;
	int start_index = -1;
	int count = 0;
	for (int idx = 0; idx < state->ChaffNum; idx++)
	{

		float deltat = 1 / ParamR->Prf;
		time = state[idx].Chafftime;
		vx = state[idx].ChaffVelf[0];
		vy = state[idx].ChaffVelf[1];
		vz = state[idx].ChaffVelf[2];
		V[0] = state[idx].ChaffVelf[0];
		V[1] = state[idx].ChaffVelf[1];
		V[2] = state[idx].ChaffVelf[2];
		cudaMemcpy(dev_V, V, sizeof(float) * 3, cudaMemcpyHostToDevice);
		Ps[0] = state[idx].ChaffPos[0];
		Ps[1] = state[idx].ChaffPos[1];
		Ps[2] = state[idx].ChaffPos[2];
		cudaMemcpy(dev_ChaffPs, Ps, sizeof(float) * 3, cudaMemcpyHostToDevice);

		stChaffLink *pStChaffLinkNode = CalChaffRcs(time, m_StChaffLinkHead);
		float* dev_ChaffX = pStChaffLinkNode->pChaffData.x;
		float* dev_ChaffY = pStChaffLinkNode->pChaffData.y;
		float* dev_ChaffZ = pStChaffLinkNode->pChaffData.z;
		float* dev_ChaffRcs = pStChaffLinkNode->pChaffData.rcs;
		float* dev_ChaffPhi = pStChaffLinkNode->pChaffData.phi;
		float* dev_ChaffDf = pStChaffLinkNode->pChaffData.df;
		count = (int)pStChaffLinkNode->pointNum;

		dim3 ThreadsPerBlock(512, 1);
		dim3 BlockNum((count + ThreadsPerBlock.x - 1) / ThreadsPerBlock.x, 1);
		//CUDA_Pos(ThreadsPerBlock, BlockNum, ChaffX, ChaffY, ChaffZ, dev_ChaffX, dev_ChaffY, dev_ChaffZ, dev_ChaffPs, count);
		Err = cudaGetLastError();
		if (Err != cudaSuccess){
			qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_DevID;
		}
		CUDA_Vbian(ThreadsPerBlock, BlockNum, dev_ChaffX, dev_ChaffY, dev_ChaffZ, dev_ChaffPs, dev_Target_ALL1 + m_SacreNum, dev_Target_ALL2 + m_SacreNum, dev_Target_ALL3 + m_SacreNum, time, deltat, TarNum, time0, sawtooth, cos0, dev_V, idx);
		Err = cudaGetLastError();
		if (Err != cudaSuccess){
			qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_DevID;
		}
		cudaMemcpy(dev_Target_ALL4 + m_SacreNum, dev_ChaffRcs, sizeof(float)*pStChaffLinkNode->pointNum,cudaMemcpyDeviceToDevice);
		Err = cudaGetLastError();
		if (Err != cudaSuccess){
			qDebug() << "cudaError:" << __LINE__ << __FUNCTION__ << cudaGetErrorString(Err) << Err << "GPU:" << m_DevID;
		}
		cudaMemcpy(dev_Target_ALL5 + m_SacreNum, dev_ChaffPhi, sizeof(float)*pStChaffLinkNode->pointNum, cudaMemcpyDeviceToDevice);
		m_SacreNum += pStChaffLinkNode->pointNum;
		if (m_DevID == 8)
		{
			QFile *pQFile = new QFile;
			pQFile->setFileName("..\\..\\..\\data\\EchoDMC3");
			pQFile->open(QFile::WriteOnly);
			cudaMemcpy(pTemp, dev_Target_ALL4, sizeof(float)*pStChaffLinkNode->pointNum, cudaMemcpyDeviceToHost);
			pQFile->write((char*)pTemp, sizeof(float) *pStChaffLinkNode->pointNum);
			pQFile->close();
			delete pQFile;
		}
	}
}

void ThreadClassChaffEcho::GenChaffEcho(void *p, stEchoData *pStEchoData)
{
    cudaSetDevice(m_DevID);
    _Release_Malloc();      //初始化伪内存池
    _Release_MallocCUDA();

	stChaffEchoPara *pstChaffEchoPara	= (stChaffEchoPara*)p;
	RADAR			*ParamR				= pstChaffEchoPara->ParamR;
	SimStruct		*SimData			= (SimStruct*)&pstChaffEchoPara->SimData;
	JAM_PARAM		*Pjammer			= pstChaffEchoPara->Jammer;

	dev_Target_ALL1 = pStEchoData->dev_Target_ALL1 + pStEchoData->SacreNum;
	dev_Target_ALL2 = pStEchoData->dev_Target_ALL2 + pStEchoData->SacreNum;
	dev_Target_ALL3 = pStEchoData->dev_Target_ALL3 + pStEchoData->SacreNum;
	dev_Target_ALL4 = pStEchoData->dev_Target_ALL4 + pStEchoData->SacreNum;
	dev_Target_ALL5 = pStEchoData->dev_Target_ALL5 + pStEchoData->SacreNum;

	m_SacreNum = 0;
	//提取箔条散射系数
	ExtractChaffCoef(Pjammer, ParamR, SimData);
	pStEchoData->SacreNum += m_SacreNum;

}
