/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadResourceScheduling.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadResourceScheduling.h $
 *         $Author: weiyingzhen $
 *         $Revision: 503 $
 *         $Date: 2025-05-27 10:41:15 $
*******************************************************************************************/
#ifndef THREADRESOURCESCHEDULING_H
#define THREADRESOURCESCHEDULING_H

#include <QObject>
#include <QTimer>

#include "_stumempool.h"
#include "ThreadClassGPUCalcute.h"
#include "ThreadSocketTCP.h"
#include "ClassRcsRead.h"
#include "ClassClutterRcsRead.h"
#include "globalExternalStruct.h"



class ThreadResourceScheduling : public QObject,_StuMemPool
{
    Q_OBJECT
public:
    explicit ThreadResourceScheduling(QObject *parent = 0);
    ~ThreadResourceScheduling();
    
public:
    //获取GPU板卡信息
    bool GetGPUDeviceProperties();

signals:
	void sendSocketMsgTCP(void *p, unsigned int eDecoteType);
    void sendEchoDataFormat(void*p);
	void sendEchoDataPackFormat(void*p);
	void sendASKWaveDate();

	void sendTarEchoDateGen1(int Dev,void*p,void*p2);//目标
	void sendTarEchoDateGen2(int Dev, void*p, void*p2);
	void sendTarEchoDateGen3(int Dev, void*p, void*p2);
	void sendTarEchoDateGen4(int Dev, void*p, void*p2);
	void sendTarEchoDateGen5(int Dev, void*p, void*p2);
	void sendTarEchoDateGen6(int Dev, void*p, void*p2);
	void sendTarEchoDateGen7(int Dev, void*p, void*p2);
	void sendTarEchoDateGen8(int Dev, void*p, void*p2);
	void sendActiveJamEchoDateGen(void*p);//有源干扰

public slots:
    void slotResourceScheduling(void *p);
	void slotResourceScheduling2(void *p);
	void slotCalcuteEcho(void *p);
	void handleTimeout();
	void slotSettingSarSence(void *p);
	void slotRecvGPUEcho(void *p);
	void slotRecvGPUEcho2(void *p);
	void slotASKWaveDate();

private:
    int m_GpuCount;
	QTimer *timer;
	stFrameLink *m_StFrameLinkHead;
    ClassRcsRead *pClassRcsRead;
    ClassClutterRcsRead *pClassClutterRcsRead;
	stTarClutterLink *m_stTarClutterLink;
	uint m_RestTaskNode;
	int m_GPU_NO_Start;


    //初始化目标散射系数信息
    void InitTarRcsInfo(char *path);
    //初始化海杂波
    void InitClutterRcsInfo(char *path);
	//波门宽度计算(适用场景为空对海场景、波束俯仰角小于-3°的情形)
	template<class T, class T2, class T3>
	double CalcuteRangeGaeInfo(double &Rmin, double &WaveGateWidth, T AziAngle, T PitAngle, T2 lobeWidth3dB, T3 *RadarPos, double Fs, double Tp, double PRT);

public:
	ThreadClassGPUCalcute *pThreadClassGPUCalcute[MAX_GPU_NUM];
	ThreadEchoDataFormat    *pThreadEchoDataFormat;
	ThreadEchoDataSave      *pThreadEchoDataSave;

	void SettingTarPara(void *p, void *pc);
	void SettingSarClutter(void *p, void *pc);//面目标杂波信息
};

#endif // THREADRESOURCESCHEDULING_H
