﻿/*******************************************************************************************
 * FileProperties: 
 *     FileName: GLWidgetWave.h
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/GLWidget/GLWidgetWave.h $
 *         $Author: yening $
 *         $Revision: 19 $
 *         $Date: 2024-12-22 15:19:29 $
*******************************************************************************************/
#ifndef GLWIDGETWAVE_H
#define GLWIDGETWAVE_H

#include <QGLWidget>
#include <QTimer>
#include"_GLWidgetBase.h"
#include"_stumempool.h"

#include"globalRadarStruct.h"


class GLWidgetWave :public _GLWidgetBase,_StuMemPool
{
    Q_OBJECT
public:
    explicit GLWidgetWave();
    ~GLWidgetWave();
    //绘制收到的数据
    void drawIQWave();
protected:
    void paintGL();
public:
    float g_AmpMax;
    UINT m_PlotMode,m_Sample;
    int len;
    
    uint uBw,uFs;
    float gMax,gMin;
public:

    
public slots:
    void slotWidgetWave(void *p);
};


#endif // GLWIDGETINSTANCEBWFREQ_H

