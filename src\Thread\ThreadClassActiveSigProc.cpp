/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassActiveSigProc.cpp
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/src/Thread/ThreadClassActiveSigProc.cpp $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#include "ThreadClassActiveSigProc.h"
#include<QThread>
#include <QDateTime>

#include <cuda_runtime_api.h>
#include<cuda_runtime.h>
#include<cuda_device_runtime_api.h>
#include<cufft.h>
#include<curand.h>

#include <QFile>
#include <QDebug>
#include <QImage>

#include "KernelRed.h"
#include "KernelPublic.h"

#include "ThreadClassSendWaveGen.h"
#include "ThreadSocket.h"

ThreadClassActiveSigProc::ThreadClassActiveSigProc(int DevID,int buffer_size,char *d_Buffer,QObject *parent) : QObject(parent)
{
    SetMemPoolSrcFileName((char*)"ThreadClassActiveSigProc", sizeof("ThreadClassActiveSigProc"));
	_DefineMemPool(stEchoData, 5);
    _DefineMemPool(stWaveShow,10);
    //分配CPU伪内存池空间
	InitMyMalloc((long long)2048 * (long long)1024 * (long long)1024);
    m_DevID				= DevID;

	//三角函数查表
	double CosValueSample = 1.0 / 4096;
	CosValueLen = 4100;
	m_CosValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_CosValue, 0, sizeof(float)*CosValueLen);
	m_SinValue = (float *)malloc(sizeof(float)*CosValueLen);
	memset(m_SinValue, 0, sizeof(float)*CosValueLen);
	for (int idd = 0; idd < CosValueLen; idd++)
	{
		if (CosValueSample*idd <= 1)
		{
			m_CosValue[idd] = cos(idd*CosValueSample * 2 * PI);
			m_SinValue[idd] = sin(idd*CosValueSample * 2 * PI);
		}
	}
	m_Fs = m_Br = m_Tp = 0;

    m_ParamR    = new RADAR;       memset(m_ParamR,0,sizeof(RADAR));
    m_SimData   = new SimStruct;   memset(m_SimData,0,sizeof(SimStruct));

	m_FrameNumAcc = 0;

    QThread *thread = new QThread;
    this->moveToThread(thread);
    thread->start();
}

ThreadClassActiveSigProc::~ThreadClassActiveSigProc()
{

}

void writeFileActive(char *p, int bytes, QString str)
{
	//to do
	QString strFilePath = QString("D:\\yening\\PRJ\\PR2401_010\\9_from_yening\\CalcutePool\\data\\%1").arg(str);
	QFile *pQFile = new QFile;
	pQFile->setFileName(strFilePath);
	int ret = pQFile->open(QFile::WriteOnly);
	if (ret == 1)
	{
		//char *pBuffer = (char*)_Malloc(1024 * 1024);
		//cudaMemcpy(pBuffer, (T*)dev_EchoCompress2 + OverSample*Nr*i, OverSample*SimData->SaveFlie_Nr*sizeof(T), cudaMemcpyDeviceToHost);
		pQFile->write((char*)(p), bytes);
		pQFile->close();
	}
}
//初始化GPU设备工作参数
bool ThreadClassActiveSigProc::InitDeviceWorkPara(int DevID,long long buffer_size,char *d_Buffer)
{
	BufferInit_Cur			= 0;
	BufferInit_SizeTotal	= (long long)1280 * 1024 * 1024;
    if(d_Buffer != nullptr){//作为一个类使用，和其他模块共享GPU卡
        m_DevID         = DevID;
		d_BufferInit	= d_Buffer;
		d_BufferFather = d_Buffer + BufferInit_SizeTotal;
        //分配GPU伪内存池空间
		InitMyMallocCUDA(d_BufferFather, buffer_size - BufferInit_SizeTotal);
    }
    else{//作为独立的线程使用，单独使用一个GPU卡
		m_DevID			= DevID;
		cudaSetDevice(m_DevID);
        d_BufferFather  = nullptr;
        //分配GPU伪内存池空间
		cudaMalloc((void**)&d_BufferInit, BufferInit_SizeTotal);
        cudaMalloc((void**)&d_Buffer,buffer_size);
		d_BufferFather = d_Buffer;
		InitMyMallocCUDA(d_BufferFather, buffer_size);
    }
	//发射信号缓存区
	dev_Signal_Send = (float*)(d_BufferInit + BufferInit_Cur); BufferInit_Cur += 128*1024*sizeof(float);
    cudaMemset(dev_Signal_Send, 0, 128*1024*sizeof(float));

	cudaError_t Err = cudaGetLastError();
	//三角函数查表
	long long Bytes = (CosValueLen*sizeof(float) + 16) / 16 * 16;
	dev_CosValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_CosValue, m_CosValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
	dev_SinValue = (float*)(d_BufferInit + BufferInit_Cur);
	cudaMemcpy(dev_SinValue, m_SinValue, sizeof(float)*CosValueLen, cudaMemcpyHostToDevice); BufferInit_Cur += Bytes;
	Bytes = 4 * 1024 * 1024 * sizeof(float);

	cufftHandle planTemp;
	cufftPlan1d(&planTemp, 32768, CUFFT_C2C, 1);

    return true;
}
//初始化cuda参数
bool ThreadClassActiveSigProc::InitCUDAPara()
{
	int    BATCH = 1;
	for (int i = 0; i < CUDAStreamMaxNum; i++)
	{
		cudaStreamCreate(&cudaStream[i]);
		cufftPlan1d(&plan[i], m_SimData->SaveFlie_Na, CUFFT_C2C, m_SimData->SaveFlie_Nr);
		cufftSetStream(plan[i], cudaStream[i]);
	}
    
    return true;
}

template <class T>
cufftHandle _CufftExecC2C(cufftHandle plan, T *idata, T *odata, int direction)
{
	cufftResult ret = CUFFT_NOT_SUPPORTED;
	if (sizeof(T) > 8){
		ret = cufftExecZ2Z(plan, (cuDoubleComplex*)idata, (cuDoubleComplex*)odata, direction);
	}
	else{
		ret = cufftExecC2C(plan, (cufftComplex*)idata, (cufftComplex*)odata, direction);
	}

	return ret;
}
//对回波做脉压
template <class T,class T2,class T3>
void ThreadClassActiveSigProc::EchoCompress(SimStruct *SimData,RADAR *ParamR,T *BaseSignal, T2 *pEchoData, T3 *dev_EchoComp)
{
    int Nr = SimData->SaveFlie_Nr;
    int Na = SimData->SaveFlie_Na;

    cudaEvent_t e_start, e_stop;
    cudaEventCreate(&e_start);
    cudaEventCreate(&e_stop);
    cudaEventRecord(e_start, 0);

    cudaError_t Err = cudaGetLastError();

    T *dev_Signal = (T*)dev_Signal_Send;
    //参考脉冲做脉压
    if (abs(m_Fs - ParamR->Fs) > 1 || abs(m_Br - ParamR->Br) > 1 || abs(m_Tp*1e6 - ParamR->Tp*1e6) > 0.1f)
    {
        m_Fs = ParamR->Fs; m_Br = ParamR->Br; m_Tp = ParamR->Tp;
        cudaMemset(dev_Signal, 0, 16*sizeof(T));
        Err = cudaGetLastError();
        cudaMemcpy(dev_Signal, BaseSignal, SimData->SaveFlie_Nr*sizeof(T), cudaMemcpyHostToDevice);
        cufftHandle ret =  _CufftExecC2C(plan[0], dev_Signal, dev_Signal, CUFFT_FORWARD);

        dim3 ThreadsPerBlock(512,1);
        dim3 BlockNum((Nr + ThreadsPerBlock.x - 1)/ThreadsPerBlock.x,1);
        CUDA_ComplexConj(0, ThreadsPerBlock, BlockNum, Nr,dev_Signal);//共轭
    }
    Err = cudaGetLastError();

    T       *dev_Echo0          = (T*)_MallocCUDA(sizeof(T)*Nr*Na);
    T       *dev_Echo01			= (T*)_MallocCUDA(sizeof(T)*Nr*Na);
    T2      *dev_EchoTemp		= (T2*)_MallocCUDA(sizeof(T2)*Nr*Na);

    dim3 ThreadsPerBlock0(512, 1);
    dim3 BlockNum0((Nr + ThreadsPerBlock0.x - 1) / ThreadsPerBlock0.x, 1);

    int iFrame = 1;
    for (int j = 0; j < iFrame; j++)
    {
        for (int i = 0; i < Na; i++)
        {
            int cudaStr		= i;
            cudaMemcpyAsync(dev_EchoTemp + i*Nr, pEchoData + i*Nr, Nr * sizeof(T2), cudaMemcpyHostToDevice, cudaStream[cudaStr]);   //回波->GPU
            Err = cudaGetLastError();
            CUDA_ComplexTrans(cudaStream[cudaStr], ThreadsPerBlock0, BlockNum0, dev_EchoTemp + i*Nr, dev_Echo01 + i*Nr);
            Err = cudaGetLastError();
            cufftExecC2C(plan[cudaStr], (cufftComplex*)(dev_Echo01 + i*Nr), (cufftComplex*)(dev_Echo0 + i*Nr), CUFFT_FORWARD);
            cufftExecC2C(plan[cudaStr], (cufftComplex*)(dev_Echo0 + i*Nr), (cufftComplex*)(dev_EchoComp + i*Nr), CUFFT_INVERSE);
            Err = cudaGetLastError();

        }
        cudaEventRecord(e_stop, 0);
        cudaEventSynchronize(e_stop);
    }

    float elapsedTime = 0;
    cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
    qDebug() << "GPU:" << m_DevID << "============  CUDA EchoCompress Cost Time is  " << (elapsedTime) << " ms";

}

//MTD
void ThreadClassActiveSigProc::EchoMTD(int Nr, int Na, SimStruct *SimData, PLATFORM *RadarPos, RADAR *ParamR, Complexf *PulseComData, Complexf *MTDData)
{
    double *RefRe = (double*)_Malloc(sizeof(double)*Na);
    double *RefIm = (double*)_Malloc(sizeof(double)*Na);
    int idx, idy;

    double Ax, Ay, Az, Anorm, Vx, Vy, Vz, Vnorm, Pnorm;
    double thetaV, thetaA;
    double Fdrint, Rn;
    Ax      = RadarPos->NorthAcc;
    Ay      = RadarPos->SkyAcc;
    Az      = RadarPos->EastAcc;
    Anorm   = sqrt(Ax*Ax + Ay*Ay + Az*Az);
    Vx      = RadarPos->NorthVel;
    Vy      = RadarPos->SkyVel;
    Vz      = RadarPos->EastVel;
    Vnorm   = sqrt(Vx*Vx + Vy*Vy + Vz*Vz);
    Pnorm   = sqrt(SimData->Radar2Target.x*SimData->Radar2Target.x + SimData->Radar2Target.y*SimData->Radar2Target.y + SimData->Radar2Target.z*SimData->Radar2Target.z);
    if (Vnorm < MINEQUERR)
        thetaV = 0;
    else
        thetaV = acos((Vx*SimData->Radar2Target.x + Vy*SimData->Radar2Target.y + Vz*SimData->Radar2Target.z) / Vnorm / Pnorm);
    if (Anorm < MINEQUERR)
        thetaA = 0;
    else
        thetaA = acos((Ax*SimData->Radar2Target.x + Ay*SimData->Radar2Target.y + Az*SimData->Radar2Target.z) / Anorm / Pnorm);

	//CUDA方位向FFT
	float Rn_Coef_ref	= 1.0 / ParamR->Fs*SPEEDLIGHT / 2;
	float Fdrint_Coef	= (Vnorm*sin(thetaV))*(Vnorm*sin(thetaV)) / ParamR->Lambda;
	float Fdrint_Coef2	= Anorm*cos(thetaA) / ParamR->Lambda;
	float ta_Coef		= Na / 2 / ParamR->Prf;

	Complexf *dev_InData	= (Complexf *)_MallocCUDA(Na*Nr*sizeof(Complexf));
	Complexf *dev_OutData	= (Complexf *)_MallocCUDA(Na*Nr*sizeof(Complexf));
	
	float *param	= (float*)_Malloc(32 * sizeof(float));
	param[0]		= SimData->Rmin;
	param[1]		= Rn_Coef_ref;
	param[2]		= Fdrint_Coef;
	param[3]		= Fdrint_Coef2;
	param[4]		= 1.f/ ParamR->Prf;
	param[5]		= ta_Coef;

	float *dev_param		= (float*)_Malloc(32 * sizeof(float));
	cudaMemcpy(dev_param, param, sizeof(float) * 8, cudaMemcpyHostToDevice);
	// 设置CUDA网格和块的维度
	dim3 dimBlock(TILE_DIM, BLOCK_ROWS);
	dim3 dimGrid((Nr + TILE_DIM - 1) / TILE_DIM, (Na + TILE_DIM - 1) / TILE_DIM);
	CUDA_TransDiagComplexMult(0, dimBlock, dimGrid, dev_OutData, dev_InData, dev_CosValue, dev_SinValue, dev_param, Nr, Na);

	_CufftExecC2C(plan[0], dev_OutData, dev_InData, CUFFT_FORWARD);

	dimGrid.x = (Na + TILE_DIM - 1) / TILE_DIM;
	dimGrid.y = (Nr + TILE_DIM - 1) / TILE_DIM;
	CUDA_TransDiagComplex(0, dimBlock, dimGrid, dev_OutData, dev_InData, Na, Nr);

}
/******************
*求均值
*data：输入向量
*Len:向量长度
*************/
template <class T>
float GetMean(T* Data, int Len)
{
	int idx = 0;
	double Sum = 0;
	for (idx = 0; idx < Len; idx++)
	{
		Sum = Sum + *(Data + idx);
	}
	Sum = Sum / Len;
	return Sum;
}
//计算均值
template <class T>
void CalcuteMeanMult(T *pOutMaen,T* pData, int row,int clo,int Nr)
{
	
	for (int i = 0; i < row; i++)
	{
		int idx = 0;
		float Sum = 0;
		T *pInPut = (T*)&pData[i*clo];
		for (idx = 0; idx < clo; idx++)
		{
			Sum += *(pInPut + idx);
		}
		pOutMaen[i] = Sum / Nr;
	}
	
}
//CFAR
void ThreadClassActiveSigProc::CFAR(int Nr, int Na, CFARParam *CfarP, Complexf* dev_MTDData, float* CFARData)
{
	int i, j;
	int ScattingPointNum = 0;

	float RanMean = 0, AziMean = 0, LeftMean = 0, RightMean = 0;
	float CfarThr = 0;

	float  *dev_RanLine, *AziLine, *dev_SpreadCash, *AziSpreadCash, *ScattingPointCash;
	Complexf *dev_MTDCash;

	CfarP->RanSpreadLen = (int)(Nr + 2 * (CfarP->RanProtLen + CfarP->RanRefLen)); 	//距离向扩展向量长度 2168
	CfarP->AziSpreadLen = (int)(Na + 2 * (CfarP->AziProtLen + CfarP->AziRefLen));	//方位向扩展向量长度 294

	dev_MTDCash = (Complexf *)_MallocCUDA(Na*Nr*sizeof(Complexf));
	cudaMemset(dev_MTDCash, 0, Na*Nr*sizeof(Complexf));
	cudaMemcpy(dev_MTDCash, dev_MTDData, Na*Nr*sizeof(Complexf), cudaMemcpyDeviceToDevice);

	dev_RanLine = (float *)_MallocCUDA(Na*Nr*sizeof(float));
	cudaMemset(dev_RanLine, 0, Na*Nr*sizeof(double));
	AziLine = (float *)_MallocCUDA(Na*Nr*sizeof(float));
	cudaMemset(AziLine, 0, Na*Nr*sizeof(float));

	//扩展向量
	int CashLen = CfarP->RanSpreadLen;		//默认只构建距离向扩展向量
	dev_SpreadCash = (float *)_MallocCUDA(Na*CashLen*sizeof(float));
	cudaMemset(dev_SpreadCash, 0, Na*CashLen*sizeof(float));

	AziSpreadCash = NULL;
	if (CfarP->Direction == TWODIMESION)
	{
		CashLen = CfarP->AziSpreadLen;
		//AziSpreadCash = (float *)malloc(Na*Na*sizeof(float));
		AziSpreadCash = (float *)_Malloc(CashLen*sizeof(float));
		memset(AziSpreadCash, 0, CashLen*sizeof(float));
	}

	//散射点输出缓存
	ScattingPointCash = (float *)_Malloc((4 * Na*Nr + 4)*sizeof(float));//为数组前4 个值增加4个double内存
	memset(ScattingPointCash, 0, (4 * Na*Nr + 4)*sizeof(float));

	if (CfarP->Direction == RANGEONLY)
	{
		//
		dim3 dimBlock(1024);
		dim3 dimGrid((Nr*Na + dimBlock.x - 1) / dimBlock.x, 1);
		if (SQUARELAW == CfarP->DetectorLaw)    // 平方率检波
		{
			CUDA_ComplexAbs2(0, dimBlock, dimGrid, dev_MTDCash, dev_RanLine);
		}
		else    // 幅度检波
		{
			CUDA_ComplexAbs(0, dimBlock, dimGrid, dev_MTDCash, dev_RanLine);
		}

		//求距离向均值
		dimGrid.x = (Nr*Na/4 + dimBlock.x - 1) / dimBlock.x;
		float *dev_RanLineSum = (float*)_MallocCUDA(sizeof(float)*dimGrid.x);
		CUDA_ReduceSumValSmemUnroll4(0, dimBlock, dimGrid, dev_RanLine, dev_RanLineSum, Nr*Na);

		float *pRanLineSum = (float*)_Malloc(sizeof(float)*dimGrid.x);
		cudaMemcpy(pRanLineSum, dev_RanLineSum, sizeof(float)*dimGrid.x, cudaMemcpyDeviceToHost);
		CalcuteMeanMult(pRanLineSum, pRanLineSum, Na, dimGrid.x / Na, Nr);

		//构造扩展向量
		float *pSpreadAdd = (float*)_Malloc(sizeof(float)*(CfarP->RanProtLen + CfarP->RanRefLen)*Na);
		for (int i = 0; i < Na; i++){
			for (int j = 0; j < (CfarP->RanProtLen + CfarP->RanRefLen); j++){
				pSpreadAdd[i*(CfarP->RanProtLen + CfarP->RanRefLen) + j] = pRanLineSum[i];
			}
		}
		float *dev_SpreadAdd = (float*)_MallocCUDA(sizeof(float)*(CfarP->RanProtLen + CfarP->RanRefLen)*Na);
		cudaMemcpy(dev_SpreadAdd, pSpreadAdd, sizeof(float)*(CfarP->RanProtLen + CfarP->RanRefLen)*Na, cudaMemcpyHostToDevice);
		for (int i = 0; i < Na; i++)
		{
			cudaMemcpy(dev_SpreadCash, dev_SpreadAdd + (CfarP->RanProtLen + CfarP->RanRefLen)*i, sizeof(float)*(CfarP->RanProtLen + CfarP->RanRefLen), cudaMemcpyDeviceToDevice);
			cudaMemcpy(dev_SpreadCash + (CfarP->RanProtLen + CfarP->RanRefLen)*(i + 1), dev_RanLine + Nr*i, sizeof(float)*Nr, cudaMemcpyDeviceToDevice);
			cudaMemcpy(dev_SpreadCash, dev_SpreadAdd + (Nr + CfarP->RanProtLen + CfarP->RanRefLen)*(i + 1), sizeof(float)*(CfarP->RanProtLen + CfarP->RanRefLen), cudaMemcpyDeviceToDevice);
		}

		// 第一门限
		float firstThr = CfarP->FirstThr;
		if (SQUARELAW == CfarP->DetectorLaw)    // 平方律检波
		{
			firstThr = firstThr / 2;
		}
		
		dimBlock.x = 1024;
		dimGrid.x = (Nr*Na + dimBlock.x - 1) / dimBlock.x;
		int *dev_CfarTar_Index = (int*)_MallocCUDA(sizeof(int)*Na*CfarP->MaxDetectNum);
		//CFAR检测出过门限的点的位置，将索引记录在dev_CfarTar_Index中
		CUDA_CFAR(0, dimBlock, dimGrid, Nr*Na, Nr, dev_CfarTar_Index, dev_RanLine, dev_SpreadCash,powf(10, firstThr / 10), 
										CfarP->RanProtLen, CfarP->RanRefLen, CfarP->Type, CfarP->RanThr, CfarP->MaxDetectNum);
		
		int *pCfarTar_Index = (int*)_MallocCUDA(sizeof(int)*Na*CfarP->MaxDetectNum);
		cudaMemcpy(pCfarTar_Index, dev_CfarTar_Index, sizeof(int)*Na*CfarP->MaxDetectNum, cudaMemcpyDeviceToHost);

		Complexf* pMTDData = (Complexf*)_Malloc(sizeof(Complexf)*Nr*Na);
		cudaMemcpy(pMTDData, dev_MTDData, sizeof(Complexf)*Nr*Na, cudaMemcpyDeviceToHost);
		for (int i = 0; i<Na; i++)
		{
			float *pCfarTar = (float*)&pCfarTar_Index[Nr*i];
			Complexf* pMTDDataTmp = (Complexf*)&pMTDData[i*Nr];
			for (int j = 0; j<Nr; j++)
			{
				if (pCfarTar[j] > 0)
				{
					if (ScattingPointNum < CfarP->MaxDetectNum)   //散射点小于最大探测数目
					{
						*(ScattingPointCash + (4 + 4 * ScattingPointNum + 0)) = j;
						*(ScattingPointCash + (4 + 4 * ScattingPointNum + 1)) = i;
						*(ScattingPointCash + (4 + 4 * ScattingPointNum + 2)) = pMTDDataTmp[j].x;
						*(ScattingPointCash + (4 + 4 * ScattingPointNum + 3)) = pMTDDataTmp[j].y;
						ScattingPointNum++;
					}
				}//第一门限
			}
		}
	}
	else if (CfarP->Direction == AZIMUTHONLY)    // 方位维cfar
	{
		//
		dim3 dimBlock(1024);
		dim3 dimGrid((Nr*Na + dimBlock.x - 1) / dimBlock.x, 1);
		if (SQUARELAW == CfarP->DetectorLaw)    // 平方率检波
		{
			CUDA_ComplexAbs2(0, dimBlock, dimGrid, dev_MTDCash, dev_RanLine);
		}
		else    // 幅度检波
		{
			CUDA_ComplexAbs(0, dimBlock, dimGrid, dev_MTDCash, dev_RanLine);
		}
		
		//求方位向均值,同时构造扩展向量
		dimBlock.x = 512; dimBlock.y = 1;
		dimGrid.x = (Nr + dimBlock.x - 1) / dimBlock.x; dimGrid.y = 1;
		float *dev_SpreadCashT = (float*)_MallocCUDA(CfarP->AziSpreadLen*Nr*sizeof(float));
		CUDA_AmzMean(0, dimBlock, dimGrid, Na, Nr, 2 * (CfarP->AziProtLen + CfarP->AziRefLen), dev_RanLine, dev_SpreadCashT);
		cudaMemcpy(dev_SpreadCash + (CfarP->AziSpreadLen - Na) / 2 * Nr*sizeof(float), dev_RanLine,Na*Nr*sizeof(float),cudaMemcpyDeviceToDevice);

		//转置abs信号
		dim3 dimBlock2(TILE_DIM, BLOCK_ROWS);
		dim3 dimGrid2((Nr + TILE_DIM - 1) / TILE_DIM, (Na + TILE_DIM - 1) / TILE_DIM);
		float *dev_RanLineT = (float*)_MallocCUDA(Na*Nr*sizeof(float));
		CUDA_TransDiagReal(0, dimBlock2, dimGrid2, dev_RanLineT, dev_RanLine, Nr, Na);

		//转置扩展向量
		dim3 dimBlock3(TILE_DIM, BLOCK_ROWS);
		dim3 dimGrid3((Nr + TILE_DIM - 1) / TILE_DIM, (CfarP->AziSpreadLen + TILE_DIM - 1) / TILE_DIM);
		float *dev_SpreadCash = (float*)_MallocCUDA(CfarP->AziSpreadLen*Nr*sizeof(float));
		CUDA_TransDiagReal(0, dimBlock3, dimGrid3, dev_SpreadCash, dev_SpreadCashT, Nr, CfarP->AziSpreadLen);

		// 第一门限
		double firstThr = CfarP->FirstThr;
		if (SQUARELAW == CfarP->DetectorLaw)    // 平方律检波
		{
			firstThr = firstThr / 2;
		}

		dimBlock.x = 1024;
		dimGrid.x = (Nr*Na + dimBlock.x - 1) / dimBlock.x;
		int *dev_CfarTar_Index = (int*)_MallocCUDA(sizeof(int)*Na*CfarP->MaxDetectNum);
		//CFAR检测出过门限的点的位置，将索引记录在dev_CfarTar_Index中
		CUDA_CFAR(0, dimBlock, dimGrid, Nr*Na, Na, dev_CfarTar_Index, dev_RanLineT, dev_SpreadCash, powf(10, firstThr / 10),
					CfarP->RanProtLen, CfarP->RanRefLen, CfarP->Type, CfarP->RanThr, CfarP->MaxDetectNum);

		int *pCfarTar_Index = (int*)_MallocCUDA(sizeof(int)*Na*CfarP->MaxDetectNum);
		cudaMemcpy(pCfarTar_Index, dev_CfarTar_Index, sizeof(int)*Na*CfarP->MaxDetectNum, cudaMemcpyDeviceToHost);

		//转置abs信号
		Complexf *dev_MTDDataT = (Complexf*)_MallocCUDA(Na*Nr*sizeof(Complexf));
		CUDA_TransDiagComplex(0, dimBlock2, dimGrid2, dev_MTDDataT, dev_MTDData, Nr, Na);
		Complexf* pMTDData = (Complexf*)_Malloc(sizeof(Complexf)*Nr*Na);
		cudaMemcpy(pMTDData, dev_MTDDataT, sizeof(Complexf)*Nr*Na, cudaMemcpyDeviceToHost);
		for (int i = 0; i < Nr; i++)
		{
			float *pCfarTar = (float*)&pCfarTar_Index[Na*i];
			Complexf* pMTDDataTmp = (Complexf*)&pMTDData[i*Na];
			for (int j = 0; j < Na; j++)
			{
				if (pCfarTar[j] > 0)
				{
					if (ScattingPointNum < CfarP->MaxDetectNum)   //散射点小于最大探测数目
					{
						*(ScattingPointCash + (4 + 4 * ScattingPointNum + 0)) = j;
						*(ScattingPointCash + (4 + 4 * ScattingPointNum + 1)) = i;
						*(ScattingPointCash + (4 + 4 * ScattingPointNum + 2)) = pMTDDataTmp[j].x;
						*(ScattingPointCash + (4 + 4 * ScattingPointNum + 3)) = pMTDDataTmp[j].y;
						ScattingPointNum++;
					}
				}//第一门限
			}
		}
	}
	else    // 二维CFAR
	{

	}

	// 第一个点为散射点个数，第二个点为Na的值，第三个点为Nr的值，第四个点为0
	*(ScattingPointCash + 0) = ScattingPointNum;
	*(ScattingPointCash + 1) = Na;
	*(ScattingPointCash + 2) = Nr;
	*(ScattingPointCash + 3) = 0;

	memcpy(CFARData, ScattingPointCash, (ScattingPointNum + 1) * 4 * sizeof(double));
	CfarP->CFARDataOut = CFARData;  //将CFAR结果传到结构体指针


}

template <class T,class T2>
void ThreadClassActiveSigProc::EchoActiveSigProc(SimStruct *SimData, RadarParam *RParam, RADAR *ParamR, PLATFORM *RadarPos,
                                                 CFARParam *CfarP, AssemPointParam *APParam, ParamAngle *ParamA,
                                                 Complexf *SumCompPolar1, Complexf *AziCompPolar1, Complexf *EleCompPolar1,Complexf *ExCompPolar1,
                                                 Complexf *SumCompPolar2, Complexf *AziCompPolar2, Complexf *EleCompPolar2,Complexf *ExCompPolar2,
                                                 Complexf *SumMTD)
{
	cudaEvent_t e_start, e_stop;
	cudaEventCreate(&e_start);
	cudaEventCreate(&e_stop);
	cudaEventRecord(e_start, 0);

	cudaError_t Err = cudaGetLastError();

    int Nr = SimData->SaveFlie_Nr;
    int Na = SimData->SaveFlie_Na;

    //**********发射信号生成***********//
    int PRTNum = (int)(ParamR->Fs / ParamR->Prf + 0.5);	//一个prf的点数
    int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数
    int idx, idz, idn, idm, idy = 0;

    //MTD数据
    Complexf* dev_SumMTDPolar1   = (Complexf*)_MallocCUDA(sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    Complexf* dev_AziMTDPolar1   = (Complexf*)_MallocCUDA(sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    Complexf* dev_EleMTDPolar1   = (Complexf*)_MallocCUDA(sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    Complexf* dev_ExMTDPolar1    = (Complexf*)_MallocCUDA(sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    Complexf* dev_SumMTDPolar2   = (Complexf*)_MallocCUDA(sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    Complexf* dev_AziMTDPolar2   = (Complexf*)_MallocCUDA(sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    Complexf* dev_EleMTDPolar2   = (Complexf*)_MallocCUDA(sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    Complexf* dev_ExMTDPolar2    = (Complexf*)_MallocCUDA(sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    cudaMemset(dev_SumMTDPolar1, 0, sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    cudaMemset(dev_AziMTDPolar1, 0, sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    cudaMemset(dev_EleMTDPolar1, 0, sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    cudaMemset(dev_ExMTDPolar1,  0, sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    cudaMemset(dev_SumMTDPolar2, 0, sizeof(Complexf)*SimData->SaveFlie_Na*SimData->SaveFlie_Nr);
    cudaMemset(dev_AziMTDPolar2, 0, sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    cudaMemset(dev_EleMTDPolar2, 0, sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);
    cudaMemset(dev_ExMTDPolar2,  0, sizeof(Complexf)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na);

	//MTD
	EchoMTD(Nr, Na, SimData, RadarPos, ParamR, SumCompPolar1, dev_SumMTDPolar1);


	//规约求最值
	dim3 dimBlock(1024);
	dim3 dimGrid((Nr*Na/4 + dimBlock.x - 1) / dimBlock.x, 1);
	float *dev_MaxBuffer = (float*)_MallocCUDA(dimGrid.x*sizeof(float));
	CUDA_ReduceMaxValSmemUnroll4(0, dimBlock, dimGrid, (float*)dev_SumMTDPolar1, (float*)dev_MaxBuffer, Nr*Na);
	float *pMaxBuffer = (float*)_Malloc(dimGrid.x*sizeof(float));
	cudaMemcpy(pMaxBuffer, dev_MaxBuffer, sizeof(float)*dimGrid.x,cudaMemcpyDeviceToHost);
	float tempMax = pMaxBuffer[0];
	for (int i = 1; i < dimGrid.x; i++){
		if (tempMax < pMaxBuffer[i])
			tempMax = pMaxBuffer[i];
	}

	//幅度归一化
	dimGrid.x	= (Nr*Na + dimBlock.x - 1) / dimBlock.x;
	float Coef	= 1.0 / tempMax * 30000;
	Complexf *dev_SumMTD = (Complexf*)_MallocCUDA(Nr*Na*sizeof(Complexf));
	CUDA_ComplexMultCoef(0, dimBlock, dimGrid, Nr*Na, dev_SumMTDPolar1, dev_SumMTD, Coef);

	//*******************************CFAR-点凝聚-测距测速-测角-定位*******************************//
	//固定距离门模式下：CFAR-点凝聚-测距测速-测角-定位数据的内存分配
	double *CFARData = (double*)_Malloc(sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);
	memset(CFARData, 0, sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);
	double *FindMainLobeData = (double*)_Malloc(sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);
	memset(FindMainLobeData, 0, sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);
	double *SumAssembleData = (double*)_Malloc(sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);
	memset(SumAssembleData, 0, sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);
	double *TargetInfo = (double*)_Malloc(sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);
	memset(TargetInfo, 0, sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 2);
	double *TargetAngle = (double*)_Malloc(sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);
	memset(TargetAngle, 0, sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);
	double *PosInfo = (double*)_Malloc(sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);
	memset(PosInfo, 0, sizeof(double)*SimData->SaveFlie_Nr*SimData->SaveFlie_Na * 4 + 4);

	//CFAR






}
void ThreadClassActiveSigProc::TestTrans()
{
	cudaError_t Err = cudaGetLastError();
	float *pInPut = (float*)_Malloc(1024 * 1024 * sizeof(float));
	float *pInPutT = (float*)_Malloc(1024 * 1024 * sizeof(float));

	float *dev_InPut = (float*)_MallocCUDA(1024 * 1024 * sizeof(float));
	float *dev_InPutT = (float*)_MallocCUDA(1024 * 1024 * sizeof(float));

	for (int i = 0; i < 1024 * 1024; i++)
	{
		pInPut[i] = i % 1024;
	}

	cudaMemcpy(dev_InPut, pInPut, 1024 * 1024 * sizeof(float), cudaMemcpyHostToDevice);
	Err = cudaGetLastError();
	//转置扩展向量
	dim3 dimBlock3(TILE_DIM, BLOCK_ROWS);
	dim3 dimGrid3((1024 + TILE_DIM - 1) / TILE_DIM, (1024 + BLOCK_ROWS - 1) / BLOCK_ROWS);
	float *dev_SpreadCash = (float*)_MallocCUDA(1024 * 1024 * sizeof(float));

	cudaEvent_t e_start, e_stop;
	cudaEventCreate(&e_start);
	cudaEventCreate(&e_stop);
	cudaEventRecord(e_start, 0);

	for (int i = 0; i < 1024; i++)
	{
		//cudaMemcpy(dev_InPutT, dev_InPut, 1024 * 1024 * sizeof(float), cudaMemcpyDeviceToDevice);
		//CUDA_TransDiagReal(0, dimBlock3, dimGrid3, dev_InPutT, dev_InPut, 1024, 1024);
		CUDA_TransDiagRealPuSu(0, dimBlock3, dimGrid3, dev_InPutT, dev_InPut, 1024, 1024);
	}

	cudaEventRecord(e_stop, 0);
	cudaEventSynchronize(e_stop);

	float elapsedTime = 0;
	cudaEventElapsedTime(&elapsedTime, e_start, e_stop);
	qDebug() << "GPU:" << m_DevID << "============  CUDA Trans Cost Time is  " << (elapsedTime) << " ms";

	Err = cudaGetLastError();
	cudaMemcpy(pInPutT, dev_InPutT, 1024 * 1024 * sizeof(float), cudaMemcpyDeviceToHost);

	Err = cudaGetLastError();

	writeFileActive((char*)pInPut, 1024 * 1024 * sizeof(float), QString("M001"));
	writeFileActive((char*)pInPutT, 1024 * 1024 * sizeof(float), QString("M002"));
	return;
}
//
void ThreadClassActiveSigProc::slotActiveSigProc(void *p)
{
	cudaError_t Err = cudaGetLastError();
    cudaSetDevice(m_DevID);
    _Release_Malloc();      //初始化伪内存池
    _Release_MallocCUDA();

    RADAR       *ParamR     = m_ParamR;
    SimStruct   *SimData    = m_SimData;
    SARPARAM    *SARParam   = m_SARParam;

	stEchoData *pStEchoData = (stEchoData*)p;

	SimData->SaveFlie_Na = pStEchoData->childFrameNum;
	//发射信号生成
	Complexf *BaseSignal = (Complexf *)_Malloc(sizeof(Complexf) * ParamR->Nr);
	memset(BaseSignal, 0, sizeof(Complexf) * ParamR->Nr);
	int TrNum = (int)(ParamR->Fs*ParamR->Tp + 0.5);		//一个脉宽的点数
	ThreadClassSendWaveGen::Instance()->TransSignalSim(ParamR, SimData, TrNum, BaseSignal);

	Complexs *SarEchoData	= (Complexs*)pStEchoData->SumEchoDataPolar1;

    int             FollowFlag  = 0;            //跟踪标志位，等于0表示搜索或截获，等于1表示跟踪

    //EchoActiveSigProc(ParamR, SimData, SARParam, pStEchoData->PlatForm, BaseSignal, SarEchoData);



	_ReleaseMemObj(stEchoData, pStEchoData);
}
void ThreadClassActiveSigProc::slotEchoActiveSigProc(void *p)
{
	stEchoDataNuffer *pStEchoDataNuffer = (stEchoDataNuffer*)p;

	stEchoData *pStEchoData = (stEchoData*)_MallocMemObj(stEchoData);
	ExtractEchoData(pStEchoDataNuffer, pStEchoData);

	_ReleaseMemObj(stEchoDataNuffer, pStEchoDataNuffer);

    slotActiveSigProc(pStEchoData);
}
//参数初始化
void ThreadClassActiveSigProc::slotSettingActiveSigProc(void *p)
{
	cudaSetDevice(m_DevID);
    stWhitePara *pStWhitePara = (stWhitePara*)p;
    memcpy(m_ParamR,&pStWhitePara->RadarParam,sizeof(RADAR));
    memcpy(m_SimData,&pStWhitePara->SimData,sizeof(SimStruct));
	_ReleaseMemObj(stWhitePara, pStWhitePara);
	

	InitCUDAPara();

	return;
	
	char *pBuffer = (char*)_Malloc(32 * 1024 * 1024);
	ReadEchoDate(pBuffer);
	stEchoData *pStEchoData = (stEchoData*)_MallocMemObj(stEchoData);
	ExtractEchoData(pBuffer, pStEchoData);

    slotActiveSigProc(pStEchoData);

}
//解析回波数据
bool ThreadClassActiveSigProc::ReadEchoDate(void *p)
{
	QString strFileName = QString("Echo1028");
	QString strFilePath = QString("D:\\yening\\PRJ\\PR2401_063\\5_from_yening\\EchoInterfeCalcutePool\\doc\\%1").arg(strFileName);

	QFile *pQFile = new QFile;
	pQFile->setFileName(strFilePath);
	int ret = pQFile->open(QFile::ReadOnly);
	if (ret == 1)
	{
		pQFile->read((char*)p, pQFile->size());
		pQFile->close();
		return true;
	}
	return false;
}
void ThreadClassActiveSigProc::EchoCmp(char *pBufferDst, char *pBufferSrc, int &dot, int &offset)
{
	int Bytes = *(int*)(pBufferSrc + offset); offset += sizeof(int);//
	if (Bytes > 0)
	{
		memcpy(pBufferDst, pBufferSrc + offset, Bytes); offset += Bytes;
		dot += Bytes / sizeof(Complexs);
	}
	
}
bool ThreadClassActiveSigProc::ExtractEchoData(void *p, stEchoData *pStEchoData)
{
	char *pBuffer = (char*)p;

	int Size_MessageEnd = sizeof(MessageEnd);

	MessageHead *pMsgHead = (MessageHead*)pBuffer;	//报文的头部

	int BufferBytes = pMsgHead->uLen;
	int offset		= 0;

	switch ((DecodeType)pMsgHead->DeviceID)//区别当前报文的发送方
	{
	case DecodeType_WhiteCalcutePool: //白方计算池
	{
		offset += sizeof(MessageHead);//大帧头
		
		int cntPulse = 0;
		while ((BufferBytes - offset) > Size_MessageEnd)
		{
			offset += sizeof(MessageHead);//小帧帧头
			//运动平台参数
			memcpy(&pStEchoData->PlatForm[cntPulse], pBuffer + offset, sizeof(PLATFORM)); offset += sizeof(PLATFORM);

			EchoCmp((char*)&pStEchoData->SumEchoDataPolar1[pStEchoData->SumEchoData1Dot / 2], pBuffer, pStEchoData->SumEchoData1Dot, offset);//极化1
			EchoCmp((char*)&pStEchoData->SumEchoDataPolar2[pStEchoData->SumEchoData2Dot / 2], pBuffer, pStEchoData->SumEchoData2Dot, offset);//极化2

			EchoCmp((char*)&pStEchoData->AziEchoDataPolar1[pStEchoData->AziEchoData1Dot / 2], pBuffer, pStEchoData->AziEchoData1Dot, offset);//方位差1
			EchoCmp((char*)&pStEchoData->AziEchoDataPolar2[pStEchoData->AziEchoData2Dot / 2], pBuffer, pStEchoData->AziEchoData2Dot, offset);//方位差2

			EchoCmp((char*)&pStEchoData->EleEchoDataPolar1[pStEchoData->EleEchoData1Dot / 2], pBuffer, pStEchoData->EleEchoData1Dot, offset);//俯仰差1
			EchoCmp((char*)&pStEchoData->EleEchoDataPolar2[pStEchoData->EleEchoData2Dot / 2], pBuffer, pStEchoData->EleEchoData2Dot, offset);//俯仰差2

			EchoCmp((char*)&pStEchoData->ExEchoDataPolar1[pStEchoData->ExEchoData1Dot / 2], pBuffer, pStEchoData->ExEchoData1Dot, offset);//辅助1
			EchoCmp((char*)&pStEchoData->ExEchoDataPolar2[pStEchoData->ExEchoData2Dot / 2], pBuffer, pStEchoData->ExEchoData2Dot, offset);//辅助2

			cntPulse++;
			offset += sizeof(MessageEnd);//小帧帧尾
		}
		pStEchoData->childFrameNum = cntPulse;
		break;
	}
	default:
		break;
	}
	return true;
}

