/*******************************************************************************************
 * FileProperties: 
 *     FileName: RedGloable.h
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/inc/RedGloable.h $
 *         $Author: weiyingzhen $
 *         $Revision: 257 $
 *         $Date: 2025-02-27 17:47:56 $
*******************************************************************************************/
#ifndef REDGLOABLE
#define REDGLOABLE

#define _CRT_SECURE_NO_WARNINGS

/******************
*定义结构体和通用函数
*************/
#include "math.h"
#include "stdio.h"
#include "string.h"
#include "malloc.h"
#include <stdlib.h>
#include "time.h"

using namespace std;

#define QUANTIFY_LEN	32768					//量化到2^15
#ifndef PI
#define PI				3.141592653589793		//常数pi
#endif
#define SPEEDLIGHT		299792458.0
#define BUFFER_LEN		65536
#define k0				1.3806488e-23			//玻尔兹曼常数
#define MINEQUERR       1.0e-8					//允许的最小误差
#define MAXTRACKNUM     10						//最大同时跟踪目标数量，也是搜索模式下最大探测目标数量 （WS）

#define TREADSPERBLOCK	32
#define TARGET_NUM		8		//最大目标个数
#define RANGEGATE_NUM   8		//距离门最大个数
#define Fore_TarNum_Max 20		//预测最大目标个数

#define BIT8			8.0
#define BIT14           14.0
#define BIT16           16.0

//enum TriggerType	//触发器类型
//{
//	ConstantPRF = 0,
//	StaggeredPRF,
//	JitteringPRF,
//	SlipPRF
//};//恒定重频、重频参差、重频抖动、重频滑动

////指标要求0 1 2 4 5 9
//enum SignalType
//{
//	Pulse = 0,
//	Chirp,
//	NChirp,
//	FSK,
//	BPSK,
//	QPSK,
//	SinCos
//};//0常规脉冲信号 1线性调频信号 2非线性调频信号 3FSK 4BPSK 5QPSK 6单频连续波

//typedef struct
//{
//	int RcsType;	//斯威林模型,0无起伏，1-4分别为斯威林1-3型
//	double Sigma0;	//平均RCS
//	double SigmaRe;	//RCS实部
//	double SigmaIm;	//RCS虚部
//}RCS;	//RCS类型

struct Output
{
	char *OutInfo;
	int  MsgCont;
	struct Output *pNext;
};	//输出信息链表结构体


enum TargetMoveType	//目标运动类型
{
	Static = 0,
	RadialMove,
	ConicalMove,
	LoadFile
};//静止，径向靠近/径向远离，圆锥运动/逆时针圆锥运动，加载文件

////--------平台参数----------//
//typedef struct
//{
//	int MoveType;		//0.界面输入，1.加载模式
//	//初始时刻位置-北天东坐标系系
//	double NorthPos;	//北向位置
//	double SkyPos;		//天向位置
//	double EastPos;		//东向位置

//	double NorthVel;	//北向速度
//	double SkyVel;		//天向速度
//	double EastVel;		//东向速度

//	double NorthAcc;	//北向加速度
//	double SkyAcc;		//天向加速度
//	double EastAcc;		//东向加速度
//}PLATFORM;


enum CFARDirection  //cfar维度
{
	RANGEONLY = 1,
	AZIMUTHONLY,
	TWODIMESION,
};

enum CFARDetectorLaw
{
	AMPLITUDELAW = 0, //平方率建波
	SQUARELAW,  // 幅度建波
};

enum CFARType  //CFAR类型
{
	CACFAR = 1,
	GOCFAR,
	SOCFAR,
};

typedef struct
{
	int    DetectorLaw;      // 做平方率检波还是幅度检波
	int    Direction;        // CFAR沿距离维还是多普勒维
	int    Type;             // CFAR类型 ( CA  GO  SO )
	int    MaxDetectNum;     // 最大检测数量
	double FirstThr;         // 第一门限
	int    RanProtLen;       // 距离向保护单元长度
	int    RanRefLen;        // 距离向参考单元长度
	double RanThr;           // 距离向第二门限
	float  PFA;				 // 虚警概率
	int    AziProtLen;       // 方位向保护单元长度
	int    AziRefLen;        // 方位向参考单元长度
	double AziThr;           // 方位向第二门限
	int    RanSpreadLen;     // 距离向扩展向量长度,Nr + 2*(CfarP->RanProtLen+CfarP->RanRefLen)
	int    AziSpreadLen;     // 方位向扩展向量长度,Na + 2*(CfarP->AziProtLen+CfarP->AziRefLen)

	float *CFARDataOut;     // CFAR输出数据结果
} CFARParam;

typedef struct
{
	int MinScatterNum;  // 最小聚类要求数目
	int MaxDetectNum;   // 最大可凝聚点的数目 
	int RanDistance;   // 做凝聚时，散射点关联的距离通道号
	int AziDistance;   // 做凝聚时，散射点关联的多普勒通道号

	float *AssembleData;  //点凝聚的输出数据
} AssemPointParam;

typedef struct
{
	double   PRF;       // 重频
	double   Fc;        // 载频
	double   Fs;        // 采样率
	double   Rmin;      // 最短距离
	double   Rmax;      // 最远距离
	double   DeltaR;    // 距离分辨率
	double   Lambda;    // 载波波长
	double   Vmu;       // 最大不模糊速度
} RadarParam;

typedef struct 
{
	int TarNum;
	double RangeResult[Fore_TarNum_Max];	//测距结果
	double VelResult[Fore_TarNum_Max];		//测速结果
	double AziResult[Fore_TarNum_Max];      //方位角测量结果
	double EleResult[Fore_TarNum_Max];      //俯仰角测量结果

	double RangeReal;						//实际距离
	double VelReal;							//实际速度
	double AziReal;							//实际方位角
	double EleReal;							//实际俯仰角


	double RangeDiff[Fore_TarNum_Max];		//测距误差
	double VelDiff[Fore_TarNum_Max];		//测速误差
	double AziDiff[Fore_TarNum_Max];		//方位角误差
	double EleDiff[Fore_TarNum_Max];		//俯仰角误差

}TarPara;

enum CoordinateArgc
{
	THREE = 0,    //3坐标
	TWO,		  //2坐标
};


enum AlgorithmArgc
{
	PHASE_COMPARISION = 1,   //比相
	AMPLITUDE_COMPARISION,	 //比幅
};


typedef struct
{
	int Coordinate_Argc;  //坐标系（3坐标还是2坐标）
	int Algorithm_Argc;   //算法选择（比相or比幅）
	//    double D_ARGC;           //天线阵元间距（俯仰+方位）
	double AntSpaceRow;      //行阵元间距（方位）
	double AntSpaceColumn;   //列阵元间距（俯仰）
	double AziSlope;		 //和差差天线 - 方位方向差斜率(比幅)
	double EleSlope;		 //和差差天线 - 俯仰方向差斜率(比幅)
	double Fc;			     //载频
	double Azimuth;			//波束指向方位
	double Pitch;			//波束指向俯仰

	double *AngleResult;       //角度测量结果
}ParamAngle;

//--------SAR成像参数----------//
typedef struct
{
    int PixelNorth;		//成像场景北向点数
    int PixelEast;		//成像场景东向点数
    double NorthSpace;		//北向分辨率
    double EastSpace;		//东向分辨率
    int OverSample;			//升采样倍数
    double NorthOffSet;		//波足北向坐标
    double SkyOffSet;		//波足天向坐标（通常为0）
    double EastOffSet;		//波足东向坐标
}SARPARAM;
//SAR
typedef struct _stSarImag
{
    int     width;
    int     hight;
    float   MaxAmp;
    float   MinAmp;
    unsigned short   Imag[2048 * 2048];
}stSarImag;

typedef struct
{

	int N;				  //总采样次数
	double rangeAssocThresh; //距离跟踪门限
	//double **KalmanFilterData;//卡尔曼滤波输出结果
	double *KalmanFilterData;//卡尔曼滤波输出结果，size：行：3*目标数；列：卡尔曼滤波次数。一列中3个数一组结果，分别表示距离，速度，加速度的估计值

} KalmanFilterParam;

////每个目标的初始跟踪状态
typedef struct
{
	int maxTargets;  //最大同时跟踪目标数量
	int targetsNum; //当前正在跟踪的目标数
	int *targetsIDs;  //每个数组下标位置上对应的ID，没有的位置填0，数组长度固定
	int TargetID_gen;  //用于产生目标ID的计数器
}TrackingStates;

//跟踪系统状态的结构体（当前时刻下）
typedef struct
{
	double **X;			//数据融合后的状态值
	double **X1;		//数据融合前的状态值
	double **P;			//数据融合后协方差矩阵
	double **P1;
	double **H1;
	int used;			//观测值能否关联上滤波值
	int linked;			//滤波值能否关联上观测值
	int X_time;
	int X1_time;
	int *MeasHistory;	//记录每一次关联结果，共10位，后一位表示此次关联结果（1关联，0没关联）
	int TargetID;
	double **meas1;
}targets;

//enum AntennaType
//{
//	Single = 0,
//	Line,
//	RectAngle,
//	CircleBound,
//	Hexagon,
//	Circle,
//	ConcentircCircle,
//	ArbitraryArray
//};//线阵天线,矩形阵列、圆周边界阵列、六边形阵列、圆周阵列、同心圆阵列、任意加阵

//enum SubAntType
//{
//	ArryElement = 0,
//	SubArray
//};//阵元和子阵

typedef struct
{
	double *LocationData;  //定位的输出数据
} LocationParam;



#endif







