/*******************************************************************************************
 * FileProperties: 
 *     FileName: ThreadClassJFEcho.h
 *     SvnProperties: 
 *         $URL: http://192.168.0.155/svn/HQP2442/DEVELOP/P1/5_Software/52_Client/CalcutePool/src/EchoInterfeCalcutePool_Com/inc/Thread/ThreadClassJFEcho.h $
 *         $Author: yening $
 *         $Revision: 111 $
 *         $Date: 2025-01-15 10:37:52 $
*******************************************************************************************/
#ifndef THREADCLASSJFECHO_H
#define THREADCLASSJFECHO_H

#include <QObject>

#include<curand.h>
#include<cufft.h>

#include"MyMalloc.h"
#include"MyMallocCUDA.h"
#include"_stumempool.h"
#include"ClassRcsRead.h"
#include"ClassCoorDinate.h"

#include "WhiteCalGlobel.h"



class ThreadClassJFEcho : public QObject, MyMalloc, MyMallocCUDA, ClassRcsRead
{
    Q_OBJECT
public:
    explicit ThreadClassJFEcho(int DevID = -1,int buffer_size = 0,char *d_Buffer = nullptr,QObject *parent = 0);
    ~ThreadClassJFEcho();

	void GenSenceEcho(void *p, stEchoData *pStEchoData);
	//加载目标RCS系数
	void InitTarRcsPara(void *p);
    
public:
    //初始化GPU设备工作参数
	bool InitDeviceWorkPara(int DevID, long long buffer_size, char *d_Buffer, char* d_BufferPublic);
	//提取角反模型散射系数
	void ExtractCorRefScatterCoef(Target *pTarget, PLATFORM *pPlatForm, int TarNum);

signals:


public slots:

    
private:
    int m_DevID;
	char *d_BufferFather, *d_BufferInit;
	long long BufferInit_SizeTotal, BufferInitTarRcs_Size,BufferInit_Cur;

    float *dev_Target_ALL1, *dev_Target_ALL2, *dev_Target_ALL3, *dev_Target_ALL4, *dev_Target_ALL5;

	cudaEvent_t e_start, e_stop, e_start2, e_stop2;
	
	stTarLink *m_StTarLink;
	char				*m_d_BufferTarRCS, *m_pBufferTarRCS;
	unsigned long long	m_BytesOffsetTarRCS;
    ClassCoorDinate *pClassCoorDinate;
	unsigned int m_SacreNum;
};

#endif // THREADCLASSJFECHO_H
